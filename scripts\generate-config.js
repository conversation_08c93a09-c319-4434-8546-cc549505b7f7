// <PERSON>ript to generate runtime configuration from environment variables
const fs = require('fs');
const path = require('path');
require('dotenv').config(); // Load .env file if present

// Default values (REPLACE THESE WITH PLACEHOLDERS IN PRODUCTION CODE)
const defaultConfig = {
  auth0: {
    domain: 'dev-example.us.auth0.com',
    clientId: 'your-client-id-placeholder',
  },
  xano: {
    baseURL: 'https://example.xano.io/api:example',
  }
};

// Get environment variables with fallbacks to default values
const config = {
  auth0: {
    domain: process.env.AUTH0_DOMAIN || defaultConfig.auth0.domain,
    clientId: process.env.AUTH0_CLIENT_ID || defaultConfig.auth0.clientId,
  },
  xano: {
    baseURL: process.env.XANO_BASE_URL || defaultConfig.xano.baseURL,
  }
};

// Create the runtime config file
const runtimeConfig = `// GENERATED FILE - DO NOT EDIT DIRECTLY
// This file is auto-generated during the build process

window.APP_CONFIG = ${JSON.stringify(config, null, 2)};
`;

// Determine output path based on environment
const isDevelopment = process.env.NODE_ENV === 'development';
const outputPath = isDevelopment 
  ? path.resolve(__dirname, '../runtime-config.js')
  : path.resolve(__dirname, '../dist/runtime-config.js');

// Ensure the directory exists
const outputDir = path.dirname(outputPath);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Write the file
fs.writeFileSync(outputPath, runtimeConfig);

console.log(`Runtime configuration generated at: ${outputPath}`);

// If in development mode, also create a .env.example file if it doesn't exist
if (isDevelopment && !fs.existsSync(path.resolve(__dirname, '../.env.example'))) {
  const envExample = `# Environment Variables Example
# Copy this file to .env for local development

# Auth0 Configuration
AUTH0_DOMAIN=dev-example.us.auth0.com
AUTH0_CLIENT_ID=your-client-id-here

# Xano Configuration
XANO_BASE_URL=https://example.xano.io/api:example
`;

  fs.writeFileSync(path.resolve(__dirname, '../.env.example'), envExample);
  console.log('.env.example file created');
}