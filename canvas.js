       // DOM Element References
        const toolbar = document.getElementById('toolbar');
        const gridSizeSelector = document.getElementById('gridSize');
        const colorPickerInput = document.getElementById('color-picker');
        const hexInput = document.getElementById('hexInput');
        const toolSelector = document.getElementById('tool');
        const thicknessInput = document.getElementById('thickness');
        const saveBtn = document.getElementById('saveBtn');
        const filenameInput = document.getElementById('filename');
        const gridStyleSelector = document.getElementById('gridStyle');
        const checkerboardToggle = document.getElementById('checkerboardToggle');
        const mobileUndoBtn = document.getElementById('mobileUndoBtn');
        const mobileRedoBtn = document.getElementById('mobileRedoBtn');
        const mobileCopyBtn = document.getElementById('mobileCopyBtn');
        const mobilePasteBtn = document.getElementById('mobilePasteBtn');
        const mobileDeleteSelectionBtn = document.getElementById('mobileDeleteSelectionBtn');
        const zoomSlider = document.getElementById('zoomSlider');
        const zoomSliderContainer = document.getElementById('zoomSliderContainer');
        const pixelCanvas = document.getElementById('pixelCanvas');
        const gridCanvas = document.getElementById('gridCanvas');
        const pixelCtx = pixelCanvas.getContext('2d');
        const gridCtx = gridCanvas.getContext('2d');
        const editorContainer = document.getElementById('editorContainer');
        const brushOptionsBar = document.getElementById('brushOptionsBar');
        const fillOptionsContainer = document.getElementById('fillOptionsContainer');
        const canvasArea = document.getElementById('canvasArea');
        const canvasTabsBar = document.getElementById('canvasTabsBar');
        const addNewCanvasBtn = document.getElementById('addNewCanvasBtn');
        const canvasContainer = document.getElementById('canvasContainer');
        const rightPanels = document.getElementById('rightPanels');
        const layersPanelContainer = document.getElementById('layersPanelContainer');
        const newLayerBtn = document.getElementById('newLayerBtn');
        const duplicateLayerBtn = document.getElementById('duplicateLayerBtn');
        const mergeLayerDownBtn = document.getElementById('mergeLayerDownBtn');
        const layersList = document.getElementById('layersList');
        const layerCountSpan = document.getElementById('layerCount');

        const commonBrushOptionsContainer = document.getElementById('commonBrushOptionsContainer');
        const hatchingBrushOptionsContainer = document.getElementById('hatchingBrushOptionsContainer');
        const hatchScaleContainer = document.getElementById('hatchScaleContainer');
        const hatchScaleSlider = document.getElementById('hatchScaleSlider');
        const hatchScaleValueSpan = document.getElementById('hatchScaleValue');
        const sprayBrushOptionsContainer = document.getElementById('sprayBrushOptionsContainer');
        const sprayRadiusContainer = document.getElementById('sprayRadiusContainer');
        const sprayRadiusSlider = document.getElementById('sprayRadiusSlider');
        const sprayRadiusValueSpan = document.getElementById('sprayRadiusValue');

        const brushOpacityContainer = document.getElementById('brushOpacityContainer');
        const brushOpacityInput = document.getElementById('brushOpacityInput');
        const brushOpacitySlider = document.getElementById('brushOpacitySlider');

        const fillOpacityInput = document.getElementById('fillOpacityInput');
        const fillOpacitySlider = document.getElementById('fillOpacitySlider');

        // State Variables
        const EMPTY_COLOR = null; // Represents a transparent pixel
        const MAX_LAYERS = 20;
        const LAYER_NAME_MAX_LENGTH = 50;
        const CANVAS_NAME_MAX_LENGTH = LAYER_NAME_MAX_LENGTH;
        const MAX_CANVASES = 3;
        
        // Security limits for undo/redo history
        const MAX_UNDO_HISTORY = 50; // Maximum number of undo states to keep
        const MAX_UNDO_MEMORY_MB = 50; // Maximum memory usage for undo history in MB
        let currentUndoMemoryUsage = 0; // Track current memory usage
        // ... existing DOM element references ...
        // ... (existing DOM element references)
        // ...
        const openSizeModalBtn = document.getElementById('openSizeModalBtn');
        const sizeModal = document.getElementById('sizeModal');
        const sizeOptionsContainer = document.getElementById('sizeOptionsContainer');
        const modalCloseBtn = sizeModal.querySelector('.modal-close-btn');
        const cancelSizeChangeBtn = document.getElementById('cancelSizeChangeBtn');
        const confirmSizeChangeBtn = document.getElementById('confirmSizeChangeBtn');

        let tempSelectedSizeValue = null; // To store selection within the modal

        // === Tool Options Containers ===
        const selectOptionsContainer = document.getElementById('selectOptionsContainer');
        const panOptionsContainer = document.getElementById('panOptionsContainer');
        const eyedropperOptionsContainer = document.getElementById('eyedropperOptionsContainer');
        const eraseOptionsContainer = document.getElementById('eraseOptionsContainer');

        const COPY_OFFSET_X = 4; // Grid cells to offset copied selection horizontally
        const COPY_OFFSET_Y = 4; // Grid cells to offset copied selection vertically
        const flipHorizontalBtn = document.getElementById('flipHorizontalBtn');
        const flipVerticalBtn = document.getElementById('flipVerticalBtn');
        const MIN_SELECTION_DIM = 1; // Minimum width/height for a selection during resize
        const scaleUpSelectionBtn = document.getElementById('scaleUpSelectionBtn');
        const scaleDownSelectionBtn = document.getElementById('scaleDownSelectionBtn');
        const rotate90SelectionBtn = document.getElementById('rotate90SelectionBtn');
        const rotate180SelectionBtn = document.getElementById('rotate180SelectionBtn');
        // MINIMAP START
        let minimapCanvasElement;
        let minimapCtx;
        const MINIMAP_MAX_SIZE = 128; // Max width/height for the minimap in pixels
        let tempMinimapCompositeCanvas = null; // For compositing layers before drawing to minimap
        let tempMinimapCompositeCtx = null;
        // ... existing state variables ...
        let draggedLayerElement = null;
        let draggedLayerOriginalIndex = -1;
        let isLayerDragging = false;
        let currentDropTargetFinalIndex = -1; // The final index the layer would have in currentLayers
        let dropIndicatorElements = []; // To store references to the indicator divs
        // ... (existing modal DOM references)
        const currentCanvasPreview = document.getElementById('currentCanvasPreview');
        const newCanvasPreview = document.getElementById('newCanvasPreview');
        const currentCanvasPreviewCtx = currentCanvasPreview.getContext('2d');
        const newCanvasPreviewCtx = newCanvasPreview.getContext('2d');
        currentCanvasPreviewCtx.imageSmoothingEnabled = false;
        newCanvasPreviewCtx.imageSmoothingEnabled = false;

        const currentSizeLabel = document.getElementById('currentSizeLabel');
        const newSizeLabel = document.getElementById('newSizeLabel');

        const PREVIEW_BOX_SIZE = 120; // Display size in pixels for the preview boxes

        // === Selection Tool State ===
        let isMovingSelection = false;        // True if currently dragging a selection to move it
        let movingSelectionBuffer = null;     // Stores pixel data of the selection being moved
                                              // Format: array of rows, where each row is an array of color strings/EMPTY_COLOR
        let movingSelectionDelta = null;      // {dx, dy} offset from top-left of selection to mouse down point during move
        let movingSelectionCurrentTopLeft = null; // {x, y} current top-left of the moving selection preview on the grid
        let originalSelectionBeforeMove = null; // {start: {x,y}, end: {x,y}} to restore selection box if move is cancelled (future)
        let currentSelectionPasteMode = 'PASTE_MODE_REPLACE_ALL'; // Default mode

        // === Selection Resize/Scale State ===
        let isResizingSelection = false;      // True if currently dragging a resize handle
        let resizeHandleType = null;          // Type of handle being dragged: 'nw', 'ne', 'sw', 'se', 'n', 'e', 's', 'w'
        let originalSelectionForResize = null; // Original selection bounds before resize
        let resizeSelectionBuffer = null;     // Stores original pixel data for resize operations
        let resizeAnchorPoint = null;         // Fixed point during resize (opposite corner/edge)

        // Mirroring State
        let isVerticalMirrorActive = false;
        let isHorizontalMirrorActive = false;
        let isRadialMirrorActive = false;
        let radialMirrorAxes = 3;
        let mirrorCenterX = null;
        let mirrorCenterY = null;


        // MINIMAP END
        // ... (existing state variables) ...
        let layerDragHoldTimeout = null; // To store the setTimeout ID for the drag delay
        let isPotentialLayerDrag = false; // True when mousedown occurs, waiting for hold or mouseup
        let layerMouseDownPos = { x: 0, y: 0 }; // To check if mouse moved significantly during potential drag
        const DRAG_INITIATION_THRESHOLD = 5; // Pixels mouse can move before hold is cancelled
        const DRAG_HOLD_DURATION = 250; // Milliseconds to hold before drag starts

        // GLOW BRUSH UI ELEMENTS START
        const glowBrushOptionsContainer = document.getElementById('glowBrushOptionsContainer');
        const glowSizeSlider = document.getElementById('glowSizeSlider');
        const glowSizeValueSpan = document.getElementById('glowSizeValue');

        // Mirror UI Elements
        // User indicated their fix for swapped V/H mirror was to reassign these JS variables.
        // So, jsVarForVerticalFunctionality will point to the HTML element intended for vertical mirror (likely id="verticalMirrorToggle")
        // and its state will control isVerticalMirrorActive.
        // To achieve the user's "swapped functionality fix", the event listeners will map accordingly.
        const verticalMirrorToggleElem = document.getElementById('verticalMirrorToggle');    // HTML element with id="verticalMirrorToggle"
        const horizontalMirrorToggleElem = document.getElementById('horizontalMirrorToggle'); // HTML element with id="horizontalMirrorToggle"
        const radialMirrorToggle = document.getElementById('radialMirrorToggle');
        const mirrorOptionsContainer = document.getElementById('mirrorOptionsContainer'); // Added for show/hide
        const radialMirrorSettings = document.getElementById('radialMirrorSettings');
        const radialAxesSlider = document.getElementById('radialAxesSlider');
        const radialAxesValue = document.getElementById('radialAxesValue');
        // GLOW BRUSH UI ELEMENTS END
        let currentGlowSize = 5; // Default glow size
        let currentPathPoints = []; // For Taper Brush
        let clipboardBuffer = null;
        let clipboardBufferDimensions = { width: 0, height: 0 };

        let canvases = [];
        let activeCanvasIndex = -1;
        let nextCanvasId = 0;
        let nextLayerIdGlobal = 0;

        let currentGridSize = 64;
        let currentLayers = [];
        let currentActiveLayerIndex = 0;
        let activeLayer = null; // Will be set in switchActiveCanvas and other places to point to currentLayers[currentActiveLayerIndex]

        let currentUndoStack = [];
        let currentRedoStack = [];
        let currentViewOffsetX = 0;
        let currentViewOffsetY = 0;
        let currentZoomFactor = 1;
        let currentSelectionStart = null;
        let currentSelectionEnd = null;

        let drawing = false;
        let isSelecting = false;
        let startCell = null;

        let currentStrokeBuffer = null;
        let layerDataBeforeStroke = null;
        let isInitialPasteFloat = false; // True if the current moving selection is a fresh paste, centering on cursor

        let minFitCellSize = 1;
        let renderedCellSize = 1;
        let isPanning = false;
        let panStartMouse = { x: 0, y: 0 };
        let panStartViewOffset = { x: 0, y: 0 };

        let needsFullRedraw = true;
        let isCanvasDirtyForTools = false;
        let dirtyRect = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };

        // Performance debugging counters
        let fullRedrawCount = 0;
        let dirtyRedrawCount = 0;

        // Warning state tracking
        let storageWarningDismissed = false;
        let quotaErrorDismissed = false;

        let previousDrawingTool = 'draw';
        let currentSelectedColor = "#1EC7FA";
        let isRightButtonPanning = false;
        let isAutoPanKeyPressed = false;
        let currentBrushType = 'normal';
        let currentFillType = 'normal_fill';
        let currentHatchScale = 1;
        let currentSprayRadius = 10;

        let currentBrushOpacity = 100;
        let currentFillOpacity = 100;

        // Local Storage Configuration
        const LOCAL_STORAGE_KEY = 'pixelArtNexus_canvases';
        const LOCAL_STORAGE_SETTINGS_KEY = 'pixelArtNexus_settings';
        let saveTimeout = null;
        const SAVE_DEBOUNCE_DELAY = 500; // milliseconds

        pixelCtx.imageSmoothingEnabled = false;
        gridCtx.imageSmoothingEnabled = false;

        // --- Local Storage Functions ---
        async function saveCanvasesToLocalStorage() {
            try {
                if (!window.localStorage) {
                    console.warn('localStorage not available');
                    return false;
                }

                // Prepare canvas data for storage
                const canvasData = {
                    canvases: canvases.map(canvas => ({
                        id: canvas.id,
                        name: canvas.name,
                        gridSize: canvas.gridSize,
                        layers: canvas.layers.map(layer => ({
                            id: layer.id,
                            name: layer.name,
                            pixels: layer.pixels,
                            isVisible: layer.isVisible,
                            opacity: layer.opacity
                        })),
                        activeLayerIndex: canvas.activeLayerIndex,
                        viewOffsetX: canvas.viewOffsetX,
                        viewOffsetY: canvas.viewOffsetY,
                        zoomFactor: canvas.zoomFactor,
                        filename: canvas.filename,
                        selectionStart: canvas.selectionStart,
                        selectionEnd: canvas.selectionEnd,
                        isSelecting: canvas.isSelecting
                        // Note: undoStack and redoStack are not saved to reduce storage size
                    })),
                    activeCanvasIndex: activeCanvasIndex,
                    nextCanvasId: nextCanvasId,
                    nextLayerIdGlobal: nextLayerIdGlobal,
                    timestamp: Date.now()
                };

                const canvasDataString = JSON.stringify(canvasData);
                const dataSizeKB = Math.round(canvasDataString.length / 1024);
                const dataSizeMB = (dataSizeKB / 1024).toFixed(2);

                // Try secure storage first if available
                if (typeof SecurityUtils !== 'undefined' && SecurityUtils.SecureStorage) {
                    try {
                        const result = await SecurityUtils.SecureStorage.saveToStorage(LOCAL_STORAGE_KEY, canvasData);
                        console.log(`Canvases saved securely (${result.size}KB)${result.fallback ? ' via ' + result.fallback : ''}`);
                        
                        // Clear IndexedDB if localStorage worked (prefer localStorage for speed)
                        if (canvasDB && !result.fallback) {
                            clearCanvasFromIndexedDB();
                        }
                        return true;
                    } catch (secureStorageError) {
                        console.warn('Secure storage failed, trying fallback:', secureStorageError);
                        // Continue to fallback methods
                    }
                }
                
                // Try localStorage as fallback
                try {
                    localStorage.setItem(LOCAL_STORAGE_KEY, canvasDataString);
                    console.log(`Canvases saved to localStorage (${dataSizeKB}KB)`);

                    // Clear IndexedDB if localStorage worked (prefer localStorage for speed)
                    if (canvasDB) {
                        clearCanvasFromIndexedDB();
                    }
                    return true;
                } catch (localStorageError) {
                    console.warn(`localStorage failed (${dataSizeKB}KB), trying IndexedDB...`);

                    // Try IndexedDB as fallback
                    if (canvasDB) {
                        const saved = await saveCanvasToIndexedDB(canvasData);
                        if (saved) {
                            // Clear localStorage to make space
                            try {
                                localStorage.removeItem(LOCAL_STORAGE_KEY);
                            } catch (e) {}
                            console.log(`Canvases saved to IndexedDB (${dataSizeMB}MB)`);
                            return true;
                        }
                    }

                    // Both storage methods failed - show error
                    throw localStorageError;
                }
            } catch (error) {
                console.error('Failed to save canvases:', error);

                // Show user-friendly error notification
                if (!quotaErrorDismissed && !document.getElementById('quotaErrorShown')) {
                    const errorDiv = document.createElement('div');
                    errorDiv.id = 'quotaErrorShown';
                    errorDiv.style.cssText = `
                        position: fixed; top: 280px; right: 20px; z-index: 10000;
                        background: #dc3545; color: white; padding: 15px; border-radius: 8px;
                        max-width: 350px; font-size: 14px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    `;
                    errorDiv.innerHTML = `
                        <strong>💾 Save Failed - Storage Full</strong><br>
                        Canvas is too large for automatic saving.<br>
                        <strong>⚠️ Refreshing the page will lose your progress!</strong><br>
                        <small>Export to file or use smaller canvas size.</small>
                        <button onclick="quotaErrorDismissed = true; this.parentElement.remove()" style="float: right; margin-left: 10px; background: none; border: 1px solid white; color: white; padding: 2px 6px; border-radius: 3px; cursor: pointer;">×</button>
                    `;
                    document.body.appendChild(errorDiv);
                    setTimeout(() => {
                        if (errorDiv.parentNode) {
                            quotaErrorDismissed = true;
                            errorDiv.remove();
                        }
                    }, 20000);
                }
                return false;
            }
        }

        function loadCanvasesFromLocalStorage() {
            try {
                if (!window.localStorage) {
                    console.warn('localStorage not available');
                    return false;
                }

                const storedData = localStorage.getItem(LOCAL_STORAGE_KEY);
                if (!storedData) {
                    console.log('No saved canvases found in localStorage');
                    return false;
                }

                const canvasData = JSON.parse(storedData);

                // Validate the data structure
                if (!canvasData.canvases || !Array.isArray(canvasData.canvases)) {
                    console.warn('Invalid canvas data structure in localStorage');
                    return false;
                }

                // Restore global state
                nextCanvasId = canvasData.nextCanvasId || 0;
                nextLayerIdGlobal = canvasData.nextLayerIdGlobal || 0;

                // Restore canvases
                canvases = canvasData.canvases.map(canvasState => ({
                    ...canvasState,
                    undoStack: [], // Reset undo/redo stacks
                    redoStack: []
                }));

                // Restore active canvas index
                const savedActiveIndex = canvasData.activeCanvasIndex;
                if (savedActiveIndex >= 0 && savedActiveIndex < canvases.length) {
                    switchActiveCanvas(savedActiveIndex);
                } else if (canvases.length > 0) {
                    switchActiveCanvas(0);
                }

                console.log(`Loaded ${canvases.length} canvases from localStorage`);
                return true;
            } catch (error) {
                console.error('Failed to load canvases from localStorage:', error);
                return false;
            }
        }

        function removeCanvasFromLocalStorage(canvasId) {
            try {
                if (!window.localStorage) return;

                const storedData = localStorage.getItem(LOCAL_STORAGE_KEY);
                if (!storedData) return;

                const canvasData = JSON.parse(storedData);
                if (canvasData.canvases) {
                    canvasData.canvases = canvasData.canvases.filter(canvas => canvas.id !== canvasId);
                    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(canvasData));
                    console.log(`Canvas ${canvasId} removed from localStorage`);
                }
            } catch (error) {
                console.error('Failed to remove canvas from localStorage:', error);
            }
        }

        function clearAllCanvasesFromLocalStorage() {
            try {
                if (!window.localStorage) return;
                localStorage.removeItem(LOCAL_STORAGE_KEY);
                console.log('All canvas data cleared from localStorage');
            } catch (error) {
                console.error('Failed to clear canvas data from localStorage:', error);
            }
        }

        function debouncedSaveToLocalStorage() {
            if (saveTimeout) {
                clearTimeout(saveTimeout);
            }
            saveTimeout = setTimeout(async () => {
                await saveCanvasesToLocalStorage();
                saveTimeout = null;
            }, SAVE_DEBOUNCE_DELAY);
        }

        // --- Utility Functions ---
        // --- Modal Control Functions ---
        const availableSizes = [
            { value: "16", text: "16x16" },
            { value: "32", text: "32x32" },
            { value: "64", text: "64x64" },
            { value: "128", text: "128x128" },
            { value: "256", text: "256x256" },
            { value: "512", text: "512x512" },
            { value: "1024", text: "1024x1024" }
        ];

        function populateSizeOptions() {
            sizeOptionsContainer.innerHTML = ''; // Clear existing options
            let initialNewSizeForPreview = String(currentGridSize); // Default to current

            availableSizes.forEach(size => {
                const label = document.createElement('label');
                label.className = 'size-option';

                const radio = document.createElement('input');
                radio.type = 'radio';
                radio.name = 'newGridSize';
                radio.value = size.value;

                if (size.value === String(currentGridSize)) {
                    radio.checked = true;
                    tempSelectedSizeValue = size.value;
                    initialNewSizeForPreview = size.value;
                    // CORRECTED LABEL (also in event listener below)
                    if (newSizeLabel) newSizeLabel.textContent = `${size.value}x${size.value}`;
                }

                radio.addEventListener('change', (e) => {
                    tempSelectedSizeValue = e.target.value;
                    // CORRECTED LABEL
                    if (newSizeLabel) newSizeLabel.textContent = `${e.target.value}x${e.target.value}`;

                    const activeC = canvases[activeCanvasIndex];
                    const newDimVal = parseInt(tempSelectedSizeValue);



                    if (activeC) {
                        const previewDataForNewSize = createPreviewContentForNewSize(activeC, newDimVal);
                        drawOnPreviewCanvas(newCanvasPreviewCtx, newCanvasPreview, previewDataForNewSize, { width: newDimVal, height: newDimVal }, false);
                    } else {
                        // Fallback: if no active canvas, draw a blank placeholder for the new size.
                        drawOnPreviewCanvas(newCanvasPreviewCtx, newCanvasPreview, null, {width: newDimVal, height: newDimVal}, true);
                    }
                });

                const span = document.createElement('span');
                span.textContent = size.text;

                label.appendChild(radio);
                label.appendChild(span);
                sizeOptionsContainer.appendChild(label);
            });
            return initialNewSizeForPreview;
        }

        function openSizeModal() {
            const initialNewSize = populateSizeOptions();
            sizeModal.style.display = 'flex';

            const activeC = canvases[activeCanvasIndex];

            // Draw Current Canvas Preview
            if (activeC) {
                // CORRECTED LABEL
                if (currentSizeLabel) currentSizeLabel.textContent = `${activeC.gridSize}x${activeC.gridSize}`;
                drawOnPreviewCanvas(currentCanvasPreviewCtx, currentCanvasPreview, activeC, {width: activeC.gridSize, height: activeC.gridSize}, false);
            } else {
                 if (currentSizeLabel) currentSizeLabel.textContent = `N/A`;
                 drawOnPreviewCanvas(currentCanvasPreviewCtx, currentCanvasPreview, null, {width: 64, height: 64}, true);
            }

            // Draw Initial New Canvas Preview
            const initialNewDimVal = parseInt(initialNewSize);
            // CORRECTED LABEL (ensure it's set based on initialNewSize)
            if (newSizeLabel) {
                newSizeLabel.textContent = `${initialNewDimVal}x${initialNewDimVal}`;
            }

            if (activeC) {
                const previewDataForInitialNewSize = createPreviewContentForNewSize(activeC, initialNewDimVal);
                drawOnPreviewCanvas(newCanvasPreviewCtx, newCanvasPreview, previewDataForInitialNewSize, { width: initialNewDimVal, height: initialNewDimVal }, false);
            } else {
                drawOnPreviewCanvas(newCanvasPreviewCtx, newCanvasPreview, null, {width: initialNewDimVal, height: initialNewDimVal}, true);
            }
        }

        // closeSizeModal function remains the same as previously provided
        // applyNewGridSize function remains the same

        function closeSizeModal(applyChange = false) {
            if (applyChange && tempSelectedSizeValue) {
                // If the new size is different from the current active canvas's size
                if (String(currentGridSize) !== tempSelectedSizeValue) {
                    applyNewGridSize(tempSelectedSizeValue);
                }
            }
            sizeModal.style.display = 'none';
            tempSelectedSizeValue = null; // Reset temporary selection
        }
        function hexToRgba(hex, alphaPercent = 100) {
            let alphaValue = Math.min(1, Math.max(0, alphaPercent / 100));

            // If brush opacity is effectively 100%, force alpha to exactly 1.0
            if (alphaPercent >= 99.9) { // Threshold for floating point issues
                alphaValue = 1.0;
            }

            // Consistently format the alpha string
            const alphaString = alphaValue === 1.0 ? "1.000" : alphaValue.toFixed(3);

            if (!hex || typeof hex !== 'string' || !hex.startsWith('#')) {
                return `rgba(0,0,0,${alphaString})`;
            }
            let r = 0, g = 0, b = 0;
            if (hex.length === 4) {
                r = parseInt(hex[1] + hex[1], 16); g = parseInt(hex[2] + hex[2], 16); b = parseInt(hex[3] + hex[3], 16);
            } else if (hex.length === 7) {
                r = parseInt(hex.substring(1, 3), 16); g = parseInt(hex.substring(3, 5), 16); b = parseInt(hex.substring(5, 7), 16);
            } else { return `rgba(0,0,0,${alphaString})`; }
            if (isNaN(r) || isNaN(g) || isNaN(b)) { return `rgba(0,0,0,${alphaString})`;}
            return `rgba(${r},${g},${b},${alphaString})`;
        }

        function parseRgba(rgbaString) {
            if (rgbaString === EMPTY_COLOR || rgbaString === null || rgbaString === undefined) {
                return { r: 0, g: 0, b: 0, a: 0 };
            }
            if (typeof rgbaString !== 'string') {
                console.warn("parseRgba: received non-string input", rgbaString);
                return { r: 0, g: 0, b: 0, a: 0 };
            }
            const match = rgbaString.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)$/i);
            if (match) {
                return { r: parseInt(match[1]), g: parseInt(match[2]), b: parseInt(match[3]), a: match[4] !== undefined ? parseFloat(match[4]) : 1 };
            }
            if (rgbaString.startsWith('#')) {
                const tempRgba = hexToRgba(rgbaString, 100);
                return parseRgba(tempRgba);
            }
            console.warn("parseRgba: failed to parse string:", rgbaString);
            return { r: 0, g: 0, b: 0, a: 0 };
        }

        function validateAndClampInput(inputElement, minVal, maxVal, updateCallback, sliderElement) {
            let value = parseInt(inputElement.value);
            let clampedValue = value;
            
            // Use security validation if available
            if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                const validation = SecurityUtils.InputValidator.validateNumericRange(value, minVal, maxVal, inputElement.id || 'Value');
                if (validation.valid) {
                    clampedValue = validation.value;
                } else {
                    clampedValue = minVal; // Default to minimum on validation failure
                }
            } else {
                // Fallback validation
                if (isNaN(value)) clampedValue = minVal;
                else if (value < minVal) clampedValue = minVal;
                else if (value > maxVal) clampedValue = maxVal;
            }
            
            if (value !== clampedValue || isNaN(value)) inputElement.value = clampedValue;
            if (updateCallback) updateCallback(clampedValue);
            if (sliderElement) sliderElement.value = clampedValue;
        }

        // Secure undo/redo history management
        function calculateUndoStateSize(undoState) {
            // Estimate memory usage of an undo state
            const jsonString = JSON.stringify(undoState);
            return jsonString.length * 2; // Rough estimate: 2 bytes per character
        }

        function addToUndoStack(undoState) {
            // Use security validation if available
            if (typeof SecurityUtils !== 'undefined' && SecurityUtils.ResourceManager) {
                const validation = SecurityUtils.ResourceManager.validateUndoHistorySize(currentUndoStack.length, currentUndoMemoryUsage);
                if (!validation.valid) {
                    console.warn('Undo history limit reached, removing oldest entries');
                    // Remove oldest entries to make space
                    while (currentUndoStack.length >= MAX_UNDO_HISTORY / 2) {
                        const removed = currentUndoStack.shift();
                        if (removed) {
                            currentUndoMemoryUsage -= calculateUndoStateSize(removed);
                        }
                    }
                }
            } else {
                // Fallback validation
                if (currentUndoStack.length >= MAX_UNDO_HISTORY) {
                    const removed = currentUndoStack.shift();
                    if (removed) {
                        currentUndoMemoryUsage -= calculateUndoStateSize(removed);
                    }
                }
            }

            // Calculate size of new state
            const stateSize = calculateUndoStateSize(undoState);
            
            // Check memory limit
            if (currentUndoMemoryUsage + stateSize > MAX_UNDO_MEMORY_MB * 1024 * 1024) {
                // Remove oldest states until we have enough space
                while (currentUndoStack.length > 0 && currentUndoMemoryUsage + stateSize > MAX_UNDO_MEMORY_MB * 1024 * 1024) {
                    const removed = currentUndoStack.shift();
                    if (removed) {
                        currentUndoMemoryUsage -= calculateUndoStateSize(removed);
                    }
                }
            }

            // Add new state
            currentUndoStack.push(undoState);
            currentUndoMemoryUsage += stateSize;
            
            // Clear redo stack when new action is performed
            currentRedoStack.forEach(state => {
                currentUndoMemoryUsage -= calculateUndoStateSize(state);
            });
            currentRedoStack = [];
            
            updateMobileUndoRedoButtons();
        }

        function recalculateUndoMemoryUsage() {
            // Recalculate memory usage for current undo/redo stacks
            currentUndoMemoryUsage = 0;
            currentUndoStack.forEach(state => {
                currentUndoMemoryUsage += calculateUndoStateSize(state);
            });
            currentRedoStack.forEach(state => {
                currentUndoMemoryUsage += calculateUndoStateSize(state);
            });
        }

        function clearUndoRedoStacks() {
            // Securely clear undo/redo stacks and reset memory usage
            currentUndoStack.length = 0;
            currentRedoStack.length = 0;
            currentUndoMemoryUsage = 0;
            updateMobileUndoRedoButtons();
        }

        // Security notification system
        function showNotification(message, type = 'info') {
            // Use existing notification system if available, otherwise fallback to alert
            if (typeof showSuccessNotification === 'function' && type === 'success') {
                showSuccessNotification(message);
            } else if (typeof showErrorNotification === 'function' && type === 'error') {
                showErrorNotification(message);
            } else {
                // Fallback to alert for critical security messages
                if (type === 'error') {
                    alert('Security Warning: ' + message);
                } else {
                    console.log('Security Info: ' + message);
                }
            }
        }

        if (thicknessInput) {
            thicknessInput.addEventListener('input', () => validateAndClampInput(thicknessInput, parseInt(thicknessInput.min), parseInt(thicknessInput.max)));
            thicknessInput.addEventListener('blur', () => validateAndClampInput(thicknessInput, parseInt(thicknessInput.min), parseInt(thicknessInput.max)));
        }
        if (brushOpacityInput && brushOpacitySlider) {
            brushOpacityInput.addEventListener('input', () => validateAndClampInput(brushOpacityInput, 0, 100, (val) => currentBrushOpacity = val, brushOpacitySlider));
            brushOpacityInput.addEventListener('blur', () => validateAndClampInput(brushOpacityInput, 0, 100, (val) => currentBrushOpacity = val, brushOpacitySlider));
            brushOpacitySlider.addEventListener('input', (e) => { currentBrushOpacity = parseInt(e.target.value); brushOpacityInput.value = currentBrushOpacity; });
        }
        if (fillOpacityInput && fillOpacitySlider) {
            fillOpacityInput.addEventListener('input', () => validateAndClampInput(fillOpacityInput, 0, 100, (val) => currentFillOpacity = val, fillOpacitySlider));
            fillOpacityInput.addEventListener('blur', () => validateAndClampInput(fillOpacityInput, 0, 100, (val) => currentFillOpacity = val, fillOpacitySlider));
            fillOpacitySlider.addEventListener('input', (e) => { currentFillOpacity = parseInt(e.target.value); fillOpacityInput.value = currentFillOpacity; });
        }

        function createCanvasState(name, initialGridSize) {
            const newCanvasId = nextCanvasId++;
            const canvasState = {
                id: newCanvasId, name: name || `Canvas ${newCanvasId + 1}`, gridSize: initialGridSize, layers: [],
                activeLayerIndex: 0, undoStack: [], redoStack: [], viewOffsetX: 0, viewOffsetY: 0, zoomFactor: 1,
                filename: (name || `Canvas ${newCanvasId + 1}`).toLowerCase().replace(/\s+/g, '-') + '.png',
                selectionStart: null, selectionEnd: null, isSelecting: false,
            };
            initializeLayersForCanvas(canvasState);
            return canvasState;
        }
        function initializeLayersForCanvas(canvasState) {
            const initialLayer = createNewLayer(null, canvasState.gridSize, canvasState, true);
            canvasState.layers.push(initialLayer);
            canvasState.activeLayerIndex = 0;
        }
        function addNewCanvas() {
            // Use security validation if available
            if (typeof SecurityUtils !== 'undefined' && SecurityUtils.ResourceManager) {
                const validation = SecurityUtils.ResourceManager.validateCanvasCount(canvases.length);
                if (!validation.valid) {
                    showNotification(validation.error, 'error');
                    addNewCanvasBtn.disabled = true;
                    return;
                }
            } else {
                // Fallback validation
                if (canvases.length >= MAX_CANVASES) {
                    showNotification(`Maximum number of canvases (${MAX_CANVASES}) reached.`, 'error');
                    addNewCanvasBtn.disabled = true;
                    return;
                }
            }
            
            const newCanvasName = `Canvas ${canvases.length + 1}`;
            // Use currentGridSize (from active canvas) or a default like 64
            const initialSizeForNewCanvas = (activeCanvasIndex !== -1 && canvases[activeCanvasIndex]) ? canvases[activeCanvasIndex].gridSize : 64;
            const newCanvas = createCanvasState(newCanvasName, initialSizeForNewCanvas);
            canvases.push(newCanvas);
            switchActiveCanvas(canvases.length - 1);
            if (canvases.length >= MAX_CANVASES) addNewCanvasBtn.disabled = true;
            if (canvases.length > 0) renderCanvasTabsUI();

            // Save to localStorage after adding new canvas
            debouncedSaveToLocalStorage();
        }
        function switchActiveCanvas(newCanvasIndex) {
            if (newCanvasIndex < 0 || newCanvasIndex >= canvases.length) {
                console.error("Invalid canvas index:", newCanvasIndex);
                if (canvases.length > 0) newCanvasIndex = 0; else return;
            }
            if (activeCanvasIndex !== -1 && canvases[activeCanvasIndex]) {
                const oldC = canvases[activeCanvasIndex];
                oldC.viewOffsetX = currentViewOffsetX; oldC.viewOffsetY = currentViewOffsetY; oldC.zoomFactor = currentZoomFactor;
                oldC.filename = filenameInput.value; oldC.selectionStart = currentSelectionStart; oldC.selectionEnd = currentSelectionEnd;
                oldC.isSelecting = isSelecting;
            }
            activeCanvasIndex = newCanvasIndex;
            const activeC = canvases[activeCanvasIndex];
            currentGridSize = activeC.gridSize; currentLayers = activeC.layers; currentActiveLayerIndex = activeC.activeLayerIndex;
            activeLayer = currentLayers[currentActiveLayerIndex]; // Keep activeLayer variable updated
            currentUndoStack = activeC.undoStack; currentRedoStack = activeC.redoStack;
            
            // Recalculate memory usage for the new canvas
            recalculateUndoMemoryUsage();
            
            currentViewOffsetX = activeC.viewOffsetX; currentViewOffsetY = activeC.viewOffsetY; currentZoomFactor = activeC.zoomFactor;
            currentSelectionStart = activeC.selectionStart; currentSelectionEnd = activeC.selectionEnd; isSelecting = activeC.isSelecting;
            currentGridSize = activeC.gridSize;
            zoomSlider.value = currentZoomFactor.toString();
            filenameInput.value = activeC.filename;
            renderLayersUI(); renderCanvasTabsUI(); resizeCanvases(); requestFullRedraw(); updateCursor();

            // Update mobile undo/redo buttons for the new canvas
            updateMobileUndoRedoButtons();

            // Save to localStorage when switching canvases
            debouncedSaveToLocalStorage();
        }
        function deleteCanvas(canvasIndexToDelete) {
            if (canvases.length <= 1) { alert("Cannot delete the last canvas."); return; }
            if (canvasIndexToDelete < 0 || canvasIndexToDelete >= canvases.length) return;
            if (!confirm(`Delete canvas "${canvases[canvasIndexToDelete].name}"? This cannot be undone.`)) return;

            // Get the canvas ID before deleting
            const deletedCanvasId = canvases[canvasIndexToDelete].id;

            canvases.splice(canvasIndexToDelete, 1);
            if (canvases.length < MAX_CANVASES) addNewCanvasBtn.disabled = false;
            let newActiveIdx = activeCanvasIndex;
            if (activeCanvasIndex === canvasIndexToDelete) newActiveIdx = Math.max(0, canvasIndexToDelete - 1);
            else if (activeCanvasIndex > canvasIndexToDelete) newActiveIdx = activeCanvasIndex - 1;
            if (canvases.length > 0) switchActiveCanvas(newActiveIdx); else initializeEditor();

            // Save to localStorage after deletion
            debouncedSaveToLocalStorage();
           }
        function renderCanvasTabsUI() {
            canvasTabsBar.querySelectorAll('.canvas-tab').forEach(tab => tab.remove());
            canvases.forEach((canvas, index) => {
                const tab = document.createElement('div'); tab.className = 'canvas-tab'; tab.dataset.canvasId = canvas.id; tab.dataset.canvasIndex = index;
                if (index === activeCanvasIndex) tab.classList.add('active-canvas-tab');
                const nameSpan = document.createElement('span'); nameSpan.className = 'canvas-tab-name'; nameSpan.textContent = canvas.name; nameSpan.title = canvas.name;
                const controlsDiv = document.createElement('div'); controlsDiv.className = 'canvas-tab-controls';
                const editBtn = document.createElement('button'); editBtn.textContent = '✏️'; editBtn.title = "Rename Canvas";
                editBtn.onclick = (e) => { e.stopPropagation(); if (nameSpan.querySelector('input')) return; const oN=canvas.name; nameSpan.innerHTML=''; const i=document.createElement('input'); i.type='text';i.value=oN;i.maxLength=CANVAS_NAME_MAX_LENGTH;i.spellcheck=false; i.onblur=()=>{const nN=i.value.trim();canvas.name=nN||`Canvas ${canvas.id+1}`;if(index===activeCanvasIndex){canvas.filename=(canvas.name).toLowerCase().replace(/\s+/g,'-')+'.png';filenameInput.value=canvas.filename}renderCanvasTabsUI();debouncedSaveToLocalStorage()}; i.onkeydown=ev=>{ev.stopPropagation();if(ev.key==='Enter'){ev.preventDefault();i.blur()}else if(ev.key==='Escape'){ev.preventDefault();i.value=oN;i.blur()}}; i.onclick=ev=>ev.stopPropagation(); nameSpan.appendChild(i);i.focus();if(typeof i.select==='function')i.select()};
                const deleteBtn = document.createElement('button'); deleteBtn.textContent = '✕'; deleteBtn.title = "Delete Canvas"; deleteBtn.disabled = canvases.length <= 1;
                deleteBtn.onclick = (e) => { e.stopPropagation(); deleteCanvas(index); };
                controlsDiv.append(editBtn, deleteBtn); tab.append(nameSpan, controlsDiv);
                tab.onclick = () => { if (index !== activeCanvasIndex) switchActiveCanvas(index); };
                canvasTabsBar.insertBefore(tab, addNewCanvasBtn);
            });
        }

        function createNewPixelData(size, fillValue = EMPTY_COLOR) {
            return Array(size).fill(null).map(() => Array(size).fill(fillValue));
        }
        function findNextAvailableLayerNumber(existingLayers) {
            const layerNumbersInUse = new Set();
            const layerNameRegex = /^Layer (\d+)$/i;
            for (const layer of existingLayers) {
                const match = layer.name.match(layerNameRegex);
                if (match && match[1]) layerNumbersInUse.add(parseInt(match[1]));
            }
            let nextNumber = 1;
            while (layerNumbersInUse.has(nextNumber)) nextNumber++;
            return nextNumber;
        }

        function findNextAvailableImportedImageNumber(existingLayers) {
            const numbersInUse = new Set();
            const importedImageRegex = /^Imported Image \((\d+)\)$/i;
            for (const layer of existingLayers) {
                const match = layer.name.match(importedImageRegex);
                if (match && match[1]) numbersInUse.add(parseInt(match[1]));
            }
            let nextNumber = 1;
            while (numbersInUse.has(nextNumber)) nextNumber++;
            return nextNumber;
        }
        function createNewLayer(name, size, canvasStateForNaming, isFirstLayer = false) {
            // Validate layer name if provided
            if (name && typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                const validation = SecurityUtils.InputValidator.validateLayerName(name);
                if (!validation.valid) {
                    showNotification(validation.error, 'error');
                    return null;
                }
            } else if (name) {
                // Fallback validation
                if (name.length > LAYER_NAME_MAX_LENGTH) {
                    showNotification(`Layer name cannot exceed ${LAYER_NAME_MAX_LENGTH} characters`, 'error');
                    return null;
                }
                if (/<[^>]*>/g.test(name)) {
                    showNotification('Layer name cannot contain HTML tags', 'error');
                    return null;
                }
            }
            
            // Validate canvas size
            if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                const sizeValidation = SecurityUtils.InputValidator.validateNumericRange(size, 1, SecurityUtils.SECURITY_CONFIG.MAX_CANVAS_SIZE, 'Canvas size');
                if (!sizeValidation.valid) {
                    showNotification(sizeValidation.error, 'error');
                    return null;
                }
            } else {
                // Fallback validation
                if (size < 1 || size > 1024) {
                    showNotification('Canvas size must be between 1 and 1024', 'error');
                    return null;
                }
            }
            
            const layerUniqueId = ++nextLayerIdGlobal;
            let layerDisplayName;
            if (name) {
                layerDisplayName = name;
            } else if (canvasStateForNaming) {
                const targetLayersForNaming = canvasStateForNaming.layers || [];
                const nextNum = findNextAvailableLayerNumber(targetLayersForNaming);
                layerDisplayName = `Layer ${nextNum}`;
            } else {
                layerDisplayName = `Layer ${layerUniqueId}`;
            }
            return { id: layerUniqueId, name: layerDisplayName, pixels: createNewPixelData(size), isVisible: true, opacity: 100 };
        }
        async function initializeEditor() {
            // Initialize IndexedDB for large canvas storage
            await initCanvasDB();

            // Try to load saved canvases from localStorage first, then IndexedDB
            let loadedFromStorage = loadCanvasesFromLocalStorage();

            if (!loadedFromStorage || canvases.length === 0) {
                // Try IndexedDB if localStorage failed
                loadedFromStorage = await loadCanvasesFromIndexedDB();
            }

            if (!loadedFromStorage || canvases.length === 0) {
                // No saved data or loading failed, create default canvas
                const initialGridSize = (availableSizes.find(s => s.value === "64") || availableSizes[0]).value;
                const firstCanvas = createCanvasState("Canvas 1", parseInt(initialGridSize));
                canvases.push(firstCanvas);
                switchActiveCanvas(0); // This will set currentGridSize and activeLayer
            }

            addNewCanvasBtn.disabled = canvases.length >= MAX_CANVASES;

            // Render the canvas tabs UI to show loaded canvases
            if (canvases.length > 0) {
                renderCanvasTabsUI();
            }

            // Initialize mobile undo/redo button states
            updateMobileUndoRedoButtons();

            // Initialize selection action button states
            updateSelectionActionButtons();
        }
        function isInputActive() {
            const el = document.activeElement; if (!el) return false;
            const pEl = el.parentElement;
            if (el.tagName === 'INPUT' && pEl && (pEl.classList.contains('layer-name')||pEl.classList.contains('canvas-tab-name'))) return true;
            const kIds = ['hexInput','filenameInput','thickness','color-picker','gridSize','tool','gridStyle','checkerboardToggle','hatchScaleSlider','sprayRadiusSlider','brushOpacitySlider','fillOpacitySlider','brushOpacityInput','fillOpacityInput', 'radialAxesSlider'];
            if (kIds.includes(el.id)||(el.id&&el.id.startsWith('layerOpacitySlider_'))||(el.id&&el.id.startsWith('layerOpacityInput_'))||el.name==='brushType'||el.name==='fillType') return true;
            const tn = el.tagName.toLowerCase(); return tn==='input'||tn==='textarea'||tn==='select';
        }

        function rgbToHex(r,g,b){return"#"+((1<<24)+(Math.round(r)<<16)+(Math.round(g)<<8)+Math.round(b)).toString(16).slice(1).toUpperCase()}
        if(colorPickerInput){colorPickerInput.oninput=e=>{currentSelectedColor=e.target.value.toUpperCase();if(hexInput)hexInput.value=currentSelectedColor;if(toolSelector.value==='erase'){toolSelector.value=previousDrawingTool||'draw';toolSelector.dispatchEvent(new Event('change'))}}}
        if(hexInput){
            // Validate and apply hex color
            const validateAndApplyHexColor = () => {
                let v = hexInput.value.trim().toUpperCase();
                if(!v.startsWith('#') && v.length > 0) v = '#' + v;
                
                // Use security validation if available
                if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                    const validation = SecurityUtils.InputValidator.validateHexColor(v);
                    if (validation.valid) {
                        let nCH = v;
                        if(nCH.length === 4) nCH = "#" + nCH[1].repeat(2) + nCH[2].repeat(2) + nCH[3].repeat(2);
                        currentSelectedColor = nCH;
                        hexInput.value = currentSelectedColor;
                        if(colorPickerInput) colorPickerInput.value = currentSelectedColor;
                        if(toolSelector.value === 'erase') {
                            toolSelector.value = previousDrawingTool || 'draw';
                            toolSelector.dispatchEvent(new Event('change'));
                        }
                    } else {
                        // Reset to previous valid color on invalid input
                        hexInput.value = currentSelectedColor;
                    }
                } else {
                    // Fallback validation
                    if(/^#[0-9A-F]{6}$/i.test(v) || /^#[0-9A-F]{3}$/i.test(v)) {
                        let nCH = v;
                        if(nCH.length === 4) nCH = "#" + nCH[1].repeat(2) + nCH[2].repeat(2) + nCH[3].repeat(2);
                        currentSelectedColor = nCH;
                        hexInput.value = currentSelectedColor;
                        if(colorPickerInput) colorPickerInput.value = currentSelectedColor;
                        if(toolSelector.value === 'erase') {
                            toolSelector.value = previousDrawingTool || 'draw';
                            toolSelector.dispatchEvent(new Event('change'));
                        }
                    } else {
                        // Reset to previous valid color on invalid input
                        hexInput.value = currentSelectedColor;
                    }
                }
            };
            
            // Only validate and apply on blur or Enter key
            hexInput.addEventListener('blur', validateAndApplyHexColor);
            hexInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    validateAndApplyHexColor();
                    hexInput.blur();
                }
            });
        }
        if(checkerboardToggle){checkerboardToggle.onchange=()=>{canvasContainer.classList.toggle('checkerboard',checkerboardToggle.checked);requestFullRedraw()}}
        if(hatchScaleSlider){hatchScaleSlider.oninput=e=>{currentHatchScale=parseInt(e.target.value);if(hatchScaleValueSpan)hatchScaleValueSpan.textContent=currentHatchScale}}
        if(sprayRadiusSlider){
            sprayRadiusSlider.oninput=e=>{
                currentSprayRadius=parseInt(e.target.value);
                if(sprayRadiusValueSpan)sprayRadiusValueSpan.textContent=currentSprayRadius;
            }
        }

        if (glowSizeSlider) {
            glowSizeSlider.addEventListener('input', e => {
                currentGlowSize = parseInt(e.target.value);
                if (glowSizeValueSpan) {
                    glowSizeValueSpan.textContent = currentGlowSize;
                }
            });
        }
        document.querySelectorAll('input[name="brushType"]').forEach(r=>{r.onchange=e=>{currentBrushType=e.target.value;updateBrushOptionsBarContent()}});
        document.querySelectorAll('input[name="fillType"]').forEach(r=>{r.onchange=e=>{currentFillType=e.target.value;updateBrushOptionsBarContent()}});

        function updateBrushOptionsBarContent() {
            const currentTool = toolSelector.value;
            const selectedBrushType = currentBrushType;

            const isDrawToolActive = currentTool === 'draw';
            const isFillToolActive = currentTool === 'fill';
            const isSelectToolActive = currentTool === 'select';

            // Default all specific option containers to hidden
            if (brushOpacityContainer) brushOpacityContainer.style.display = 'none';
            if (commonBrushOptionsContainer) commonBrushOptionsContainer.style.display = 'none';
            if (hatchingBrushOptionsContainer) hatchingBrushOptionsContainer.style.display = 'none';
            if (sprayBrushOptionsContainer) sprayBrushOptionsContainer.style.display = 'none';
            if (glowBrushOptionsContainer) glowBrushOptionsContainer.style.display = 'none';
            if (mirrorOptionsContainer) mirrorOptionsContainer.style.display = 'none';
            if (fillOptionsContainer) fillOptionsContainer.style.display = 'none';
            if (selectOptionsContainer) selectOptionsContainer.style.display = 'none';
            if (panOptionsContainer) panOptionsContainer.style.display = 'none';
            if (eyedropperOptionsContainer) eyedropperOptionsContainer.style.display = 'none';
            if (eraseOptionsContainer) eraseOptionsContainer.style.display = 'none';

            // Get circle options container
            const circleOptionsContainer = document.getElementById('circleOptionsContainer');
            if (circleOptionsContainer) circleOptionsContainer.style.display = 'none';
            if (hatchScaleContainer) hatchScaleContainer.style.display = 'none';

            const isShapeToolActive = ['line', 'rectangle'].includes(currentTool); // Removed 'circle' so it can have its own case

            if ((isDrawToolActive || isShapeToolActive) && !isMovingSelection) { // MODIFIED CONDITION
                if (brushOpacityContainer) brushOpacityContainer.style.display = 'flex';

                // For shapes, we might not need all brush types (e.g., spray, glow might not make sense directly for a line tool's definition)
                // You can decide which brush options are relevant for shapes.
                // For now, let's enable common and hatching, which seem applicable as they affect the stroke.
                if (commonBrushOptionsContainer) commonBrushOptionsContainer.style.display = 'flex';
                if (hatchingBrushOptionsContainer) hatchingBrushOptionsContainer.style.display = 'flex';

                // Mirror options can also be useful for shapes
                if (mirrorOptionsContainer) mirrorOptionsContainer.style.display = 'block';

                const hatchBrushTypes = ['hatch', 'cross_hatch', 'grid_hatch', 'dot_hatch'];
                if (hatchBrushTypes.includes(selectedBrushType)) {
                    if (hatchScaleContainer) hatchScaleContainer.style.display = 'block';
                }

                // These might be less relevant for shapes unless your shapes can be "sprayed" or "glow"
                // Only show these if it's the actual draw tool and the specific brush is selected
                if (isDrawToolActive) {
                    if (selectedBrushType === 'spray_paint') {
                        if (sprayBrushOptionsContainer) sprayBrushOptionsContainer.style.display = 'block';
                    }
                    if (selectedBrushType === 'glow') {
                        if (glowBrushOptionsContainer) glowBrushOptionsContainer.style.display = 'block';
                    }
                }
            } else if (isFillToolActive) {
                if (fillOptionsContainer) fillOptionsContainer.style.display = 'flex';
                const hatchFillTypes = ['hatch_fill', 'cross_hatch_fill', 'grid_hatch_fill', 'dot_hatch_fill'];
                if (hatchFillTypes.includes(currentFillType)) {
                    if (hatchScaleContainer) hatchScaleContainer.style.display = 'block';
                }
            } else if (isSelectToolActive) {
                if (selectOptionsContainer) selectOptionsContainer.style.display = 'flex';
                const selectionActuallyActive = isSelecting && currentSelectionStart && currentSelectionEnd &&
                    typeof currentSelectionStart.x === 'number' && typeof currentSelectionStart.y === 'number' && // Validity check
                    (currentSelectionEnd.x >= currentSelectionStart.x) &&
                    (currentSelectionEnd.y >= currentSelectionStart.y);
                const disableSelectActions = isMovingSelection;

                if (flipHorizontalBtn) flipHorizontalBtn.disabled = !(selectionActuallyActive && !disableSelectActions);
                if (flipVerticalBtn) flipVerticalBtn.disabled = !(selectionActuallyActive && !disableSelectActions);
                if (scaleUpSelectionBtn) scaleUpSelectionBtn.disabled = !(selectionActuallyActive && !disableSelectActions);
                if (scaleDownSelectionBtn) scaleDownSelectionBtn.disabled = !(selectionActuallyActive && !disableSelectActions);
                if (rotate90SelectionBtn) rotate90SelectionBtn.disabled = !(selectionActuallyActive && !disableSelectActions);
                if (rotate180SelectionBtn) rotate180SelectionBtn.disabled = !(selectionActuallyActive && !disableSelectActions);
            } else if (currentTool === 'pan') {
                if (panOptionsContainer) panOptionsContainer.style.display = 'flex';
            } else if (currentTool === 'eyedropper') {
                if (eyedropperOptionsContainer) eyedropperOptionsContainer.style.display = 'flex';
            } else if (currentTool === 'erase') {
                // Show erase tool info first (title and blurb)
                if (eraseOptionsContainer) eraseOptionsContainer.style.display = 'flex';

                // Then give erase tool access to all brush functionality options
                if (brushOpacityContainer) brushOpacityContainer.style.display = 'flex';
                if (commonBrushOptionsContainer) commonBrushOptionsContainer.style.display = 'flex';
                if (hatchingBrushOptionsContainer) hatchingBrushOptionsContainer.style.display = 'flex';
                if (mirrorOptionsContainer) mirrorOptionsContainer.style.display = 'block';

                const hatchBrushTypes = ['hatch', 'cross_hatch', 'grid_hatch', 'dot_hatch'];
                if (hatchBrushTypes.includes(selectedBrushType)) {
                    if (hatchScaleContainer) hatchScaleContainer.style.display = 'block';
                }

                if (selectedBrushType === 'spray_paint') {
                    if (sprayBrushOptionsContainer) sprayBrushOptionsContainer.style.display = 'block';
                }
                if (selectedBrushType === 'glow') {
                    if (glowBrushOptionsContainer) glowBrushOptionsContainer.style.display = 'block';
                }
            } else if (currentTool === 'circle') {
                // Show circle-specific info about Shift key
                if (circleOptionsContainer) circleOptionsContainer.style.display = 'flex';

                // Give circle tool access to all brush functionality options (like draw tool)
                if (brushOpacityContainer) brushOpacityContainer.style.display = 'flex';
                if (commonBrushOptionsContainer) commonBrushOptionsContainer.style.display = 'flex';
                if (hatchingBrushOptionsContainer) hatchingBrushOptionsContainer.style.display = 'flex';
                if (mirrorOptionsContainer) mirrorOptionsContainer.style.display = 'block';

                const hatchBrushTypes = ['hatch', 'cross_hatch', 'grid_hatch', 'dot_hatch'];
                if (hatchBrushTypes.includes(selectedBrushType)) {
                    if (hatchScaleContainer) hatchScaleContainer.style.display = 'block';
                }

                if (selectedBrushType === 'spray_paint') {
                    if (sprayBrushOptionsContainer) sprayBrushOptionsContainer.style.display = 'block';
                }
                if (selectedBrushType === 'glow') {
                    if (glowBrushOptionsContainer) glowBrushOptionsContainer.style.display = 'block';
                }
            }
        }

        function resetDirtyRect(){dirtyRect.minX=Infinity;dirtyRect.minY=Infinity;dirtyRect.maxX=-Infinity;dirtyRect.maxY=-Infinity;isCanvasDirtyForTools=false}
        function expandDirtyRect(x,y){isCanvasDirtyForTools=true;dirtyRect.minX=Math.min(dirtyRect.minX,x);dirtyRect.minY=Math.min(dirtyRect.minY,y);dirtyRect.maxX=Math.max(dirtyRect.maxX,x);dirtyRect.maxY=Math.max(dirtyRect.maxY,y)}
        function requestFullRedraw(){needsFullRedraw=true}
        function drawTaperedSegmentToBuffer(buffer, x0, y0, x1, y1, baseHexColor, opacityPercent, thick0, thick1, shape = 'circular') {
            const dxAbs = Math.abs(x1 - x0);
            const dyAbs = Math.abs(y1 - y0);
            const sx = (x0 < x1) ? 1 : -1;
            const sy = (y0 < y1) ? 1 : -1;
            let err = dxAbs - dyAbs;
            let currentX = x0;
            let currentY = y0;
            const totalSteps = Math.max(1, dxAbs + dyAbs);
            let stepsTaken = 0;
            const colorWithOpacity = hexToRgba(baseHexColor, opacityPercent);

            while (true) {
                const t = (totalSteps <= 1) ? 1.0 : stepsTaken / (totalSteps -1) ;
                const currentThickness = Math.max(1, Math.round(thick0 * (1 - t) + thick1 * t));
                const stampRadius = (currentThickness - 1) / 2.0;
                const iterBound = Math.ceil(stampRadius);

                for (let offsetY = -iterBound; offsetY <= iterBound; offsetY++) {
                    for (let offsetX = -iterBound; offsetX <= iterBound; offsetX++) {
                        let includePixel = false;
                        if (shape === 'circular') {
                            if (offsetX * offsetX + offsetY * offsetY <= stampRadius * stampRadius + 0.5) {
                                includePixel = true;
                            }
                        } else { includePixel = true; }

                        if (includePixel) {
                            const gX = currentX + offsetX;
                            const gY = currentY + offsetY;
                            if (gX >= 0 && gX < currentGridSize && gY >= 0 && gY < currentGridSize) {
                                if (!buffer[gY]) buffer[gY] = Array(currentGridSize).fill(undefined);
                                const existingVal = buffer[gY][gX];
                                const newPixelRgba = parseRgba(colorWithOpacity);
                                if (existingVal && existingVal !== EMPTY_COLOR) {
                                    const existingOpacity = parseRgba(existingVal).a;
                                    if (newPixelRgba.a > existingOpacity) {
                                        buffer[gY][gX] = colorWithOpacity;
                                    }
                                } else {
                                    buffer[gY][gX] = colorWithOpacity;
                                }
                            }
                        }
                    }
                }
                if (currentX === x1 && currentY === y1) break;
                stepsTaken++;
                const e2 = 2 * err;
                if (e2 > -dyAbs) { err -= dyAbs; currentX += sx; }
                if (e2 < dxAbs) { err += dxAbs; currentY += sy; }
            }
        }

        function drawSinglePixelCellOnGrid(gX, gY, colorString) {
            // This function draws on gridCtx (the top canvas for previews/grid lines)
            if (colorString === EMPTY_COLOR || colorString === undefined) return;
            
            // Calculate the exact screen position for this cell
            const screenXStart = (gX - currentViewOffsetX) * renderedCellSize;
            const screenYStart = (gY - currentViewOffsetY) * renderedCellSize;
            
            // Calculate the exact screen position for the next cell
            const screenXEnd = ((gX + 1) - currentViewOffsetX) * renderedCellSize;
            const screenYEnd = ((gY + 1) - currentViewOffsetY) * renderedCellSize;
            
            // Round to get exact pixel boundaries - this ensures no gaps or overlaps
            const drawX = Math.round(screenXStart);
            const drawY = Math.round(screenYStart);
            const drawRight = Math.round(screenXEnd);
            const drawBottom = Math.round(screenYEnd);
            
            // Calculate width and height from the rounded boundaries
            const drawWidth = drawRight - drawX;
            const drawHeight = drawBottom - drawY;
            
            // Only draw if we have a valid size
            if (drawWidth > 0 && drawHeight > 0) {
                gridCtx.fillStyle = colorString;
                gridCtx.fillRect(drawX, drawY, drawWidth, drawHeight);
            }
        }

        function drawLiveStrokePreviewOnGrid() {
            const currentTool = toolSelector.value;

            // For 'draw' and 'erase' tools, the live preview is now directly on activeLayer.pixels,
            // which is rendered to pixelCanvas via renderDirtyRectInViewport.
            // So, we don't draw their stroke buffer here on gridCanvas.
            if (drawing && (currentTool === 'draw' || currentTool === 'erase')) {
                return;
            }

            // Fallback for other tools if they were to use currentStrokeBuffer for gridCanvas preview
            if (!drawing || !currentStrokeBuffer) {
                return;
            }
            // Example: if a shape tool used currentStrokeBuffer for its preview before mouseup
            // (currently they use direct gridCtx drawing functions like previewLineOnGrid)
            // This section is now unlikely to be hit by 'draw' or 'erase' during a drag.
            if (currentTool === 'draw') { // This condition will likely not be met if 'drawing' is true due to the guard above
                for (let y = 0; y < currentGridSize; y++) {
                    if (currentStrokeBuffer[y]) {
                        for (let x = 0; x < currentGridSize; x++) {
                            const strokePixel = currentStrokeBuffer[y][x];
                            if (strokePixel !== undefined && strokePixel !== EMPTY_COLOR) {
                                drawSinglePixelCellOnGrid(x, y, strokePixel);
                            }
                        }
                    }
                }
            }
        }
        function mainRenderLoop() {
            if (needsFullRedraw) {
                fullRedrawCount++;
                if (fullRedrawCount % 100 === 0) {
                    console.log(`Performance: ${fullRedrawCount} full redraws, ${dirtyRedrawCount} dirty redraws (${currentGridSize}x${currentGridSize})`);
                }
                redrawFullView();
                needsFullRedraw = false;
            } else if (isCanvasDirtyForTools && activeLayer) {
                dirtyRedrawCount++;
                renderDirtyRectInViewport();
                gridCtx.clearRect(0, 0, gridCanvas.width, gridCanvas.height);
                drawGrid();
                if (toolSelector.value === 'select') {
                    // REMOVED: isResizingSelection block
                    if (isMovingSelection && currentSelectionStart && currentSelectionEnd) {
                        drawMovingSelectionPreview();
                        applyOcclusionMaskForMovingSelection(); // Ensure occlusion is applied
                    } else if (isSelecting && currentSelectionStart && currentSelectionEnd) {
                        drawSelectionBox();
                    }
                }
                // Removed: drawLiveStrokePreviewOnGrid(); as previews are direct or specific.
                isCanvasDirtyForTools = false;
                resetDirtyRect();
            }
            requestAnimationFrame(mainRenderLoop);
        }
        function handleRotateSelection(degrees) {
            if (toolSelector.value !== 'select' || !isSelecting || !currentSelectionStart || !currentSelectionEnd || !activeLayer || !activeLayer.isVisible) {
                console.warn("Rotate preconditions not met.");
                return;
            }

            saveState();

            const selX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
            const selY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
            const selX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
            const selY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);

            const originalSelWidth = selX1 - selX0 + 1;
            const originalSelHeight = selY1 - selY0 + 1;

            if (originalSelWidth <= 0 || originalSelHeight <= 0) {
                console.warn("Selection has zero or negative dimensions for rotation.");
                return;
            }

            // 1. Buffer the original selection content
            const originalContentBuffer = [];
            if (activeLayer.pixels) {
                for (let r = 0; r < originalSelHeight; r++) {
                    const layerY = selY0 + r;
                    const bufferRow = Array(originalSelWidth).fill(EMPTY_COLOR);
                    if (layerY >= 0 && layerY < currentGridSize && activeLayer.pixels[layerY]) {
                        for (let c = 0; c < originalSelWidth; c++) {
                            const layerX = selX0 + c;
                            if (layerX >= 0 && layerX < currentGridSize) {
                                bufferRow[c] = activeLayer.pixels[layerY][layerX] || EMPTY_COLOR;
                            }
                        }
                    }
                    originalContentBuffer.push(bufferRow);
                }
            } else {
                return;
            }

            // 2. Clear the original selection area on the active layer
            for (let y = selY0; y <= selY1; y++) {
                if (y >= 0 && y < currentGridSize && activeLayer.pixels[y]) {
                    for (let x = selX0; x <= selX1; x++) {
                        if (x >= 0 && x < currentGridSize) {
                            if (activeLayer.pixels[y][x] !== EMPTY_COLOR && activeLayer.pixels[y][x] !== undefined) {
                                activeLayer.pixels[y][x] = EMPTY_COLOR;
                                expandDirtyRect(x, y);
                            }
                        }
                    }
                }
            }

            let rotatedContentBuffer;
            let newSelWidth = originalSelWidth;
            let newSelHeight = originalSelHeight;

            if (degrees === 90) {
                newSelWidth = originalSelHeight;
                newSelHeight = originalSelWidth;
                rotatedContentBuffer = Array(newSelHeight).fill(null).map(() => Array(newSelWidth).fill(EMPTY_COLOR));
                for (let r = 0; r < originalSelHeight; r++) { // original rows
                    for (let c = 0; c < originalSelWidth; c++) { // original columns
                        // For 90 deg clockwise: newX = r; newY = (originalWidth - 1) - c; (if pivot is top-left of source)
                        // For our buffer: source pixel (c, r) from originalContentBuffer goes to (r, originalSelWidth - 1 - c) in rotatedContentBuffer
                        if (originalContentBuffer[r] && originalContentBuffer[r][c] !== undefined) {
                            if (r < newSelWidth && (originalSelWidth - 1 - c) < newSelHeight) { // Check bounds for rotated buffer
                                rotatedContentBuffer[originalSelWidth - 1 - c][r] = originalContentBuffer[r][c];
                            }
                        }
                    }
                }
            } else if (degrees === 180) {
                // newSelWidth and newSelHeight remain the same
                rotatedContentBuffer = Array(newSelHeight).fill(null).map(() => Array(newSelWidth).fill(EMPTY_COLOR));
                for (let r = 0; r < originalSelHeight; r++) {
                    for (let c = 0; c < originalSelWidth; c++) {
                        // For 180 deg: newX = (originalWidth - 1) - c; newY = (originalHeight - 1) - r;
                        if (originalContentBuffer[r] && originalContentBuffer[r][c] !== undefined) {
                            if ((originalSelHeight - 1 - r) < newSelHeight && (originalSelWidth - 1 - c) < newSelWidth) { // Check bounds
                                rotatedContentBuffer[originalSelHeight - 1 - r][originalSelWidth - 1 - c] = originalContentBuffer[r][c];
                            }
                        }
                    }
                }
            } else {
                console.warn("Unsupported rotation angle:", degrees);
                return; // Or paste original back if desired
            }

            // 3. Paste the rotated buffer back, ensuring it fits the canvas
            const finalPasteX0 = selX0;
            const finalPasteY0 = selY0;
            let finalDrawableWidth = newSelWidth;
            let finalDrawableHeight = newSelHeight;

            // Adjust drawable dimensions if pasted selection would go off-canvas
            if (finalPasteX0 + newSelWidth > currentGridSize) {
                finalDrawableWidth = Math.max(0, currentGridSize - finalPasteX0);
            }
            if (finalPasteY0 + newSelHeight > currentGridSize) {
                finalDrawableHeight = Math.max(0, currentGridSize - finalPasteY0);
            }

            finalDrawableWidth = Math.max(0, finalDrawableWidth);
            finalDrawableHeight = Math.max(0, finalDrawableHeight);

            if (finalDrawableWidth > 0 && finalDrawableHeight > 0 && rotatedContentBuffer && rotatedContentBuffer.length > 0) {
                let bufferToPaste = [];
                // Extract the drawable part of the rotated buffer
                for (let r = 0; r < finalDrawableHeight; r++) {
                    if (r < newSelHeight && rotatedContentBuffer[r]) { // Check source row exists
                        bufferToPaste.push(rotatedContentBuffer[r].slice(0, finalDrawableWidth));
                    } else {
                        bufferToPaste.push(Array(finalDrawableWidth).fill(EMPTY_COLOR));
                    }
                }
                currentSelectionPasteMode = 'PASTE_MODE_REPLACE_ALL'; // Ensure it overwrites
                _pasteSelectionBuffer(bufferToPaste, finalPasteX0, finalPasteY0);
            }

            // 4. Update selection marquee
            if (finalDrawableWidth > 0 && finalDrawableHeight > 0) {
                currentSelectionStart = { x: finalPasteX0, y: finalPasteY0 };
                currentSelectionEnd = {
                    x: finalPasteX0 + finalDrawableWidth - 1,
                    y: finalPasteY0 + finalDrawableHeight - 1
                };
                isSelecting = true;
            } else { // Selection becomes empty if it's rotated off-canvas or becomes zero-size
                currentSelectionStart = null;
                currentSelectionEnd = null;
                isSelecting = false;
            }

            if (canvases[activeCanvasIndex]) {
                canvases[activeCanvasIndex].selectionStart = currentSelectionStart ? { ...currentSelectionStart } : null;
                canvases[activeCanvasIndex].selectionEnd = currentSelectionEnd ? { ...currentSelectionEnd } : null;
                canvases[activeCanvasIndex].isSelecting = isSelecting;
            }

            updateBrushOptionsBarContent();
            requestFullRedraw();
        }

        function handleScaleSelection(scaleFactor) {
            if (toolSelector.value !== 'select' || !isSelecting || !currentSelectionStart || !currentSelectionEnd || !activeLayer || !activeLayer.isVisible) {
                console.warn("Scale preconditions not met.");
                return;
            }

            saveState();

            const selX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
            const selY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
            const selX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
            const selY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);

            const originalSelWidth = selX1 - selX0 + 1;
            const originalSelHeight = selY1 - selY0 + 1;

            if (originalSelWidth <= 0 || originalSelHeight <= 0) {
                console.warn("Selection has zero or negative dimensions.");
                return;
            }

            const originalContentBuffer = [];
            if (activeLayer.pixels) {
                for (let r = 0; r < originalSelHeight; r++) {
                    const layerY = selY0 + r;
                    const bufferRow = Array(originalSelWidth).fill(EMPTY_COLOR);
                    if (layerY >= 0 && layerY < currentGridSize && activeLayer.pixels[layerY]) {
                        for (let c = 0; c < originalSelWidth; c++) {
                            const layerX = selX0 + c;
                            if (layerX >= 0 && layerX < currentGridSize) {
                                bufferRow[c] = activeLayer.pixels[layerY][layerX] || EMPTY_COLOR;
                            }
                        }
                    }
                    originalContentBuffer.push(bufferRow);
                }
            } else {
                return; // Should not happen if activeLayer is valid
            }

            for (let y = selY0; y <= selY1; y++) {
                if (y >= 0 && y < currentGridSize && activeLayer.pixels[y]) {
                    for (let x = selX0; x <= selX1; x++) {
                        if (x >= 0 && x < currentGridSize) {
                            if (activeLayer.pixels[y][x] !== EMPTY_COLOR && activeLayer.pixels[y][x] !== undefined) {
                                activeLayer.pixels[y][x] = EMPTY_COLOR;
                                expandDirtyRect(x, y);
                            }
                        }
                    }
                }
            }

            let targetNewWidth = Math.round(originalSelWidth * scaleFactor);
            let targetNewHeight = Math.round(originalSelHeight * scaleFactor);
            targetNewWidth = Math.max(MIN_SELECTION_DIM, targetNewWidth);
            targetNewHeight = Math.max(MIN_SELECTION_DIM, targetNewHeight);

            const scaledContentBuffer = resampleSelectionBuffer(
                originalContentBuffer, originalSelWidth, originalSelHeight, targetNewWidth, targetNewHeight
            );

            const finalPasteX0 = selX0;
            const finalPasteY0 = selY0;
            let finalDrawableWidth = targetNewWidth;
            let finalDrawableHeight = targetNewHeight;

            if (finalPasteX0 < 0) {
                finalDrawableWidth = Math.max(0, targetNewWidth + finalPasteX0);
            } else if (finalPasteX0 + targetNewWidth > currentGridSize) {
                finalDrawableWidth = Math.max(0, currentGridSize - finalPasteX0);
            }
            if (finalPasteY0 < 0) {
                finalDrawableHeight = Math.max(0, targetNewHeight + finalPasteY0);
            } else if (finalPasteY0 + targetNewHeight > currentGridSize) {
                finalDrawableHeight = Math.max(0, currentGridSize - finalPasteY0);
            }

            finalDrawableWidth = Math.max(0, finalDrawableWidth); // Ensure non-negative
            finalDrawableHeight = Math.max(0, finalDrawableHeight);


            if (finalDrawableWidth > 0 && finalDrawableHeight > 0 && scaledContentBuffer && scaledContentBuffer.length > 0) {
                let bufferToPaste = scaledContentBuffer;
                // If the drawable area is smaller than the target scaled buffer, we need to extract the correct sub-section.
                if (targetNewWidth !== finalDrawableWidth || targetNewHeight !== finalDrawableHeight) {
                    bufferToPaste = [];
                    for (let r = 0; r < finalDrawableHeight; r++) {
                        let sourceR = (finalPasteY0 < 0) ? r - finalPasteY0 : r; // Offset if original selection started off-canvas
                        if (sourceR >= 0 && sourceR < targetNewHeight && scaledContentBuffer[sourceR]) {
                            let newRow = Array(finalDrawableWidth).fill(EMPTY_COLOR);
                            for (let c = 0; c < finalDrawableWidth; c++) {
                                let sourceC = (finalPasteX0 < 0) ? c - finalPasteX0 : c;
                                if (sourceC >= 0 && sourceC < targetNewWidth && scaledContentBuffer[sourceR][sourceC] !== undefined) {
                                    newRow[c] = scaledContentBuffer[sourceR][sourceC];
                                }
                            }
                            bufferToPaste.push(newRow);
                        } else {
                            bufferToPaste.push(Array(finalDrawableWidth).fill(EMPTY_COLOR));
                        }
                    }
                }

                const actualPasteX0 = Math.max(0, finalPasteX0); // Ensure paste starts on canvas
                const actualPasteY0 = Math.max(0, finalPasteY0);

                currentSelectionPasteMode = 'PASTE_MODE_REPLACE_ALL';
                _pasteSelectionBuffer(bufferToPaste, actualPasteX0, actualPasteY0);
            }

            if (finalDrawableWidth > 0 && finalDrawableHeight > 0) {
                currentSelectionStart = { x: Math.max(0, finalPasteX0), y: Math.max(0, finalPasteY0) };
                currentSelectionEnd = {
                    x: Math.max(0, finalPasteX0) + finalDrawableWidth - 1,
                    y: Math.max(0, finalPasteY0) + finalDrawableHeight - 1
                };
                isSelecting = true;
            } else {
                currentSelectionStart = null;
                currentSelectionEnd = null;
                isSelecting = false;
            }

            if (canvases[activeCanvasIndex]) {
                canvases[activeCanvasIndex].selectionStart = currentSelectionStart ? { ...currentSelectionStart } : null;
                canvases[activeCanvasIndex].selectionEnd = currentSelectionEnd ? { ...currentSelectionEnd } : null;
                canvases[activeCanvasIndex].isSelecting = isSelecting;
            }

            updateBrushOptionsBarContent();
            requestFullRedraw();
        }
        function redrawFullView() {
            renderViewport();
            gridCtx.clearRect(0, 0, gridCanvas.width, gridCanvas.height);
            drawGrid();
            if (toolSelector.value === 'select') {
                // REMOVED: isResizingSelection block
                if (isMovingSelection && currentSelectionStart && currentSelectionEnd) {
                    drawMovingSelectionPreview();
                    applyOcclusionMaskForMovingSelection(); // Ensure occlusion is applied here too
                } else if (isSelecting && currentSelectionStart && currentSelectionEnd) {
                    drawSelectionBox();
                }
            }
            // Removed: drawLiveStrokePreviewOnGrid();
            resetDirtyRect();
            isCanvasDirtyForTools = false;
        }

        function drawSinglePixelCell(ctx, gX, gY, colorString) {
            if (colorString === EMPTY_COLOR || colorString === undefined) return;
            
            // Calculate the exact screen position for this cell
            const screenXStart = (gX - currentViewOffsetX) * renderedCellSize;
            const screenYStart = (gY - currentViewOffsetY) * renderedCellSize;
            
            // Calculate the exact screen position for the next cell
            const screenXEnd = ((gX + 1) - currentViewOffsetX) * renderedCellSize;
            const screenYEnd = ((gY + 1) - currentViewOffsetY) * renderedCellSize;
            
            // Round to get exact pixel boundaries - this ensures no gaps or overlaps
            const drawX = Math.round(screenXStart);
            const drawY = Math.round(screenYStart);
            const drawRight = Math.round(screenXEnd);
            const drawBottom = Math.round(screenYEnd);
            
            // Calculate width and height from the rounded boundaries
            const drawWidth = drawRight - drawX;
            const drawHeight = drawBottom - drawY;
            
            // Only draw if we have a valid size
            if (drawWidth > 0 && drawHeight > 0) {
                ctx.fillStyle = colorString;
                ctx.fillRect(drawX, drawY, drawWidth, drawHeight);
            }
        }
        function handleFlipSelection(axis) {
            if (toolSelector.value !== 'select' || !isSelecting || !currentSelectionStart || !currentSelectionEnd || !activeLayer || !activeLayer.isVisible) {
                // console.warn("Flip: Preconditions not met (no selection, select tool inactive, or no visible active layer).");
                return;
            }
            if (!activeLayer.pixels) {
                // console.warn("Flip: Active layer has no pixel data.");
                return; // Nothing to flip
            }

            saveState(); // Save state before modifying pixels

            const selX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
            const selY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
            const selX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
            const selY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);

            const selWidth = selX1 - selX0 + 1;
            const selHeight = selY1 - selY0 + 1;

            if (selWidth <= 0 || selHeight <= 0) {
                // console.warn("Flip: Invalid selection dimensions.");
                return;
            }

            // 1. Create a buffer of the selected area's pixels
            const originalBuffer = [];
            for (let r = 0; r < selHeight; r++) {
                const currentLayerY = selY0 + r;
                const bufferRow = [];
                // Ensure the source row exists before trying to access it
                if (currentLayerY >= 0 && currentLayerY < currentGridSize && activeLayer.pixels[currentLayerY]) {
                    for (let c = 0; c < selWidth; c++) {
                        const currentLayerX = selX0 + c;
                        if (currentLayerX >= 0 && currentLayerX < currentGridSize) {
                            bufferRow.push(activeLayer.pixels[currentLayerY][currentLayerX] || EMPTY_COLOR);
                        } else {
                            bufferRow.push(EMPTY_COLOR); // Should be caught by selection bounds, but defensive
                        }
                    }
                } else { // Row is effectively outside canvas or doesn't exist on layer
                    for (let c = 0; c < selWidth; c++) {
                        bufferRow.push(EMPTY_COLOR);
                    }
                }
                originalBuffer.push(bufferRow);
            }

            // 2. Create the flippedBuffer (which will be modified)
            // Deep copy is essential here if originalBuffer might be used elsewhere,
            // or if flip operations are complex. For simple reverse, it might not be strictly needed
            // if we paste from a new structure, but good practice.
            let flippedBuffer = JSON.parse(JSON.stringify(originalBuffer));

            // 3. Perform the flip on flippedBuffer
            if (axis === 'horizontal') {
                for (let r = 0; r < selHeight; r++) {
                    flippedBuffer[r].reverse(); // Reverses the row in place
                }
            } else if (axis === 'vertical') {
                flippedBuffer.reverse(); // Reverses the array of rows in place
            }

            // 4. Paste the flippedBuffer back onto the activeLayer at the original location
            // This directly modifies activeLayer.pixels.
            let changed = false;
            for (let r_buffer = 0; r_buffer < selHeight; r_buffer++) { // Iterate through buffer rows
                const currentLayerY = selY0 + r_buffer;
                if (currentLayerY >= 0 && currentLayerY < currentGridSize) { // Ensure target row is on canvas
                    if (!activeLayer.pixels[currentLayerY]) { // Ensure target layer row exists
                        activeLayer.pixels[currentLayerY] = Array(currentGridSize).fill(EMPTY_COLOR);
                    }
                    for (let c_buffer = 0; c_buffer < selWidth; c_buffer++) { // Iterate through buffer columns
                        const currentLayerX = selX0 + c_buffer;
                        if (currentLayerX >= 0 && currentLayerX < currentGridSize) { // Ensure target col is on canvas
                            const newPixel = flippedBuffer[r_buffer][c_buffer];
                            if (activeLayer.pixels[currentLayerY][currentLayerX] !== newPixel) {
                                activeLayer.pixels[currentLayerY][currentLayerX] = newPixel;
                                expandDirtyRect(currentLayerX, currentLayerY);
                                changed = true;
                            }
                        }
                    }
                }
            }

            if (changed) {
                requestFullRedraw();
            }
            // The selection marquee remains over the same area.
        }
        function handleDeleteSelection() {
            if (toolSelector.value !== 'select' || !isSelecting || !currentSelectionStart || !currentSelectionEnd || !activeLayer || !activeLayer.isVisible) {
                return false;
            }
            saveState();
            const selX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
            const selY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
            const selX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
            const selY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);
            let pixelsCleared = false;
            if (activeLayer.pixels) {
                for (let y = selY0; y <= selY1; y++) {
                    if (y >= 0 && y < currentGridSize && activeLayer.pixels[y]) {
                        for (let x = selX0; x <= selX1; x++) {
                            if (x >= 0 && x < currentGridSize) {
                                if (activeLayer.pixels[y][x] !== EMPTY_COLOR && activeLayer.pixels[y][x] !== undefined) {
                                    activeLayer.pixels[y][x] = EMPTY_COLOR;
                                    expandDirtyRect(x, y);
                                    pixelsCleared = true;
                                }
                            }
                        }
                    }
                }
            }
            if (pixelsCleared) {
                // Clear the selection after deleting content
                currentSelectionStart = null;
                currentSelectionEnd = null;
                isSelecting = false;

                // Also clear selection from canvas data
                if (canvases[activeCanvasIndex]) {
                    canvases[activeCanvasIndex].selectionStart = null;
                    canvases[activeCanvasIndex].selectionEnd = null;
                    canvases[activeCanvasIndex].isSelecting = false;
                }

                // Update UI to reflect no selection
                updateBrushOptionsBarContent();
                requestFullRedraw();
            }
            return pixelsCleared;
        }
        function handleCopySelection() {
            if (toolSelector.value !== 'select' || !currentSelectionStart || !currentSelectionEnd || !activeLayer) {
                return;
            }
            const selX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
            const selY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
            const selX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
            const selY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);
            const selWidth = selX1 - selX0 + 1;
            const selHeight = selY1 - selY0 + 1;

            if (selWidth <= 0 || selHeight <= 0) {
                clipboardBuffer = null;
                clipboardBufferDimensions = { width: 0, height: 0 };
                return;
            }
            const tempBuffer = [];
            for (let r = 0; r < selHeight; r++) {
                const sourceY = selY0 + r;
                let bufferRow = [];
                for (let c = 0; c < selWidth; c++) {
                    const sourceX = selX0 + c;
                    let pixelData = EMPTY_COLOR;
                    if (sourceY >= 0 && sourceY < currentGridSize &&
                        sourceX >= 0 && sourceX < currentGridSize &&
                        activeLayer.pixels && activeLayer.pixels[sourceY]) {
                        pixelData = activeLayer.pixels[sourceY][sourceX] || EMPTY_COLOR;
                    }
                    bufferRow.push(pixelData);
                }
                tempBuffer.push(bufferRow);
            }
            clipboardBuffer = tempBuffer;
            clipboardBufferDimensions = { width: selWidth, height: selHeight };
        }

        function handlePasteSelection() {
            if (isMovingSelection) {
                finalizeMoveSelection();
                if (isMovingSelection) { return; }
            }
            if (!clipboardBuffer || clipboardBufferDimensions.width === 0 || clipboardBufferDimensions.height === 0) { return; }

            const bufferWidth = clipboardBufferDimensions.width;
            const bufferHeight = clipboardBufferDimensions.height;
            let pasteX, pasteY;

            const currentRenderedCellSize = renderedCellSize > 0.001 ? renderedCellSize : 1;
            const viewportWidthInCells = pixelCanvas.width / currentRenderedCellSize;
            const viewportHeightInCells = pixelCanvas.height / currentRenderedCellSize;

            pasteX = Math.floor(currentViewOffsetX + (viewportWidthInCells / 2) - (bufferWidth / 2));
            pasteY = Math.floor(currentViewOffsetY + (viewportHeightInCells / 2) - (bufferHeight / 2));

            pasteX = Math.max(0, Math.min(pasteX, currentGridSize - bufferWidth >= 0 ? currentGridSize - bufferWidth : 0));
            pasteY = Math.max(0, Math.min(pasteY, currentGridSize - bufferHeight >= 0 ? currentGridSize - bufferHeight : 0));
            pasteX = Math.max(0, pasteX);
            pasteY = Math.max(0, pasteY);

            isMovingSelection = true;
            movingSelectionBuffer = JSON.parse(JSON.stringify(clipboardBuffer));
            movingSelectionCurrentTopLeft = { x: pasteX, y: pasteY };
            movingSelectionDelta = null; // Mousemove will use this to detect initial centering
            currentSelectionPasteMode = 'PASTE_MODE_IGNORE_TRANSPARENT';
            isInitialPasteFloat = true;

            currentSelectionStart = { x: pasteX, y: pasteY };
            currentSelectionEnd = { x: pasteX + bufferWidth - 1, y: pasteY + bufferHeight - 1 };
            isSelecting = true;

            if (canvases[activeCanvasIndex]) {
                canvases[activeCanvasIndex].selectionStart = { ...currentSelectionStart };
                canvases[activeCanvasIndex].selectionEnd = { ...currentSelectionEnd };
                canvases[activeCanvasIndex].isSelecting = true;
            }
            originalSelectionBeforeMove = { start: { ...currentSelectionStart }, end: { ...currentSelectionEnd } };
            requestFullRedraw();
            updateCursor();
        }
        function renderViewport() {
            pixelCtx.clearRect(0, 0, pixelCanvas.width, pixelCanvas.height);
            if (renderedCellSize <= 0 || currentGridSize <= 0 || !currentLayers || currentLayers.length === 0) return;
            
            // Pre-calculate viewport boundaries to avoid repeated calculations
            const startX = Math.max(0, Math.floor(currentViewOffsetX));
            const startY = Math.max(0, Math.floor(currentViewOffsetY));
            const endX = Math.min(currentGridSize - 1, Math.ceil(currentViewOffsetX + pixelCanvas.width / renderedCellSize));
            const endY = Math.min(currentGridSize - 1, Math.ceil(currentViewOffsetY + pixelCanvas.height / renderedCellSize));
            
            // Set the composite operation to ensure pixels don't blend at edges
            const originalCompositeOperation = pixelCtx.globalCompositeOperation;
            pixelCtx.globalCompositeOperation = 'source-over';
            
            // Batch rendering by using a single path for each layer
            for (const layer of currentLayers) {
                if (!layer.isVisible || !layer.pixels) continue;
                pixelCtx.globalAlpha = layer.opacity / 100;
                
                // For large canvases, use optimized rendering approach
                if (currentGridSize >= 256) {
                    // Batch similar colored pixels for better performance
                    let currentColor = null;
                    
                    for (let gY = startY; gY <= endY; gY++) {
                        if (!layer.pixels[gY]) continue;
                       
                        for (let gX = startX; gX <= endX; gX++) {
                            const pixelData = layer.pixels[gY][gX];
                            if (pixelData !== EMPTY_COLOR && pixelData !== undefined) {
                                if (pixelData !== currentColor) {
                                    pixelCtx.fillStyle = pixelData;
                                    currentColor = pixelData;
                                }
                                
                                // Calculate the exact screen position for this cell
                                const screenXStart = (gX - currentViewOffsetX) * renderedCellSize;
                                const screenYStart = (gY - currentViewOffsetY) * renderedCellSize;
                                
                                // Calculate the exact screen position for the next cell
                                const screenXEnd = ((gX + 1) - currentViewOffsetX) * renderedCellSize;
                                const screenYEnd = ((gY + 1) - currentViewOffsetY) * renderedCellSize;
                                
                                // Round to get exact pixel boundaries - this ensures no gaps or overlaps
                                const drawX = Math.round(screenXStart);
                                const drawY = Math.round(screenYStart);
                                const drawRight = Math.round(screenXEnd);
                                const drawBottom = Math.round(screenYEnd);
                                
                                // Calculate width and height from the rounded boundaries
                                const drawWidth = drawRight - drawX;
                                const drawHeight = drawBottom - drawY;
                                
                                // Only draw if we have a valid size
                                if (drawWidth > 0 && drawHeight > 0) {
                                    pixelCtx.fillRect(drawX, drawY, drawWidth, drawHeight);
                                }
                            }
                        }
                    }
                } else {
                    // For smaller canvases, use the modified approach with integer pixel sizes
                    for (let gY = startY; gY <= endY; gY++) {
                        if (!layer.pixels[gY]) continue;
                        for (let gX = startX; gX <= endX; gX++) {
                            const pixelData = layer.pixels[gY][gX];
                            if (pixelData !== EMPTY_COLOR && pixelData !== undefined) {
                                // Just use our helper function for consistent rendering
                                drawSinglePixelCell(pixelCtx, gX, gY, pixelData);
                            }
                        }
                    }
                }
            }
            
            // Reset the canvas state
            pixelCtx.globalAlpha = 1.0;
            pixelCtx.globalCompositeOperation = originalCompositeOperation;
        }

        function renderDirtyRectInViewport() {
            if (dirtyRect.minX > dirtyRect.maxX || dirtyRect.minY > dirtyRect.maxY || renderedCellSize <= 0 || !currentLayers) return;
            
            // Calculate clear area
            const clearScreenXStart = (dirtyRect.minX - currentViewOffsetX) * renderedCellSize;
            const clearScreenYStart = (dirtyRect.minY - currentViewOffsetY) * renderedCellSize;
            const clearScreenXEnd = (dirtyRect.maxX + 1 - currentViewOffsetX) * renderedCellSize;
            const clearScreenYEnd = (dirtyRect.maxY + 1 - currentViewOffsetY) * renderedCellSize;
            const clearDrawX = Math.round(clearScreenXStart);
            const clearDrawY = Math.round(clearScreenYStart);
            const clearDrawWidth = Math.round(clearScreenXEnd) - clearDrawX;
            const clearDrawHeight = Math.round(clearScreenYEnd) - clearDrawY;
            
            // Only clear if there's a valid area to clear
            if (clearDrawWidth > 0 && clearDrawHeight > 0) {
                pixelCtx.clearRect(clearDrawX, clearDrawY, clearDrawWidth, clearDrawHeight);
            }
            
            // Pre-calculate boundaries for the dirty rect
            const minY = Math.max(0, dirtyRect.minY);
            const maxY = Math.min(currentGridSize - 1, dirtyRect.maxY);
            const minX = Math.max(0, dirtyRect.minX);
            const maxX = Math.min(currentGridSize - 1, dirtyRect.maxX);
            
            // Check if the dirty rect is visible in the viewport
            const isRectVisible = !(
                (minX - currentViewOffsetX) * renderedCellSize > pixelCanvas.width ||
                (maxX + 1 - currentViewOffsetX) * renderedCellSize < 0 ||
                (minY - currentViewOffsetY) * renderedCellSize > pixelCanvas.height ||
                (maxY + 1 - currentViewOffsetY) * renderedCellSize < 0
            );
            
            if (!isRectVisible) return;
            
            // Set the composite operation to ensure pixels don't blend at edges
            const originalCompositeOperation = pixelCtx.globalCompositeOperation;
            pixelCtx.globalCompositeOperation = 'source-over';
            
            // For large canvases, use a more optimized approach
            const isLargeCanvas = currentGridSize >= 256;
            
            for (const layer of currentLayers) {
                if (!layer.isVisible || !layer.pixels) continue;
                pixelCtx.globalAlpha = layer.opacity / 100;
                
                if (isLargeCanvas) {
                    // Batch rendering by color for large canvases
                    let currentColor = null;
                    
                    for (let gY = minY; gY <= maxY; gY++) {
                        if (!layer.pixels[gY]) continue;
                        
                        const screenY_start = (gY - currentViewOffsetY) * renderedCellSize;
                        const screenY_end = (gY + 1 - currentViewOffsetY) * renderedCellSize;
                        if (Math.round(screenY_end) < 0 || Math.round(screenY_start) > pixelCanvas.height) continue;
                        
                        for (let gX = minX; gX <= maxX; gX++) {
                            const screenX_start = (gX - currentViewOffsetX) * renderedCellSize;
                            const screenX_end = (gX + 1 - currentViewOffsetX) * renderedCellSize;
                            if (Math.round(screenX_end) < 0 || Math.round(screenX_start) > pixelCanvas.width) continue;
                            
                            const pixelData = layer.pixels[gY][gX];
                            if (pixelData !== EMPTY_COLOR && pixelData !== undefined) {
                                if (pixelData !== currentColor) {
                                    pixelCtx.fillStyle = pixelData;
                                    currentColor = pixelData;
                                }
                                
                                // Calculate the exact screen position for the next cell
                                const screenX_end = ((gX + 1) - currentViewOffsetX) * renderedCellSize;
                                const screenY_end = ((gY + 1) - currentViewOffsetY) * renderedCellSize;
                                
                                // Round to get exact pixel boundaries - this ensures no gaps or overlaps
                                const drawX = Math.round(screenX_start);
                                const drawY = Math.round(screenY_start);
                                const drawRight = Math.round(screenX_end);
                                const drawBottom = Math.round(screenY_end);
                                
                                // Calculate width and height from the rounded boundaries
                                const drawWidth = drawRight - drawX;
                                const drawHeight = drawBottom - drawY;
                                
                                // Only draw if we have a valid size
                                if (drawWidth > 0 && drawHeight > 0) {
                                    pixelCtx.fillRect(drawX, drawY, drawWidth, drawHeight);
                                }
                            }
                        }
                    }
                } else {
                    // Modified approach for smaller canvases
                    for (let gY = minY; gY <= maxY; gY++) {
                        if (!layer.pixels[gY]) continue;
                        const screenY_start = (gY - currentViewOffsetY) * renderedCellSize;
                        const screenY_end = (gY + 1 - currentViewOffsetY) * renderedCellSize;
                        if (Math.round(screenY_end) < 0 || Math.round(screenY_start) > pixelCanvas.height) continue;
                        
                        for (let gX = minX; gX <= maxX; gX++) {
                            const screenX_start = (gX - currentViewOffsetX) * renderedCellSize;
                            const screenX_end = (gX + 1 - currentViewOffsetX) * renderedCellSize;
                            if (Math.round(screenX_end) < 0 || Math.round(screenX_start) > pixelCanvas.width) continue;
                            
                            const pixelData = layer.pixels[gY][gX];
                            if (pixelData !== EMPTY_COLOR && pixelData !== undefined) {
                                // Just use our helper function for consistent rendering
                                drawSinglePixelCell(pixelCtx, gX, gY, pixelData);
                            }
                        }
                    }
                }
            }
            
            // Reset the canvas state
            pixelCtx.globalAlpha = 1.0;
            pixelCtx.globalCompositeOperation = originalCompositeOperation;
        }

        function resizeCanvases(){
            const tbEl=toolbar,edContEl=editorContainer,brOptEl=brushOptionsBar,cvTabsEl=canvasTabsBar,cvContEl=canvasContainer,rPanelsEl=rightPanels;const edCMT=edContEl?parseInt(window.getComputedStyle(edContEl).marginTop)||0:0,bP=10,edCBM=20,cvTH=cvTabsEl?cvTabsEl.offsetHeight:0;let brBWAM=0;if(brOptEl){const brS=window.getComputedStyle(brOptEl);brBWAM=brOptEl.offsetWidth+(parseInt(brS.marginLeft)||0)+(parseInt(brS.marginRight)||0)}const rPWAM=rPanelsEl?rPanelsEl.offsetWidth+(parseInt(window.getComputedStyle(rPanelsEl).marginLeft)||0)+(parseInt(window.getComputedStyle(rPanelsEl).marginRight)||0):280;const tWW=window.innerWidth-(bP*2),tbS=window.getComputedStyle(tbEl),tbVM=(parseInt(tbS.marginTop)||0)+(parseInt(tbS.marginBottom)||0);const aHFEC=window.innerHeight-(tbEl.offsetHeight+tbVM)-edCMT-edCBM,aHFCAP=aHFEC-cvTH-5;let tCDS=Math.min(aHFCAP,tWW-brBWAM-rPWAM);tCDS=Math.max(64,tCDS);cvContEl.style.width=tCDS+'px';cvContEl.style.height=tCDS+'px';const cAAH=canvasArea.offsetHeight;if(brOptEl)brOptEl.style.height=cAAH+'px';if(rPanelsEl)rPanelsEl.style.height=cAAH+'px';const aDW=cvContEl.clientWidth,aDH=cvContEl.clientHeight;pixelCanvas.width=aDW>0?aDW:1;pixelCanvas.height=aDH>0?aDH:1;gridCanvas.width=aDW>0?aDW:1;gridCanvas.height=aDH>0?aDH:1;minFitCellSize=currentGridSize>0&&aDW>0?aDW/currentGridSize:1;if(zoomSlider&&zoomSliderContainer){const sCH=zoomSliderContainer.clientHeight;zoomSlider.style.height=(sCH>40?sCH-20:sCH)+'px'}const cAAW=canvasArea.offsetWidth,eCW=brBWAM+cAAW+rPWAM-(parseInt(window.getComputedStyle(rPanelsEl).marginLeft)||0);toolbar.style.width=eCW+'px';updateZoom()
        }
        function updateZoom(keepCenter = false) {
            let oldRenderedCellSize = renderedCellSize;
            let currentDisplayWidth = gridCanvas.width;
            let currentDisplayHeight = gridCanvas.height;
            let viewportCenterGX, viewportCenterGY;
            if (keepCenter && oldRenderedCellSize > 0 && currentDisplayWidth > 0 && currentDisplayHeight > 0 && currentGridSize > 0) {
                viewportCenterGX = currentViewOffsetX + (currentDisplayWidth / oldRenderedCellSize) / 2;
                viewportCenterGY = currentViewOffsetY + (currentDisplayHeight / oldRenderedCellSize) / 2;
            }
            if (currentGridSize > 0 && minFitCellSize > 0) renderedCellSize = minFitCellSize * currentZoomFactor;
            else renderedCellSize = (currentDisplayWidth > 0 ? currentDisplayWidth : 1) * currentZoomFactor;
            renderedCellSize = Math.max(0.001, renderedCellSize);
            if (Math.abs(currentZoomFactor - 1.0) < 0.01 && !keepCenter) {
                currentViewOffsetX = 0; currentViewOffsetY = 0;
                if (currentGridSize > 0 && minFitCellSize > 0) renderedCellSize = minFitCellSize;
            } else if (keepCenter && viewportCenterGX !== undefined && viewportCenterGY !== undefined && renderedCellSize > 0) {
                currentViewOffsetX = viewportCenterGX - (currentDisplayWidth / renderedCellSize) / 2;
                currentViewOffsetY = viewportCenterGY - (currentDisplayHeight / renderedCellSize) / 2;
            }
            clampViewOffset(); updateCursor(); requestFullRedraw();
        }
        function clampViewOffset() {
            if (renderedCellSize <= 0 || currentGridSize <= 0 || !gridCanvas || gridCanvas.width === 0 || gridCanvas.height === 0) {
                currentViewOffsetX = 0;
                currentViewOffsetY = 0;
                return;
            }

            const cellsCanFitX = gridCanvas.width / renderedCellSize;
            const cellsCanFitY = gridCanvas.height / renderedCellSize;

            // If the content is smaller than or fits perfectly in the viewport, no panning is allowed (offset is 0)
            if (currentGridSize <= cellsCanFitX + 0.001) { // Added tolerance
                currentViewOffsetX = 0;
            } else {
                const maxPossibleViewOffsetX = currentGridSize - cellsCanFitX;
                currentViewOffsetX = Math.max(0, Math.min(currentViewOffsetX, maxPossibleViewOffsetX));
            }

            if (currentGridSize <= cellsCanFitY + 0.001) { // Added tolerance
                currentViewOffsetY = 0;
            } else {
                const maxPossibleViewOffsetY = currentGridSize - cellsCanFitY;
                currentViewOffsetY = Math.max(0, Math.min(currentViewOffsetY, maxPossibleViewOffsetY));
            }
        }
        function saveState() {
            if (!activeLayer || !activeLayer.pixels) { // Use the global activeLayer
                console.error("saveState: No active layer or pixel data to save.");
                return;
            }
            
            const undoState = {
                layerIndex: currentActiveLayerIndex,
                pixels: JSON.parse(JSON.stringify(activeLayer.pixels))
            };
            
            addToUndoStack(undoState);
        }
        function drawSelectionBox() {
            if (!currentSelectionStart || !currentSelectionEnd || renderedCellSize <= 0) return;
            // Ensure currentSelectionStart and currentSelectionEnd are valid
            if (typeof currentSelectionStart.x !== 'number' || typeof currentSelectionStart.y !== 'number' ||
                typeof currentSelectionEnd.x !== 'number' || typeof currentSelectionEnd.y !== 'number') {
                // console.warn("drawSelectionBox called with invalid currentSelectionStart/End");
                return;
            }


            const selLogicX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
            const selLogicY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
            const selLogicX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
            const selLogicY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);

            if (selLogicX1 < selLogicX0 || selLogicY1 < selLogicY0) return;

            const screenX = (selLogicX0 - currentViewOffsetX) * renderedCellSize;
            const screenY = (selLogicY0 - currentViewOffsetY) * renderedCellSize;
            const screenW = (selLogicX1 - selLogicX0 + 1) * renderedCellSize;
            const screenH = (selLogicY1 - selLogicY0 + 1) * renderedCellSize;

            if (screenX < gridCanvas.width && screenY < gridCanvas.height && screenX + screenW > 0 && screenY + screenH > 0) {
                gridCtx.strokeStyle = 'rgba(255,0,0,0.8)';
                gridCtx.lineWidth = 1;
                gridCtx.setLineDash([3, 3]);
                gridCtx.strokeRect(Math.round(screenX) + 0.5, Math.round(screenY) + 0.5, Math.max(0, Math.round(screenW) - 1), Math.max(0, Math.round(screenH) - 1));
                gridCtx.setLineDash([]);

                // Draw resize handles if selection is static (not moving or being created)
                if (toolSelector.value === 'select' && isSelecting && !isMovingSelection && !drawing) {
                    drawSelectionHandles(screenX, screenY, screenW, screenH);
                }
                // REMOVE THIS CALL:
                // if (toolSelector.value === 'select' && isSelecting && !isMovingSelection && !isResizingSelection) {
                //     drawResizeHandles();
                // }
            }
        }

        function drawSelectionHandles(screenX, screenY, screenW, screenH) {
            const handleSize = Math.max(6, Math.min(12, renderedCellSize * 0.5)); // Adaptive handle size
            const halfHandle = handleSize / 2;

            // Save current context state
            gridCtx.save();
            gridCtx.setLineDash([]);
            gridCtx.lineWidth = 1;

            // Corner handles (for scaling)
            const corners = [
                { x: screenX, y: screenY, type: 'nw' }, // Top-left
                { x: screenX + screenW, y: screenY, type: 'ne' }, // Top-right
                { x: screenX, y: screenY + screenH, type: 'sw' }, // Bottom-left
                { x: screenX + screenW, y: screenY + screenH, type: 'se' } // Bottom-right
            ];

            // Edge handles (for stretching)
            const edges = [
                { x: screenX + screenW / 2, y: screenY, type: 'n' }, // Top
                { x: screenX + screenW, y: screenY + screenH / 2, type: 'e' }, // Right
                { x: screenX + screenW / 2, y: screenY + screenH, type: 's' }, // Bottom
                { x: screenX, y: screenY + screenH / 2, type: 'w' } // Left
            ];

            // Draw corner handles (blue squares for scaling)
            gridCtx.fillStyle = 'rgba(0, 100, 255, 0.8)';
            gridCtx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
            corners.forEach(corner => {
                gridCtx.fillRect(corner.x - halfHandle, corner.y - halfHandle, handleSize, handleSize);
                gridCtx.strokeRect(corner.x - halfHandle, corner.y - halfHandle, handleSize, handleSize);
            });

            // Draw edge handles (green rectangles for stretching)
            gridCtx.fillStyle = 'rgba(0, 200, 100, 0.8)';
            gridCtx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
            edges.forEach(edge => {
                if (edge.type === 'n' || edge.type === 's') {
                    // Horizontal edge handles (wider)
                    gridCtx.fillRect(edge.x - handleSize, edge.y - halfHandle / 2, handleSize * 2, halfHandle);
                    gridCtx.strokeRect(edge.x - handleSize, edge.y - halfHandle / 2, handleSize * 2, halfHandle);
                } else {
                    // Vertical edge handles (taller)
                    gridCtx.fillRect(edge.x - halfHandle / 2, edge.y - handleSize, halfHandle, handleSize * 2);
                    gridCtx.strokeRect(edge.x - halfHandle / 2, edge.y - handleSize, halfHandle, handleSize * 2);
                }
            });

            // Restore context state
            gridCtx.restore();
        }

        function getSelectionHandleAtPoint(mouseX, mouseY) {
            if (!currentSelectionStart || !currentSelectionEnd || !isSelecting || isMovingSelection || drawing) {
                return null;
            }

            const selLogicX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
            const selLogicY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
            const selLogicX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
            const selLogicY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);

            const screenX = (selLogicX0 - currentViewOffsetX) * renderedCellSize;
            const screenY = (selLogicY0 - currentViewOffsetY) * renderedCellSize;
            const screenW = (selLogicX1 - selLogicX0 + 1) * renderedCellSize;
            const screenH = (selLogicY1 - selLogicY0 + 1) * renderedCellSize;

            const handleSize = Math.max(6, Math.min(12, renderedCellSize * 0.5));
            const halfHandle = handleSize / 2;

            // Corner handles (for scaling)
            const corners = [
                { x: screenX, y: screenY, type: 'nw' }, // Top-left
                { x: screenX + screenW, y: screenY, type: 'ne' }, // Top-right
                { x: screenX, y: screenY + screenH, type: 'sw' }, // Bottom-left
                { x: screenX + screenW, y: screenY + screenH, type: 'se' } // Bottom-right
            ];

            // Edge handles (for stretching)
            const edges = [
                { x: screenX + screenW / 2, y: screenY, type: 'n' }, // Top
                { x: screenX + screenW, y: screenY + screenH / 2, type: 'e' }, // Right
                { x: screenX + screenW / 2, y: screenY + screenH, type: 's' }, // Bottom
                { x: screenX, y: screenY + screenH / 2, type: 'w' } // Left
            ];

            // Check corner handles first (they have priority)
            for (const corner of corners) {
                if (mouseX >= corner.x - halfHandle && mouseX <= corner.x + halfHandle &&
                    mouseY >= corner.y - halfHandle && mouseY <= corner.y + halfHandle) {
                    return { type: corner.type, isCorner: true };
                }
            }

            // Check edge handles
            for (const edge of edges) {
                let hitBox;
                if (edge.type === 'n' || edge.type === 's') {
                    // Horizontal edge handles (wider)
                    hitBox = {
                        x: edge.x - handleSize,
                        y: edge.y - halfHandle / 2,
                        w: handleSize * 2,
                        h: halfHandle
                    };
                } else {
                    // Vertical edge handles (taller)
                    hitBox = {
                        x: edge.x - halfHandle / 2,
                        y: edge.y - handleSize,
                        w: halfHandle,
                        h: handleSize * 2
                    };
                }

                if (mouseX >= hitBox.x && mouseX <= hitBox.x + hitBox.w &&
                    mouseY >= hitBox.y && mouseY <= hitBox.y + hitBox.h) {
                    return { type: edge.type, isCorner: false };
                }
            }

            return null;
        }

        function drawGrid() {
            const activeGridStyle=gridStyleSelector.value;
            if(activeGridStyle==='off'||currentGridSize<=0||gridCanvas.width===0||gridCanvas.height===0)return;
            if(activeGridStyle==='lines'&&renderedCellSize<1.5)return;
            if(activeGridStyle==='squares'&&renderedCellSize<8)return;
            const firstColLogical=Math.floor(currentViewOffsetX),logicalColsInView=gridCanvas.width/renderedCellSize;
            const lastColLogicalBoundary=currentViewOffsetX+logicalColsInView;
            const firstRowLogical=Math.floor(currentViewOffsetY),logicalRowsInView=gridCanvas.height/renderedCellSize;
            const lastRowLogicalBoundary=currentViewOffsetY+logicalRowsInView,epsilon=0.0001;
            if(activeGridStyle==='lines'){
                gridCtx.strokeStyle='#cccccc';gridCtx.lineWidth=1;
                for(let c=firstColLogical;c<=Math.ceil(lastColLogicalBoundary);c++){
                    if(c>=0&&c<=currentGridSize){
                        const sX_f=(c-currentViewOffsetX)*renderedCellSize,sX_d=Math.round(sX_f);
                        if(sX_d>=-gridCtx.lineWidth&&sX_d<=gridCanvas.width+gridCtx.lineWidth){gridCtx.beginPath();gridCtx.moveTo(sX_d+.5,0);gridCtx.lineTo(sX_d+.5,gridCanvas.height);gridCtx.stroke()}
                    }
                }
                for(let r=firstRowLogical;r<=Math.ceil(lastRowLogicalBoundary);r++){
                    if(r>=0&&r<=currentGridSize){
                        const sY_f=(r-currentViewOffsetY)*renderedCellSize,sY_d=Math.round(sY_f);
                        if(sY_d>=-gridCtx.lineWidth&&sY_d<=gridCanvas.height+gridCtx.lineWidth){gridCtx.beginPath();gridCtx.moveTo(0,sY_d+.5);gridCtx.lineTo(gridCanvas.width,sY_d+.5);gridCtx.stroke()}
                    }
                }
            }else if(activeGridStyle==='squares'){
                gridCtx.fillStyle='rgba(150,150,150,0.5)';
                // Improved square size calculation for better visibility and less artifacts
                const minSqPS = 2; // Minimum square size of 2 pixels
                const maxSqPS = Math.max(minSqPS, Math.floor(renderedCellSize * 0.4)); // Max 40% of cell size
                const sqSR = renderedCellSize < 12 ? 0.25 : 0.3; // Smaller ratio for smaller cells
                let sqDS = Math.max(minSqPS, Math.floor(renderedCellSize * sqSR));
                sqDS = Math.min(sqDS, maxSqPS);
                
                // Additional safeguard: if squares would be too small, don't draw them
                if (sqDS < 2 || renderedCellSize < 10) return;
                
                for(let gY=Math.floor(currentViewOffsetY);gY<currentViewOffsetY+logicalRowsInView+epsilon;gY++){
                    if(gY<0||gY>=currentGridSize)continue;
                    for(let gX=Math.floor(currentViewOffsetX);gX<currentViewOffsetX+logicalColsInView+epsilon;gX++){
                        // Guard for gX being out of bounds (already present, keep it)
                        if(gX<0||gX>=currentGridSize)continue;

                        let isEmpty = true; // Initialize isEmpty for the current cell (gX, gY)

                        // Iterate through all layers from top to bottom.
                        // If any visible layer with sufficient opacity has a visible pixel here,
                        // the cell is not considered empty for grid drawing purposes.
                        for (let layerIdx = currentLayers.length - 1; layerIdx >= 0; layerIdx--) {
                            const l = currentLayers[layerIdx];
                            if (l.isVisible && l.opacity > 1) { // Layer must be effectively visible
                                if (l.pixels && l.pixels[gY]) { // Check if row exists
                                    const cellData = l.pixels[gY][gX];
                                    // Check if cell has actual color data (not undefined or EMPTY_COLOR (null))
                                    if (cellData !== undefined && cellData !== EMPTY_COLOR) {
                                        const pixelColorStats = parseRgba(cellData);
                                        // Consider the pixel's own alpha combined with layer opacity
                                        if (pixelColorStats.a * (l.opacity / 100.0) > 0.01) { // Threshold for "visibly occupied"
                                            isEmpty = false; // Found a significant pixel
                                            break; // No need to check layers below this one
                                        }
                                    }
                                }
                            }
                        }
                        // Now, the single 'isEmpty' variable correctly reflects the composited state
                        // for the purpose of drawing the grid square.
                        if(isEmpty){
                            const cSXS=(gX-currentViewOffsetX)*renderedCellSize,cSYS=(gY-currentViewOffsetY)*renderedCellSize;
                            const cCX=cSXS+renderedCellSize/2,cCY=cSYS+renderedCellSize/2;
                            const sX=Math.floor(cCX-sqDS/2),sY=Math.floor(cCY-sqDS/2);
                            if(sX+sqDS>0&&sX<gridCanvas.width&&sY+sqDS>0&&sY<gridCanvas.height)gridCtx.fillRect(sX,sY,sqDS,sqDS)
                        }
                    }
                }
            }
        }
        function blendRgbaObjects(rgba_B, rgba_S) {
            const Sa = rgba_S.a; // Source Alpha
            const Ba = rgba_B.a; // Background Alpha

            // Alpha for the composite image
            const Ao = Sa + Ba * (1 - Sa);

            if (Ao < 0.001) { // Effectively fully transparent
                return { r: 0, g: 0, b: 0, a: 0 };
            }

            // Composite color components
            const Ro = (rgba_S.r * Sa + rgba_B.r * Ba * (1 - Sa)) / Ao;
            const Go = (rgba_S.g * Sa + rgba_B.g * Ba * (1 - Sa)) / Ao;
            const Bo = (rgba_S.b * Sa + rgba_B.b * Ba * (1 - Sa)) / Ao;

            return {
                r: Math.round(Ro),
                g: Math.round(Go),
                b: Math.round(Bo),
                a: parseFloat(Ao.toFixed(3)) // Keep alpha precise
            };
        }
        function setPixel(x, y, brushRgbaString) { // This is the master blend onto layer.pixels
            if (!activeLayer || !activeLayer.pixels) return false;
            const activeLayerPixels = activeLayer.pixels;

            if (x < 0 || x >= currentGridSize || y < 0 || y >= currentGridSize) return false;
            if (!activeLayerPixels[y]) activeLayerPixels[y] = Array(currentGridSize).fill(EMPTY_COLOR);

            const existingPixelRgbaString = activeLayerPixels[y][x];
            if (brushRgbaString === EMPTY_COLOR) {
                if (existingPixelRgbaString !== EMPTY_COLOR) {
                    activeLayerPixels[y][x] = EMPTY_COLOR;
                    expandDirtyRect(x, y); return true;
                } return false;
            }
            const sColor = parseRgba(brushRgbaString); // Source color (brush)
            if (!sColor || sColor.a < 0.001) { // If brush is fully transparent, no change unless erasing
                 if (existingPixelRgbaString !== EMPTY_COLOR && brushRgbaString === EMPTY_COLOR) { // Explicit erase
                    activeLayerPixels[y][x] = EMPTY_COLOR; expandDirtyRect(x, y); return true;
                 }
                 return false; // No change if brush is transparent and not erasing
            }

            const bColor = parseRgba(existingPixelRgbaString); // Backdrop color (current layer pixel)
            const As = sColor.a, Ab = bColor.a;
            const Aout = As + Ab * (1 - As); // Alpha composite formula
            let Rout, Gout, Bout;

            if (Aout < 0.001) { // Result is fully transparent
                if (existingPixelRgbaString !== EMPTY_COLOR) {
                    activeLayerPixels[y][x] = EMPTY_COLOR; expandDirtyRect(x, y); return true;
                } return false;
            } else {
                Rout = (sColor.r * As + bColor.r * Ab * (1 - As)) / Aout;
                Gout = (sColor.g * As + bColor.g * Ab * (1 - As)) / Aout;
                Bout = (sColor.b * As + bColor.b * Ab * (1 - As)) / Aout;
            }
            const newPixelRgbaString = `rgba(${Math.round(Rout)},${Math.round(Gout)},${Math.round(Bout)},${parseFloat(Aout.toFixed(3))})`;
            if (newPixelRgbaString !== existingPixelRgbaString) {
                activeLayerPixels[y][x] = newPixelRgbaString; expandDirtyRect(x, y); return true;
            } return false;
        }


        // Track which areas of the stroke buffer have been modified for efficient updates
        let strokeBufferDirtyRegions = [];

        function addStrokeBufferDirtyRegion(minX, minY, maxX, maxY) {
            strokeBufferDirtyRegions.push({ minX, minY, maxX, maxY });
        }

        function clearStrokeBufferDirtyRegions() {
            strokeBufferDirtyRegions = [];
        }

        // Brush Performance Settings
        let brushPerformanceMode = false; // When true, draws single-pixel preview during stroke
        let showCanvasSizeWarning = true; // When false, don't show large canvas warning popup
        let performancePreviewPath = []; // Stores the single-pixel path for performance mode
        let isPerformancePreviewActive = false; // True during performance preview drawing
        let isDrawingCancelled = false; // Flag to cancel ongoing drawing operations
        let drawingProgress = 0; // Current drawing progress (0-100)
        let totalDrawingOperations = 0; // Total number of operations to complete

        // Drawing Operation Queue System
        let drawingOperationQueue = []; // Queue of active drawing operations
        let nextOperationId = 1; // Unique ID counter for operations

        // Multiple sets of global variables for simultaneous operations (up to 5)
        const MAX_SIMULTANEOUS_OPERATIONS = 5;
        let operationStrokeBuffers = Array(MAX_SIMULTANEOUS_OPERATIONS).fill(null);
        let operationLayerDataBeforeStroke = Array(MAX_SIMULTANEOUS_OPERATIONS).fill(null);
        let operationDirtyRegions = Array(MAX_SIMULTANEOUS_OPERATIONS).fill(null).map(() => []);
        let availableOperationSlots = [0, 1, 2, 3, 4]; // Available slot indices

        // Layer access synchronization
        let operationsProcessingCount = 0;
        let layerCommitMutex = false;
        let pendingCommits = [];

        // Debug logging for corruption tracking
        let debugLogging = true; // Set to false to disable logging
        function debugLog(message, data = null) {
            if (!debugLogging) return;
            const timestamp = new Date().toISOString().split('T')[1].slice(0, -1);
            if (data) {
                console.log(`[${timestamp}] ${message}`, data);
            } else {
                console.log(`[${timestamp}] ${message}`);
            }
        }

        // Image Import Settings
        let imageImportMode = 'fit-to-view'; // 'fit-to-view' or 'pixel-perfect'
        let showImageImportWarning = true; // When false, don't show image import warning popup
        let isImageImportCancelled = false; // Flag to cancel ongoing image import operations
        let imageImportProgress = 0; // Current image import progress (0-100)

        // IndexedDB for large canvas storage
        let canvasDB = null;
        const DB_NAME = 'PixelArtNexusDB';
        const DB_VERSION = 1;
        const CANVAS_STORE = 'canvasData';

        // This function applies the current stroke buffer to the active layer's pixels for live preview.
        // It blends based on layerDataBeforeStroke.
        // OPTIMIZED: Only processes dirty regions instead of the entire canvas for better performance
        function _applyStrokeBufferToActiveLayerForPreview() {
            if (!activeLayer || !currentStrokeBuffer || !layerDataBeforeStroke) {
                debugLog(`⚠️ Preview skipped: missing requirements`, {
                    hasActiveLayer: !!activeLayer,
                    hasStrokeBuffer: !!currentStrokeBuffer,
                    hasLayerData: !!layerDataBeforeStroke
                });
                return;
            }

            // Skip preview updates when operations are processing to prevent interference
            if (operationsProcessingCount > 0) {
                debugLog(`🚫 Preview blocked: ${operationsProcessingCount} operations processing`);
                return;
            }

            debugLog(`🎨 Applying preview`, {
                dirtyRegionsCount: strokeBufferDirtyRegions.length,
                operationsProcessingCount
            });

            // Always use dirty region optimization - if no regions, there's nothing to update
            if (strokeBufferDirtyRegions.length === 0) {
                return; // No changes to apply
            }

            // Process only the dirty regions for much better performance
            let globalMinX = Infinity, globalMinY = Infinity;
            let globalMaxX = -Infinity, globalMaxY = -Infinity;

            for (const region of strokeBufferDirtyRegions) {
                const { minX, minY, maxX, maxY } = region;

                for (let y = Math.max(0, minY); y <= Math.min(currentGridSize - 1, maxY); y++) {
                    if (currentStrokeBuffer[y]) {
                        if (!activeLayer.pixels[y]) activeLayer.pixels[y] = Array(currentGridSize).fill(EMPTY_COLOR);

                        for (let x = Math.max(0, minX); x <= Math.min(currentGridSize - 1, maxX); x++) {
                            const strokePixelAction = currentStrokeBuffer[y][x];
                            if (strokePixelAction !== undefined) {
                                const originalPixelForBlend = layerDataBeforeStroke[y] ? (layerDataBeforeStroke[y][x] !== undefined ? layerDataBeforeStroke[y][x] : EMPTY_COLOR) : EMPTY_COLOR;
                                let previewPixelColor;

                                if (strokePixelAction === EMPTY_COLOR) {
                                    previewPixelColor = EMPTY_COLOR;
                                } else {
                                    const sColor = parseRgba(strokePixelAction);
                                    const bColor = parseRgba(originalPixelForBlend);

                                    if (!sColor || sColor.a < 0.001) {
                                        previewPixelColor = originalPixelForBlend;
                                    } else if (bColor.a < 0.001) {
                                        previewPixelColor = strokePixelAction;
                                    } else {
                                        const As = sColor.a; const Ab = bColor.a;
                                        const Aout = As + Ab * (1 - As);
                                        if (Aout < 0.001) { previewPixelColor = EMPTY_COLOR; }
                                        else {
                                            const Rout = (sColor.r * As + bColor.r * Ab * (1 - As)) / Aout;
                                            const Gout = (sColor.g * As + bColor.g * Ab * (1 - As)) / Aout;
                                            const Bout = (sColor.b * As + bColor.b * Ab * (1 - As)) / Aout;
                                            previewPixelColor = `rgba(${Math.round(Rout)},${Math.round(Gout)},${Math.round(Bout)},${parseFloat(Aout.toFixed(3))})`;
                                        }
                                    }
                                }

                                if (activeLayer.pixels[y][x] !== previewPixelColor) {
                                    activeLayer.pixels[y][x] = previewPixelColor;
                                    // Track the global dirty bounds for a single expandDirtyRect call
                                    globalMinX = Math.min(globalMinX, x);
                                    globalMinY = Math.min(globalMinY, y);
                                    globalMaxX = Math.max(globalMaxX, x);
                                    globalMaxY = Math.max(globalMaxY, y);
                                }
                            }
                        }
                    }
                }
            }

            // Single dirty rect expansion for all changes (much more efficient)
            if (globalMinX <= globalMaxX && globalMinY <= globalMaxY) {
                isCanvasDirtyForTools = true;
                dirtyRect.minX = Math.min(dirtyRect.minX, globalMinX);
                dirtyRect.minY = Math.min(dirtyRect.minY, globalMinY);
                dirtyRect.maxX = Math.max(dirtyRect.maxX, globalMaxX);
                dirtyRect.maxY = Math.max(dirtyRect.maxY, globalMaxY);
            }
        }

        // Fallback function for full canvas scan (original implementation)
        function _applyStrokeBufferToActiveLayerForPreview_FullScan() {
            if (activeLayer && currentStrokeBuffer && layerDataBeforeStroke) {
                for (let y = 0; y < currentGridSize; y++) {
                    if (currentStrokeBuffer[y]) {
                        if (!activeLayer.pixels[y]) activeLayer.pixels[y] = Array(currentGridSize).fill(EMPTY_COLOR);
                        for (let x = 0; x < currentGridSize; x++) {
                            const strokePixelAction = currentStrokeBuffer[y][x];
                            if (strokePixelAction !== undefined) {
                                const originalPixelForBlend = layerDataBeforeStroke[y] ? (layerDataBeforeStroke[y][x] !== undefined ? layerDataBeforeStroke[y][x] : EMPTY_COLOR) : EMPTY_COLOR;
                                let previewPixelColor;

                                if (strokePixelAction === EMPTY_COLOR) {
                                    previewPixelColor = EMPTY_COLOR;
                                } else {
                                    const sColor = parseRgba(strokePixelAction);
                                    const bColor = parseRgba(originalPixelForBlend);

                                    if (!sColor || sColor.a < 0.001) {
                                        previewPixelColor = originalPixelForBlend;
                                    } else if (bColor.a < 0.001) {
                                        previewPixelColor = strokePixelAction;
                                    } else {
                                        const As = sColor.a; const Ab = bColor.a;
                                        const Aout = As + Ab * (1 - As);
                                        if (Aout < 0.001) { previewPixelColor = EMPTY_COLOR; }
                                        else {
                                            const Rout = (sColor.r * As + bColor.r * Ab * (1 - As)) / Aout;
                                            const Gout = (sColor.g * As + bColor.g * Ab * (1 - As)) / Aout;
                                            const Bout = (sColor.b * As + bColor.b * Ab * (1 - As)) / Aout;
                                            previewPixelColor = `rgba(${Math.round(Rout)},${Math.round(Gout)},${Math.round(Bout)},${parseFloat(Aout.toFixed(3))})`;
                                        }
                                    }
                                }

                                if (activeLayer.pixels[y][x] !== previewPixelColor) {
                                    activeLayer.pixels[y][x] = previewPixelColor;
                                    expandDirtyRect(x, y);
                                }
                            }
                        }
                    }
                }
            }
        }


        // Renamed the original applyBrush to _applyBrushStampAtPoint
        // This function now contains the core logic for drawing a single brush stamp TO THE STROKE BUFFER.
        function _applyBrushStampAtPoint(cX, cY, baseHexColor) {
            // No direct access to activeLayer here, this function populates currentStrokeBuffer.
            if (cX < 0 || cX >= currentGridSize || cY < 0 || cY >= currentGridSize) return;

            if (!currentStrokeBuffer) {
                // console.warn("_applyBrushStampAtPoint called without currentStrokeBuffer.");
                return;
            }

            // Track the dirty region for this brush stamp for performance optimization
            let stampMinX = cX, stampMinY = cY, stampMaxX = cX, stampMaxY = cY;

            const actualBrushCoreSize = parseInt(thicknessInput.value) || 1;
            const userSelectedBrushType = currentBrushType;
            let effectiveBrushTypeForStamp = userSelectedBrushType; // Used for taper preview

            if (userSelectedBrushType === 'taper') { // For the *stamp* part of taper (preview during drag)
                effectiveBrushTypeForStamp = 'circular'; // Taper's individual stamps are circular (or square)
            }

            // Handle brushes with unique rendering logic first
            if (userSelectedBrushType === 'spray_paint') {
                const colorToApplySpray = baseHexColor === EMPTY_COLOR ? EMPTY_COLOR : hexToRgba(baseHexColor, currentBrushOpacity);
                const particleSize = actualBrushCoreSize;
                const sprayEffectRadius = currentSprayRadius;
                const numberOfDots = Math.max(5, Math.floor(5 + sprayEffectRadius * 1.5 + particleSize * 0.5));
                const halfParticleFloor = Math.floor((particleSize - 1) / 2);
                const halfParticleCeil = Math.ceil((particleSize - 1) / 2);

                for (let i = 0; i < numberOfDots; i++) {
                    const angle = Math.random() * 2 * Math.PI;
                    const distance = Math.sqrt(Math.random()) * sprayEffectRadius;
                    const pCX = cX + Math.round(distance * Math.cos(angle));
                    const pCY = cY + Math.round(distance * Math.sin(angle));

                    for (let dy_offset = -halfParticleFloor; dy_offset <= halfParticleCeil; dy_offset++) {
                        for (let dx_offset = -halfParticleFloor; dx_offset <= halfParticleCeil; dx_offset++) {
                            const GX = pCX + dx_offset;
                            const GY = pCY + dy_offset;
                            if (GX >= 0 && GX < currentGridSize && GY >= 0 && GY < currentGridSize) {
                                if (!currentStrokeBuffer[GY]) currentStrokeBuffer[GY] = Array(currentGridSize).fill(undefined);
                                currentStrokeBuffer[GY][GX] = colorToApplySpray;
                                // Track dirty region bounds
                                stampMinX = Math.min(stampMinX, GX);
                                stampMinY = Math.min(stampMinY, GY);
                                stampMaxX = Math.max(stampMaxX, GX);
                                stampMaxY = Math.max(stampMaxY, GY);
                            }
                        }
                    }
                }
                // Add the dirty region for this brush stamp to optimize preview updates
                if (stampMinX <= stampMaxX && stampMinY <= stampMaxY) {
                    addStrokeBufferDirtyRegion(stampMinX, stampMinY, stampMaxX, stampMaxY);
                }
                return; // Spray paint handled
            }
            else if (userSelectedBrushType === 'glow') {
                if (baseHexColor === EMPTY_COLOR) {
                    const eraseColor = EMPTY_COLOR;
                    const halfFloor = Math.floor((actualBrushCoreSize - 1) / 2);
                    const halfCeil = Math.ceil((actualBrushCoreSize - 1) / 2);
                    for (let dy = -halfFloor; dy <= halfCeil; dy++) {
                        for (let dx = -halfFloor; dx <= halfCeil; dx++) {
                            const gX = cX + dx; const gY = cY + dy;
                            if (gX >= 0 && gX < currentGridSize && gY >= 0 && gY < currentGridSize) {
                                if (!currentStrokeBuffer[gY]) currentStrokeBuffer[gY] = Array(currentGridSize).fill(undefined);
                                currentStrokeBuffer[gY][gX] = eraseColor;
                            }
                        }
                    }
                    return;
                }
                const coreRgbData = parseRgba(hexToRgba(baseHexColor, 100));
                const maxOverallBrushOpacity = currentBrushOpacity / 100.0;
                const coreRadius = (actualBrushCoreSize - 1) / 2.0;
                const totalGlowRadius = coreRadius + currentGlowSize;
                const iterationBound = Math.ceil(totalGlowRadius);
                for (let dy_offset = -iterationBound; dy_offset <= iterationBound; dy_offset++) {
                    for (let dx_offset = -iterationBound; dx_offset <= iterationBound; dx_offset++) {
                        const globalX = cX + dx_offset; const globalY = cY + dy_offset;
                        if (globalX < 0 || globalX >= currentGridSize || globalY < 0 || globalY >= currentGridSize) continue;
                        const distanceFromCenter = Math.sqrt(dx_offset * dx_offset + dy_offset * dy_offset);
                        let calculatedPixelOpacity = 0;
                        if (distanceFromCenter <= coreRadius) {
                            calculatedPixelOpacity = maxOverallBrushOpacity;
                        } else if (distanceFromCenter <= totalGlowRadius && currentGlowSize > 0) {
                            const glowEffectDistance = distanceFromCenter - coreRadius;
                            calculatedPixelOpacity = maxOverallBrushOpacity * (1.0 - (glowEffectDistance / currentGlowSize));
                            calculatedPixelOpacity = Math.max(0, calculatedPixelOpacity);
                        } else { continue; }
                        if (calculatedPixelOpacity > 0.001) {
                            const finalColorRgbaString = `rgba(${coreRgbData.r},${coreRgbData.g},${coreRgbData.b},${parseFloat(calculatedPixelOpacity.toFixed(3))})`;
                            if (!currentStrokeBuffer[globalY]) currentStrokeBuffer[globalY] = Array(currentGridSize).fill(undefined);
                            const existingValue = currentStrokeBuffer[globalY][globalX];
                            if (existingValue && existingValue !== EMPTY_COLOR) {
                                if (calculatedPixelOpacity > parseRgba(existingValue).a) currentStrokeBuffer[globalY][globalX] = finalColorRgbaString;
                            } else { currentStrokeBuffer[globalY][globalX] = finalColorRgbaString; }
                            // Track dirty region bounds
                            stampMinX = Math.min(stampMinX, globalX);
                            stampMinY = Math.min(stampMinY, globalY);
                            stampMaxX = Math.max(stampMaxX, globalX);
                            stampMaxY = Math.max(stampMaxY, globalY);
                        }
                    }
                }
                // Add the dirty region for this brush stamp to optimize preview updates
                if (stampMinX <= stampMaxX && stampMinY <= stampMaxY) {
                    addStrokeBufferDirtyRegion(stampMinX, stampMinY, stampMaxX, stampMaxY);
                }
                return; // Glow brush handled
            }
            else if (userSelectedBrushType === 'anti_alias') {
                if (baseHexColor === EMPTY_COLOR) {
                    const eraseColor = EMPTY_COLOR;
                    const halfFloor = Math.floor((actualBrushCoreSize - 1) / 2);
                    const halfCeil = Math.ceil((actualBrushCoreSize - 1) / 2);
                    for (let dy = -halfFloor; dy <= halfCeil; dy++) {
                        for (let dx = -halfFloor; dx <= halfCeil; dx++) {
                            const gX = cX + dx; const gY = cY + dy;
                            if (gX >= 0 && gX < currentGridSize && gY >= 0 && gY < currentGridSize) {
                                if (!currentStrokeBuffer[gY]) currentStrokeBuffer[gY] = Array(currentGridSize).fill(undefined);
                                currentStrokeBuffer[gY][gX] = eraseColor;
                            }
                        }
                    }
                    return;
                }
                const coreRgbData = parseRgba(hexToRgba(baseHexColor, 100));
                const maxOverallBrushOpacity = currentBrushOpacity / 100.0;
                const coreRadius = (actualBrushCoreSize - 1) / 2.0;
                const aaFeatherAmount = 1.0;
                const totalEffectRadius = coreRadius + aaFeatherAmount;
                const iterationBound = Math.ceil(totalEffectRadius);
                for (let dy_offset = -iterationBound; dy_offset <= iterationBound; dy_offset++) {
                    for (let dx_offset = -iterationBound; dx_offset <= iterationBound; dx_offset++) {
                        const globalX = cX + dx_offset; const globalY = cY + dy_offset;
                        if (globalX < 0 || globalX >= currentGridSize || globalY < 0 || globalY >= currentGridSize) continue;
                        const distanceFromCenter = Math.sqrt(dx_offset * dx_offset + dy_offset * dy_offset);
                        let calculatedPixelOpacity = 0;
                        if (distanceFromCenter <= coreRadius) {
                            calculatedPixelOpacity = maxOverallBrushOpacity;
                        } else if (distanceFromCenter <= totalEffectRadius && aaFeatherAmount > 0) {
                            const distanceIntoFeather = distanceFromCenter - coreRadius;
                            calculatedPixelOpacity = maxOverallBrushOpacity * (1.0 - (distanceIntoFeather / aaFeatherAmount));
                            calculatedPixelOpacity = Math.max(0, Math.min(maxOverallBrushOpacity, calculatedPixelOpacity));
                        } else { continue; }
                        if (calculatedPixelOpacity > 0.001) {
                            const finalColorRgbaString = `rgba(${coreRgbData.r},${coreRgbData.g},${coreRgbData.b},${parseFloat(calculatedPixelOpacity.toFixed(3))})`;
                            if (!currentStrokeBuffer[globalY]) currentStrokeBuffer[globalY] = Array(currentGridSize).fill(undefined);
                            const existingValue = currentStrokeBuffer[globalY][globalX];
                            if (existingValue && existingValue !== EMPTY_COLOR) {
                                if (calculatedPixelOpacity > parseRgba(existingValue).a) currentStrokeBuffer[globalY][globalX] = finalColorRgbaString;
                            } else { currentStrokeBuffer[globalY][globalX] = finalColorRgbaString; }
                            // Track dirty region bounds
                            stampMinX = Math.min(stampMinX, globalX);
                            stampMinY = Math.min(stampMinY, globalY);
                            stampMaxX = Math.max(stampMaxX, globalX);
                            stampMaxY = Math.max(stampMaxY, globalY);
                        }
                    }
                }
                // Add the dirty region for this brush stamp to optimize preview updates
                if (stampMinX <= stampMaxX && stampMinY <= stampMaxY) {
                    addStrokeBufferDirtyRegion(stampMinX, stampMinY, stampMaxX, stampMaxY);
                }
                return; // Anti-alias brush handled
            }

            // Standard brush logic (normal, circular, calligraphy, hatch types, and Taper preview stamps)
            const colorToApply = baseHexColor === EMPTY_COLOR ? EMPTY_COLOR : hexToRgba(baseHexColor, currentBrushOpacity);
            const halfFloorSize = Math.floor((actualBrushCoreSize - 1) / 2);
            const halfCeilSize = Math.ceil((actualBrushCoreSize - 1) / 2);
            let scaleForPatternLogic = currentHatchScale;

            if (effectiveBrushTypeForStamp === 'grid_hatch' || effectiveBrushTypeForStamp === 'dot_hatch') {
                scaleForPatternLogic = currentHatchScale + 1;
                if (scaleForPatternLogic < 2) scaleForPatternLogic = 2;
            }

            for (let dy_offset = -halfFloorSize; dy_offset <= halfCeilSize; dy_offset++) {
                for (let dx_offset = -halfFloorSize; dx_offset <= halfCeilSize; dx_offset++) {
                    const globalX = cX + dx_offset;
                    const globalY = cY + dy_offset;
                    if (globalX < 0 || globalX >= currentGridSize || globalY < 0 || globalY >= currentGridSize) continue;

                    let partOfBaseShape = false;
                    if (effectiveBrushTypeForStamp === 'calligraphy') {
                        const cH = actualBrushCoreSize, cW = Math.max(1, Math.floor(cH / 4));
                        const hCH_f = Math.floor((cH - 1) / 2), hCH_c = Math.ceil((cH - 1) / 2);
                        const hCW_f = Math.floor((cW - 1) / 2), hCW_c = Math.ceil((cW - 1) / 2);
                        if (dy_offset >= -hCH_f && dy_offset <= hCH_c && dx_offset >= -hCW_f && dx_offset <= hCW_c) partOfBaseShape = true;
                    } else if (['normal', 'hatch', 'cross_hatch', 'grid_hatch', 'dot_hatch'].includes(effectiveBrushTypeForStamp)) {
                        partOfBaseShape = true;
                    } else if (effectiveBrushTypeForStamp === 'circular') { // Also used by taper preview
                        if (dx_offset * dx_offset + dy_offset * dy_offset <= Math.pow((actualBrushCoreSize - 1) / 2, 2) + 0.5) {
                           partOfBaseShape = true;
                        }
                    } else { // Should only be hit if a new brush type was added and not handled above
                        if (!['glow', 'spray_paint', 'anti_alias'].includes(effectiveBrushTypeForStamp)) { // Ensure it's not a special brush
                            partOfBaseShape = true; // Default to drawing something for unknown standard types
                        }
                    }

                    if (partOfBaseShape) {
                        let shouldDrawPixel = false;
                        switch (effectiveBrushTypeForStamp) {
                            case 'normal': case 'circular': case 'calligraphy': // Circular handles taper preview stamps
                                shouldDrawPixel = true; break;
                            case 'hatch':
                                if ((Math.floor(globalX / currentHatchScale) + Math.floor(globalY / currentHatchScale)) % 2 === 0) shouldDrawPixel = true; break;
                            case 'cross_hatch':
                                if ((Math.floor(globalX / currentHatchScale) + Math.floor(globalY / currentHatchScale)) % 2 !== 0) shouldDrawPixel = true; break;
                            case 'grid_hatch':
                                const rX_g = globalX % scaleForPatternLogic, rY_g = globalY % scaleForPatternLogic;
                                const m_g = Math.floor((scaleForPatternLogic - 1) / 2);
                                if (rX_g === m_g || rY_g === m_g) shouldDrawPixel = true; break;
                            case 'dot_hatch':
                                const rX_d = globalX % scaleForPatternLogic, rY_d = globalY % scaleForPatternLogic;
                                const m_d = Math.floor((scaleForPatternLogic - 1) / 2);
                                if (rX_d !== m_d && rY_d !== m_d) shouldDrawPixel = true; break;
                        }
                        if (shouldDrawPixel) {
                            if (!currentStrokeBuffer[globalY]) currentStrokeBuffer[globalY] = Array(currentGridSize).fill(undefined);
                            currentStrokeBuffer[globalY][globalX] = colorToApply;
                            // Track dirty region bounds
                            stampMinX = Math.min(stampMinX, globalX);
                            stampMinY = Math.min(stampMinY, globalY);
                            stampMaxX = Math.max(stampMaxX, globalX);
                            stampMaxY = Math.max(stampMaxY, globalY);
                        }
                    }
                }
            }

            // Add the dirty region for this brush stamp to optimize preview updates
            if (stampMinX <= stampMaxX && stampMinY <= stampMaxY) {
                addStrokeBufferDirtyRegion(stampMinX, stampMinY, stampMaxX, stampMaxY);
            }
        }

        // New applyBrush function to handle mirroring and dispatch to _applyBrushStampAtPoint
        function applyBrush(originalGridX, originalGridY, baseHexColor) {
            if (!currentStrokeBuffer) return;

            const pointsToProcess = [];
            const processedPoints = new Set();

            function addPointToProcess(x, y) {
                const key = `${x},${y}`;
                if (!processedPoints.has(key)) {
                    pointsToProcess.push({ x: x, y: y });
                    processedPoints.add(key);
                }
            }

            addPointToProcess(originalGridX, originalGridY); // Always add the primary point

            if (mirrorCenterX !== null && mirrorCenterY !== null) { // Mirroring is active
                const tempMirroredCandidates = [];

                // Vertical Mirror (applied to originalGridX, originalGridY)
                if (isVerticalMirrorActive) {
                    const mirroredY = Math.round(2 * mirrorCenterY - originalGridY);
                    tempMirroredCandidates.push({ x: originalGridX, y: mirroredY, type: 'v' });
                }

                // Horizontal Mirror (applied to originalGridX, originalGridY)
                if (isHorizontalMirrorActive) {
                    const mirroredX = Math.round(2 * mirrorCenterX - originalGridX);
                    tempMirroredCandidates.push({ x: mirroredX, y: originalGridY, type: 'h' });

                    // Diagonal for combined V+H
                    if (isVerticalMirrorActive) {
                        const mirroredY = Math.round(2 * mirrorCenterY - originalGridY);
                        tempMirroredCandidates.push({ x: mirroredX, y: mirroredY, type: 'vh' });
                    }
                }

                // Radial Mirror (applied to originalGridX, originalGridY)
                if (isRadialMirrorActive && radialMirrorAxes > 1) {
                    const angleIncrement = (2 * Math.PI) / radialMirrorAxes;
                    const dx_orig = originalGridX - mirrorCenterX;
                    const dy_orig = originalGridY - mirrorCenterY;

                    // Add points for each radial segment, including the one that might align with original point
                    for (let i = 0; i < radialMirrorAxes; i++) {
                        const angle = i * angleIncrement;
                        let rotatedX, rotatedY;
                        if (dx_orig === 0 && dy_orig === 0) { // Point is the center
                            rotatedX = mirrorCenterX;
                            rotatedY = mirrorCenterY;
                        } else {
                            const rotatedDx = dx_orig * Math.cos(angle) - dy_orig * Math.sin(angle);
                            const rotatedDy = dx_orig * Math.sin(angle) + dy_orig * Math.cos(angle);
                            rotatedX = Math.round(mirrorCenterX + rotatedDx);
                            rotatedY = Math.round(mirrorCenterY + rotatedDy);
                        }
                        tempMirroredCandidates.push({ x: rotatedX, y: rotatedY, type: 'r' });
                    }
                }
                // Add all unique candidates
                for (const p of tempMirroredCandidates) {
                    addPointToProcess(p.x, p.y);
                }
            }

            // Apply brush stamp to all unique points
            for (const point of pointsToProcess) {
                 _applyBrushStampAtPoint(point.x, point.y, baseHexColor);
            }
        }

        function commitStrokeBufferToLayer() {
            debugLog(`💾 Starting commit to layer`, {
                hasActiveLayer: !!activeLayer,
                hasActiveLayerPixels: !!(activeLayer && activeLayer.pixels),
                hasStrokeBuffer: !!currentStrokeBuffer,
                hasLayerDataBeforeStroke: !!layerDataBeforeStroke,
                operationsProcessingCount
            });

            if (!activeLayer || !activeLayer.pixels) {
                debugLog(`❌ Commit failed: missing active layer or pixels`);
                console.error("Commit Error: Active layer or its pixels are missing.");
                return;
            }
            const activeLayerPixels = activeLayer.pixels;

            if (!currentStrokeBuffer || !layerDataBeforeStroke ) {
                debugLog(`❌ Commit failed: missing stroke buffer or layer data`);
                console.error("Commit Error: Missing stroke buffer or layer data before stroke.");
                return;
            }

            for (let y = 0; y < currentGridSize; y++) {
                if (!currentStrokeBuffer[y] && !(layerDataBeforeStroke[y] && Object.values(layerDataBeforeStroke[y]).some(px => px !== undefined))) {
                     // Optimization: if no stroke data in this row and no original data (or all original was undefined), skip
                    if (activeLayerPixels[y] && Object.values(activeLayerPixels[y]).every(px => px === EMPTY_COLOR || px === undefined)) {
                        // if active layer row is also empty, truly skip
                        continue;
                    }
                }


                if (!activeLayerPixels[y] && (currentStrokeBuffer[y] || layerDataBeforeStroke[y])) {
                     activeLayerPixels[y] = Array(currentGridSize).fill(EMPTY_COLOR);
                }
                if (!layerDataBeforeStroke[y] && currentStrokeBuffer[y]) { // Ensure layerDataBeforeStroke row exists if stroke buffer row does
                    layerDataBeforeStroke[y] = Array(currentGridSize).fill(EMPTY_COLOR);
                }


                for (let x = 0; x < currentGridSize; x++) {
                    const strokePixelAction = currentStrokeBuffer[y] ? currentStrokeBuffer[y][x] : undefined;
                    const originalPixelOnLayer = layerDataBeforeStroke[y] ? (layerDataBeforeStroke[y][x] !== undefined ? layerDataBeforeStroke[y][x] : EMPTY_COLOR) : EMPTY_COLOR;
                    let finalPixelColor;

                    if (strokePixelAction !== undefined) { // Pixel was affected by current stroke
                        if (strokePixelAction === EMPTY_COLOR) {
                            finalPixelColor = EMPTY_COLOR;
                        } else {
                            const sColor = parseRgba(strokePixelAction); // Color from brush stroke buffer
                            if (sColor && sColor.a >= 0.995) { // Check effective opacity of the brush stamp
                                finalPixelColor = strokePixelAction; // Use the brush color string directly
                            } else {
                            const bColor = parseRgba(originalPixelOnLayer); // Color from layer *before* this stroke started

                            if (!sColor || sColor.a < 0.001) { // Brush stroke is transparent here
                                finalPixelColor = originalPixelOnLayer; // No change to the original pixel
                            } else if (bColor.a < 0.001) { // Original pixel was transparent, new stroke is not
                                finalPixelColor = strokePixelAction; // Use the stroke color directly
                            } else { // Both have opacity, blend them
                                const As = sColor.a; const Ab = bColor.a;
                                const Aout = As + Ab * (1 - As);
                                if (Aout < 0.001) { finalPixelColor = EMPTY_COLOR; }
                                else {
                                    const Rout = (sColor.r * As + bColor.r * Ab * (1 - As)) / Aout;
                                    const Gout = (sColor.g * As + bColor.g * Ab * (1 - As)) / Aout;
                                    const Bout = (sColor.b * As + bColor.b * Ab * (1 - As)) / Aout;
                                    finalPixelColor = `rgba(${Math.round(Rout)},${Math.round(Gout)},${Math.round(Bout)},${parseFloat(Aout.toFixed(3))})`;
                                }
                            }
                        } // End of the new else block
                        }
                    } else { // Pixel was not affected by current stroke buffer
                        finalPixelColor = originalPixelOnLayer;
                    }

                    if (!activeLayerPixels[y]) activeLayerPixels[y] = Array(currentGridSize).fill(EMPTY_COLOR);

                    if (activeLayerPixels[y][x] !== finalPixelColor) {
                        activeLayerPixels[y][x] = finalPixelColor;
                        expandDirtyRect(x, y);
                    }
                }
            }
        }
        function _pasteSelectionBuffer(buffer, topLeftX, topLeftY) {
            if (!activeLayer) {
                return false;
            }
            if (!activeLayer.pixels) {
                activeLayer.pixels = createNewPixelData(currentGridSize);
            }
            if (!buffer) {
                return false;
            }

            let pixelsChanged = false;
            for (let r = 0; r < buffer.length; r++) {
                const canvasY = topLeftY + r;
                if (canvasY >= 0 && canvasY < currentGridSize) {
                    if (!activeLayer.pixels[canvasY]) {
                        activeLayer.pixels[canvasY] = Array(currentGridSize).fill(EMPTY_COLOR);
                    }
                    for (let c = 0; c < buffer[r].length; c++) {
                        const canvasX = topLeftX + c;
                        if (canvasX >= 0 && canvasX < currentGridSize) {
                            const pixelColorFromBuffer = buffer[r][c];

                            if (currentSelectionPasteMode === 'PASTE_MODE_IGNORE_TRANSPARENT') {
                                if (pixelColorFromBuffer !== EMPTY_COLOR && pixelColorFromBuffer !== undefined) {
                                    if (activeLayer.pixels[canvasY][canvasX] !== pixelColorFromBuffer) {
                                        activeLayer.pixels[canvasY][canvasX] = pixelColorFromBuffer;
                                        expandDirtyRect(canvasX, canvasY);
                                        pixelsChanged = true;
                                    }
                                }
                            } else { // PASTE_MODE_REPLACE_ALL (for moves and flips)
                                if (activeLayer.pixels[canvasY][canvasX] !== pixelColorFromBuffer) {
                                    activeLayer.pixels[canvasY][canvasX] = pixelColorFromBuffer;
                                    expandDirtyRect(canvasX, canvasY);
                                    pixelsChanged = true;
                                }
                            }
                        }
                    }
                }
            }
            return pixelsChanged;
        }
        function floodFill(x, y, targetColorRgba, fillColorHex) {
            if (!activeLayer || !activeLayer.pixels) return;
            const layerPixels = activeLayer.pixels;

            if (x < 0 || x >= currentGridSize || y < 0 || y >= currentGridSize) return;

            const actualFillRgbaString = currentFillType === 'transparent_fill' ? EMPTY_COLOR : hexToRgba(fillColorHex, currentFillOpacity);

            if (actualFillRgbaString === targetColorRgba && currentFillType !== 'transparent_fill') return; // Filling with same color (unless erasing)

            // Determine if we need to use pattern fill
            const isPatternFill = currentFillType !== 'normal_fill' && currentFillType !== 'transparent_fill';
            
            // Pre-calculate pattern parameters for better performance
            let scale = currentHatchScale;
            let mid = 0;
            if (isPatternFill && (currentFillType === 'grid_hatch_fill' || currentFillType === 'dot_hatch_fill')) {
                scale = currentHatchScale + 1;
                if (scale < 2) scale = 2;
                mid = Math.floor((scale - 1) / 2);
            }
            
            // For large canvases, use a more efficient approach
            const isLargeCanvas = currentGridSize >= 256;
            
            // Track the dirty region for optimized redraw
            let minDirtyX = x, maxDirtyX = x, minDirtyY = y, maxDirtyY = y;
            
            // Use a queue-based approach for better memory efficiency
            // For large canvases, use a more efficient visited tracking method
            let visited;
            let stack;
            
            if (isLargeCanvas) {
                // For large canvases, use a 2D array for visited tracking (faster than Set for large areas)
                visited = Array(currentGridSize);
                for (let i = 0; i < currentGridSize; i++) {
                    visited[i] = new Array(currentGridSize).fill(false);
                }
                visited[y][x] = true;
                
                // Use a queue instead of a stack for more predictable memory usage
                stack = [[x, y]];
            } else {
                // For smaller canvases, use the original Set approach
                visited = new Set([`${x},${y}`]);
                stack = [[x, y]];
            }
            
            // Process pixels in batches for large canvases to avoid UI freezing
            const BATCH_SIZE = isLargeCanvas ? 5000 : Number.MAX_SAFE_INTEGER;
            let pixelsProcessed = 0;
            
            // Scan line fill algorithm for horizontal spans (more efficient)
            function processStack() {
                while (stack.length > 0 && pixelsProcessed < BATCH_SIZE) {
                    pixelsProcessed++;
                    const [cX, cY] = stack.shift(); // Use shift() for queue behavior (FIFO)
                    
                    // Check if the current pixel has the target color
                    const existingPixelColorAtCurrentXY = (layerPixels[cY] && typeof layerPixels[cY][cX] !== 'undefined') ? 
                        layerPixels[cY][cX] : EMPTY_COLOR;
                    
                    if (existingPixelColorAtCurrentXY !== targetColorRgba) continue;
                    
                    // Find the leftmost pixel of the current span
                    let spanLeft = cX;
                    while (spanLeft > 0) {
                        const leftColor = (layerPixels[cY] && typeof layerPixels[cY][spanLeft-1] !== 'undefined') ? 
                            layerPixels[cY][spanLeft-1] : EMPTY_COLOR;
                        if (leftColor !== targetColorRgba) break;
                        
                        if (isLargeCanvas) {
                            if (visited[cY][spanLeft-1]) break;
                        } else {
                            if (visited.has(`${spanLeft-1},${cY}`)) break;
                        }
                        
                        spanLeft--;
                    }
                    
                    // Find the rightmost pixel of the current span
                    let spanRight = cX;
                    while (spanRight < currentGridSize - 1) {
                        const rightColor = (layerPixels[cY] && typeof layerPixels[cY][spanRight+1] !== 'undefined') ? 
                            layerPixels[cY][spanRight+1] : EMPTY_COLOR;
                        if (rightColor !== targetColorRgba) break;
                        
                        if (isLargeCanvas) {
                            if (visited[cY][spanRight+1]) break;
                        } else {
                            if (visited.has(`${spanRight+1},${cY}`)) break;
                        }
                        
                        spanRight++;
                    }
                    
                    // Fill the current span
                    for (let i = spanLeft; i <= spanRight; i++) {
                        // Mark as visited
                        if (isLargeCanvas) {
                            visited[cY][i] = true;
                        } else {
                            visited.add(`${i},${cY}`);
                        }
                        
                        // Determine if we should apply the pattern at this pixel
                        let shouldApplyPatternPixel = true;
                        if (isPatternFill) {
                            const relX = i % scale, relY = cY % scale;
                            switch (currentFillType) {
                                case 'hatch_fill': 
                                    shouldApplyPatternPixel = (Math.floor(i / scale) + Math.floor(cY / scale)) % 2 === 0; 
                                    break;
                                case 'cross_hatch_fill': 
                                    shouldApplyPatternPixel = (Math.floor(i / scale) + Math.floor(cY / scale)) % 2 !== 0; 
                                    break;
                                case 'grid_hatch_fill': 
                                    shouldApplyPatternPixel = (relX === mid || relY === mid); 
                                    break;
                                case 'dot_hatch_fill': 
                                    shouldApplyPatternPixel = (relX !== mid && relY !== mid); 
                                    break;
                            }
                        }
                        
                        // Apply the fill color if the pattern allows
                        if (shouldApplyPatternPixel) {
                            if (!layerPixels[cY]) layerPixels[cY] = Array(currentGridSize).fill(EMPTY_COLOR);
                            if (layerPixels[cY][i] !== actualFillRgbaString) {
                                layerPixels[cY][i] = actualFillRgbaString;
                                
                                // Update dirty region
                                minDirtyX = Math.min(minDirtyX, i);
                                maxDirtyX = Math.max(maxDirtyX, i);
                                minDirtyY = Math.min(minDirtyY, cY);
                                maxDirtyY = Math.max(maxDirtyY, cY);
                            }
                        }
                    }
                    
                    // Check for spans above and below the current span
                    // and add seed points to the stack
                    const checkNeighborSpan = (neighborY) => {
                        if (neighborY >= 0 && neighborY < currentGridSize) {
                            let inSpan = false;
                            
                            for (let i = spanLeft; i <= spanRight; i++) {
                                const neighborColor = (layerPixels[neighborY] && typeof layerPixels[neighborY][i] !== 'undefined') ? 
                                    layerPixels[neighborY][i] : EMPTY_COLOR;
                                
                                const isVisited = isLargeCanvas ? 
                                    visited[neighborY][i] : 
                                    visited.has(`${i},${neighborY}`);
                                
                                if (neighborColor === targetColorRgba && !isVisited) {
                                    if (!inSpan) {
                                        // Start a new span
                                        stack.push([i, neighborY]);
                                        inSpan = true;
                                    }
                                } else if (inSpan) {
                                    // End the current span
                                    inSpan = false;
                                }
                            }
                        }
                    };
                    
                    // Check spans above and below
                    checkNeighborSpan(cY - 1);
                    checkNeighborSpan(cY + 1);
                }
                
                // If there are still pixels to process, continue in the next frame
                if (stack.length > 0) {
                    setTimeout(processStack, 0);
                } else {
                    // Update the dirty rect with the filled region
                    for (let y = minDirtyY; y <= maxDirtyY; y++) {
                        for (let x = minDirtyX; x <= maxDirtyX; x++) {
                            expandDirtyRect(x, y);
                        }
                    }
                    
                    // For large canvases, use dirty rect redraw instead of full redraw
                    if (isLargeCanvas) {
                        isCanvasDirtyForTools = true;
                    } else {
                        requestFullRedraw();
                    }
                }
            }
            
            // Start processing
            processStack();
        }


        function drawLine(x0,y0,x1,y1,c){const dX=Math.abs(x1-x0),dY=Math.abs(y1-y0),sX=x0<x1?1:-1,sY=y0<y1?1:-1;let e=dX-dY;while(!0){applyBrush(x0,y0,c);if(x0===x1&&y0===y1)break;const e2=2*e;if(e2>-dY){e-=dY;x0+=sX}if(e2<dX){e+=dX;y0+=sY}}}
        function drawRectangle(x0,y0,x1,y1,c){const mX=Math.min(x0,x1),MX=Math.max(x0,x1),mY=Math.min(y0,y1),MY=Math.max(y0,y1);for(let i=mX;i<=MX;i++){applyBrush(i,mY,c);applyBrush(i,MY,c)}for(let i=mY+1;i<MY;i++){applyBrush(mX,i,c);applyBrush(MX,i,c)}}
        function plotCirclePoints(cX,cY,x,y,c){applyBrush(cX+x,cY+y,c);applyBrush(cX-x,cY+y,c);applyBrush(cX+x,cY-y,c);applyBrush(cX-x,cY-y,c);if(x!==y){applyBrush(cX+y,cY+x,c);applyBrush(cX-y,cY+x,c);applyBrush(cX+y,cY-x,c);applyBrush(cX-y,cY-x,c)}}
        function drawCircle(xS,yS,xE,yE,c,isPerf){if(isPerf){const cX=xS,cY=yS,r=Math.round(Math.sqrt((xE-cX)**2+(yE-cY)**2));if(r<0)return;if(r===0){applyBrush(cX,cY,c);return}let x=r,y=0,p=1-r;plotCirclePoints(cX,cY,x,y,c);while(x>y){y++;if(p<=0)p=p+2*y+1;else{x--;p=p+2*y-2*x+1}plotCirclePoints(cX,cY,x,y,c)}}else{const mGX=Math.min(xS,xE),MGX=Math.max(xS,xE),mGY=Math.min(yS,yE),MGY=Math.max(yS,yE);const cX=(mGX+MGX)/2,rX=(MGX-mGX)/2,cY=(mGY+MGY)/2,rY=(MGY-mGY)/2;if(rX<.001&&rY<.001)applyBrush(Math.round(cX),Math.round(cY),c);else if(rX<.001)for(let gY=mGY;gY<=MGY;gY++)applyBrush(Math.round(cX),gY,c);else if(rY<.001)for(let gX=mGX;gX<=MGX;gX++)applyBrush(gX,Math.round(cY),c);else{for(let gX=mGX;gX<=MGX;gX++){const xR=gX-cX,yOSq=rY**2*(1-(xR**2)/(rX**2));if(yOSq>=-1e-5){const yO=Math.sqrt(Math.max(0,yOSq));applyBrush(gX,Math.round(cY+yO),c);applyBrush(gX,Math.round(cY-yO),c)}}for(let gY=mGY;gY<=MGY;gY++){const yR=gY-cY,xOSq=rX**2*(1-(yR**2)/(rY**2));if(xOSq>=-1e-5){const xO=Math.sqrt(Math.max(0,xOSq));applyBrush(Math.round(cX+xO),gY,c);applyBrush(Math.round(cX-xO),gY,c)}}}}}
        function previewLineOnGrid(lX0,lY0,lX1,lY1,cH,pT){if(renderedCellSize<=0)return;const sX0=(lX0-currentViewOffsetX+.5)*renderedCellSize,sY0=(lY0-currentViewOffsetY+.5)*renderedCellSize,sX1=(lX1-currentViewOffsetX+.5)*renderedCellSize,sY1=(lY1-currentViewOffsetY+.5)*renderedCellSize;gridCtx.beginPath();gridCtx.moveTo(Math.round(sX0),Math.round(sY0));gridCtx.lineTo(Math.round(sX1),Math.round(sY1));gridCtx.strokeStyle=hexToRgba(cH,currentBrushOpacity);gridCtx.lineWidth=Math.max(1,pT*renderedCellSize*.2,pT*.5);gridCtx.lineCap='round';gridCtx.lineJoin='round';gridCtx.stroke()}
        function previewRectangleOnGrid(lX0,lY0,lX1,lY1,cH,pT){if(renderedCellSize<=0)return;const mLogX=Math.min(lX0,lX1),maxLogX=Math.max(lX0,lX1),mLogY=Math.min(lY0,lY1),maxLogY=Math.max(lY0,lY1);const mSX=(mLogX-currentViewOffsetX)*renderedCellSize,maxSXPC=(maxLogX+1-currentViewOffsetX)*renderedCellSize,mSY=(mLogY-currentViewOffsetY)*renderedCellSize,maxSYPC=(maxLogY+1-currentViewOffsetY)*renderedCellSize;gridCtx.strokeStyle=hexToRgba(cH,currentBrushOpacity);const lW=Math.max(1,pT*renderedCellSize*.2,pT*.5);gridCtx.lineWidth=lW;const off=lW/2;gridCtx.strokeRect(Math.round(mSX+off),Math.round(mSY+off),Math.round((maxSXPC-mSX)-lW),Math.round((maxSYPC-mSY)-lW))}
        function previewCircleOnGrid(lSX,lSY,lCX,lCY,cH,pT,isPerf){if(renderedCellSize<=0)return;const pCRgba=hexToRgba(cH,currentBrushOpacity),lW=Math.max(1,pT*renderedCellSize*.2,pT*.5);if(isPerf){const rIC=Math.round(Math.sqrt((lCX-lSX)**2+(lCY-lSY)**2)),sCX_f=(lSX-currentViewOffsetX+.5)*renderedCellSize,sCY_f=(lSY-currentViewOffsetY+.5)*renderedCellSize;let sR_f=rIC*renderedCellSize;if(sR_f<=0&&rIC===0){gridCtx.beginPath();gridCtx.arc(Math.round(sCX_f),Math.round(sCY_f),Math.max(1,lW/2,renderedCellSize*.2*pT),0,2*Math.PI);gridCtx.fillStyle=pCRgba;gridCtx.fill();return}if(sR_f<=0)return;gridCtx.beginPath();sR_f=Math.max(lW/2,sR_f);gridCtx.arc(Math.round(sCX_f),Math.round(sCY_f),Math.round(sR_f),0,2*Math.PI,!1);gridCtx.strokeStyle=pCRgba;gridCtx.lineWidth=lW;gridCtx.stroke()}else{const rX0L=Math.min(lSX,lCX),rY0L=Math.min(lSY,lCY),rX1L=Math.max(lSX,lCX),rY1L=Math.max(lSY,lCY);const s_rX0=(rX0L-currentViewOffsetX)*renderedCellSize,s_rY0=(rY0L-currentViewOffsetY)*renderedCellSize,s_rX1=(rX1L+1-currentViewOffsetX)*renderedCellSize,s_rY1=(rY1L+1-currentViewOffsetY)*renderedCellSize;const sW=s_rX1-s_rX0,sH=s_rY1-s_rY0,sRX=sW/2,sRY=sH/2,sCX=s_rX0+sRX,sCY=s_rY0+sRY;if(sW<=0||sH<=0)return;if(sRX<lW/2||sRY<lW/2){const ePR=Math.max(1,lW/2,renderedCellSize*.1*Math.max(1,pT));gridCtx.beginPath();gridCtx.arc(Math.round(sCX),Math.round(sCY),ePR,0,2*Math.PI);gridCtx.fillStyle=pCRgba;gridCtx.fill();return}gridCtx.beginPath();gridCtx.ellipse(Math.round(sCX),Math.round(sCY),Math.round(sRX),Math.round(sRY),0,0,2*Math.PI);gridCtx.strokeStyle=pCRgba;gridCtx.lineWidth=lW;gridCtx.stroke()}}
        function getCell(e){const r=gridCanvas.getBoundingClientRect();if(renderedCellSize<=0||currentGridSize<=0)return{x:-1,y:-1};const mXIC=e.clientX-r.left,mYIC=e.clientY-r.top;return{x:Math.floor(mXIC/renderedCellSize+currentViewOffsetX),y:Math.floor(mYIC/renderedCellSize+currentViewOffsetY)}}
        function updateCursor(){const cT=toolSelector.value;if(isPanning){pixelCanvas.style.cursor='grabbing';return}switch(cT){case'pan':pixelCanvas.style.cursor='grab';break;case'eyedropper':case'draw':case'erase':case'fill':case'line':case'rectangle':case'circle':case'select':pixelCanvas.style.cursor='crosshair';break;default:const nPPT=['draw','erase','line','rectangle','circle','select','eyedropper','fill'];pixelCanvas.style.cursor=currentZoomFactor>1.01&&!nPPT.includes(cT)?'grab':'crosshair';break}}
                // MINIMAP HELPER FUNCTIONS START

        // Checks if the current view is zoomed in enough that the entire canvas isn't visible
        // --- NEW HELPER FUNCTION ---
        // --- MODIFIED HELPER FUNCTION ---
        // --- REVISED HELPER FUNCTION ---
        // --- REVISED HELPER FUNCTION for New Canvas Preview Content ---
        function createPreviewContentForNewSize(currentCanvasState, newGridSizeVal) {
            const newGridSize = parseInt(newGridSizeVal);

            // Fallback for when there's no current canvas to get content from
            if (!currentCanvasState || !currentCanvasState.layers || currentCanvasState.layers.length === 0) {
                return {
                    layers: [{ pixels: createNewPixelData(newGridSize), isVisible: true, opacity: 100 }],
                    gridSize: newGridSize
                };
            }

            const oldGridSize = currentCanvasState.gridSize;

            // 1. Composite all visible layers of the current canvas into a single 2D pixel array.
            const compositedOldPixels = createNewPixelData(oldGridSize);
            const tempCompositeReadCanvas = document.createElement('canvas');
            tempCompositeReadCanvas.width = oldGridSize;
            tempCompositeReadCanvas.height = oldGridSize;
            const tempCompositeReadCtx = tempCompositeReadCanvas.getContext('2d');
            tempCompositeReadCtx.imageSmoothingEnabled = false;

            // Optional: Base checkerboard if main canvas has it (for transparent areas in source)
            if (checkerboardToggle.checked && canvasContainer.classList.contains('checkerboard')) {
                const compCheckerSize = Math.max(1, Math.floor(oldGridSize / 16));
                tempCompositeReadCtx.fillStyle = '#fff';
                tempCompositeReadCtx.fillRect(0, 0, oldGridSize, oldGridSize);
                tempCompositeReadCtx.fillStyle = '#ccc';
                for (let i = 0; i * compCheckerSize < oldGridSize; i++) {
                    for (let j = 0; j * compCheckerSize < oldGridSize; j++) {
                        if ((i + j) % 2 === 0) {
                            tempCompositeReadCtx.fillRect(i * compCheckerSize, j * compCheckerSize, compCheckerSize, compCheckerSize);
                        }
                    }
                }
            }

            currentCanvasState.layers.forEach(layer => {
                if (layer.isVisible && layer.pixels) {
                    tempCompositeReadCtx.globalAlpha = layer.opacity / 100;
                    for (let y = 0; y < oldGridSize; y++) {
                        if (layer.pixels[y]) {
                            for (let x = 0; x < oldGridSize; x++) {
                                const pixelData = layer.pixels[y][x];
                                if (pixelData !== EMPTY_COLOR && pixelData !== undefined) {
                                    tempCompositeReadCtx.fillStyle = pixelData;
                                    tempCompositeReadCtx.fillRect(x, y, 1, 1);
                                }
                            }
                        }
                    }
                }
            });
            tempCompositeReadCtx.globalAlpha = 1.0;

            // Read the flat composited image data into our 2D array format.
            const sourceImageData = tempCompositeReadCtx.getImageData(0, 0, oldGridSize, oldGridSize).data;
            for (let y = 0; y < oldGridSize; y++) {
                for (let x = 0; x < oldGridSize; x++) {
                    const idx = (y * oldGridSize + x) * 4;
                    const r = sourceImageData[idx];
                    const g = sourceImageData[idx + 1];
                    const b = sourceImageData[idx + 2];
                    const a = sourceImageData[idx + 3];
                    if (a > 10) { // Alpha threshold
                        compositedOldPixels[y][x] = `rgba(${r},${g},${b},${(a / 255).toFixed(3)})`;
                    } else {
                        compositedOldPixels[y][x] = EMPTY_COLOR;
                    }
                }
            }

            // 2. Create the target pixel data array for the newGridSize, initially empty.
            let newPreviewPixelData = createNewPixelData(newGridSize);

            // 3. Copy the relevant portion from compositedOldPixels to newPreviewPixelData.
            // This mimics the actual canvas resize behavior (cropping or padding).
            const rowsToCopy = Math.min(oldGridSize, newGridSize);
            const colsToCopy = Math.min(oldGridSize, newGridSize);

            for (let y = 0; y < rowsToCopy; y++) {
                if (compositedOldPixels[y]) { // Check if the source row exists
                    for (let x = 0; x < colsToCopy; x++) {
                        // Check if the source pixel exists
                        if (typeof compositedOldPixels[y][x] !== 'undefined') {
                            newPreviewPixelData[y][x] = compositedOldPixels[y][x];
                        }
                    }
                }
            }

            return {
                layers: [{ pixels: newPreviewPixelData, isVisible: true, opacity: 100 }],
                gridSize: newGridSize // This is the dimension of newPreviewPixelData
            };
        }
        function isViewEffectivelyZoomedIn() {
            if (currentGridSize <= 0 || renderedCellSize <= 0 || !pixelCanvas || pixelCanvas.width === 0 || pixelCanvas.height === 0) return false;
            const viewportCellsX = pixelCanvas.width / renderedCellSize;
            const viewportCellsY = pixelCanvas.height / renderedCellSize;
            return viewportCellsX < currentGridSize - 0.01 || viewportCellsY < currentGridSize - 0.01;
        }
        function resampleSelectionBuffer(sourceBuffer, sourceW, sourceH, targetW, targetH) {
            if (!sourceBuffer || sourceBuffer.length === 0 || sourceW <= 0 || sourceH <= 0) {
                if (targetW > 0 && targetH > 0) {
                    const emptyTarget = [];
                    for (let y = 0; y < targetH; y++) {
                        emptyTarget.push(Array(targetW).fill(EMPTY_COLOR));
                    }
                    return emptyTarget;
                }
                return [];
            }
            // Ensure target dimensions are at least MIN_SELECTION_DIM
            targetW = Math.max(MIN_SELECTION_DIM, targetW);
            targetH = Math.max(MIN_SELECTION_DIM, targetH);

            const newBuffer = [];
            const x_ratio = sourceW / targetW;
            const y_ratio = sourceH / targetH;

            for (let y = 0; y < targetH; y++) {
                const newRow = Array(targetW);
                const sy = Math.floor(y * y_ratio);
                const clampedSy = Math.max(0, Math.min(sy, sourceH - 1));

                for (let x = 0; x < targetW; x++) {
                    const sx = Math.floor(x * x_ratio);
                    const clampedSx = Math.max(0, Math.min(sx, sourceW - 1));

                    if (sourceBuffer[clampedSy] && sourceBuffer[clampedSy][clampedSx] !== undefined) {
                        newRow[x] = sourceBuffer[clampedSy][clampedSx];
                    } else {
                        newRow[x] = EMPTY_COLOR;
                    }
                }
                newBuffer.push(newRow);
            }
            return newBuffer;
        }
        function getHandleScreenPositions(minGridX, minGridY, maxGridX, maxGridY) {
            const sX0 = (minGridX - currentViewOffsetX) * renderedCellSize;
            const sY0 = (minGridY - currentViewOffsetY) * renderedCellSize;
            const sX1 = (maxGridX + 1 - currentViewOffsetX) * renderedCellSize;
            const sY1 = (maxGridY + 1 - currentViewOffsetY) * renderedCellSize;
            const sMidX = (sX0 + sX1) / 2;
            const sMidY = (sY0 + sY1) / 2;
            return {
                'topLeft':     { screenX: sX0, screenY: sY0, cursor: 'nwse-resize', type: 'corner' },
                'topMid':      { screenX: sMidX, screenY: sY0, cursor: 'ns-resize',   type: 'edge'   },
                'topRight':    { screenX: sX1, screenY: sY0, cursor: 'nesw-resize', type: 'corner' },
                'leftMid':     { screenX: sX0, screenY: sMidY, cursor: 'ew-resize',   type: 'edge'   },
                'rightMid':    { screenX: sX1, screenY: sMidY, cursor: 'ew-resize',   type: 'edge'   },
                'bottomLeft':  { screenX: sX0, screenY: sY1, cursor: 'nesw-resize', type: 'corner' },
                'bottomMid':   { screenX: sMidX, screenY: sY1, cursor: 'ns-resize',   type: 'edge'   },
                'bottomRight': { screenX: sX1, screenY: sY1, cursor: 'nwse-resize', type: 'corner' }
            };
        }
        function finalizeMoveSelection() {
            let successfullyPlaced = false;
            if (isMovingSelection && movingSelectionBuffer && movingSelectionCurrentTopLeft) {
                if (!activeLayer || !activeLayer.isVisible) {
                    alert("Cannot place selection: Target layer is not visible or does not exist. Operation cancelled.");
                    if (originalSelectionBeforeMove && originalSelectionBeforeMove.start) {
                        currentSelectionStart = { ...originalSelectionBeforeMove.start };
                        currentSelectionEnd = { ...originalSelectionBeforeMove.end };
                        isSelecting = true;
                    } else {
                        currentSelectionStart = null; currentSelectionEnd = null; isSelecting = false;
                    }
                } else {
                    saveState();
                    // Set paste mode to ignore transparent pixels when moving selections
                    const previousPasteMode = currentSelectionPasteMode;
                    currentSelectionPasteMode = 'PASTE_MODE_IGNORE_TRANSPARENT';
                    _pasteSelectionBuffer(movingSelectionBuffer, movingSelectionCurrentTopLeft.x, movingSelectionCurrentTopLeft.y);
                    currentSelectionPasteMode = previousPasteMode; // Restore previous mode
                    successfullyPlaced = true;
                    const selWidth = movingSelectionBuffer[0] ? movingSelectionBuffer[0].length : 0;
                    const selHeight = movingSelectionBuffer.length;
                    currentSelectionStart = { x: movingSelectionCurrentTopLeft.x, y: movingSelectionCurrentTopLeft.y };
                    currentSelectionEnd = { x: movingSelectionCurrentTopLeft.x + selWidth - 1, y: movingSelectionCurrentTopLeft.y + selHeight - 1 };
                    isSelecting = true;
                }
                if (canvases[activeCanvasIndex]) {
                    canvases[activeCanvasIndex].selectionStart = currentSelectionStart ? { ...currentSelectionStart } : null;
                    canvases[activeCanvasIndex].selectionEnd = currentSelectionEnd ? { ...currentSelectionEnd } : null;
                    canvases[activeCanvasIndex].isSelecting = isSelecting;
                }
            }

            if (isMovingSelection) {
                isMovingSelection = false;
                isInitialPasteFloat = false;
                movingSelectionBuffer = null;
                movingSelectionDelta = null;
                originalSelectionBeforeMove = null;
                currentSelectionPasteMode = 'PASTE_MODE_REPLACE_ALL';
            }
            updateBrushOptionsBarContent();
            requestFullRedraw();
            updateCursor();
        }
        function renderMinimapContent(targetMinimapCtx, minimapDisplayWidth, minimapDisplayHeight) {
            if (!currentLayers || currentLayers.length === 0 || currentGridSize <= 0) return;
            if (!tempMinimapCompositeCanvas || tempMinimapCompositeCanvas.width !== currentGridSize || tempMinimapCompositeCanvas.height !== currentGridSize) {
                tempMinimapCompositeCanvas = document.createElement('canvas');
                tempMinimapCompositeCanvas.width = currentGridSize;
                tempMinimapCompositeCanvas.height = currentGridSize;
                tempMinimapCompositeCtx = tempMinimapCompositeCanvas.getContext('2d');
                tempMinimapCompositeCtx.imageSmoothingEnabled = false;
            }
            tempMinimapCompositeCtx.clearRect(0, 0, currentGridSize, currentGridSize);
            for (const layer of currentLayers) {
                if (!layer.isVisible || !layer.pixels) continue;
                tempMinimapCompositeCtx.globalAlpha = layer.opacity / 100;
                for (let rI = 0; rI < currentGridSize; rI++) {
                    if (!layer.pixels[rI]) continue;
                    for (let cI = 0; cI < currentGridSize; cI++) {
                        let cellColor = layer.pixels[rI][cI];
                        if (cellColor && cellColor !== EMPTY_COLOR) {
                            tempMinimapCompositeCtx.fillStyle = cellColor;
                            tempMinimapCompositeCtx.fillRect(cI, rI, 1, 1);
                        }
                    }
                }
            }
            tempMinimapCompositeCtx.globalAlpha = 1.0;
            targetMinimapCtx.clearRect(0, 0, minimapDisplayWidth, minimapDisplayHeight);
            if (checkerboardToggle.checked && canvasContainer.classList.contains('checkerboard')) {
                const mcs = Math.max(2, Math.floor(minimapDisplayWidth / (currentGridSize / 4)));
                targetMinimapCtx.fillStyle = '#cccccc';
                for (let i = 0; i * mcs < minimapDisplayWidth; i++) {
                    for (let j = 0; j * mcs < minimapDisplayHeight; j++) {
                        if ((i + j) % 2 === 0) {
                            targetMinimapCtx.fillRect(i * mcs, j * mcs, mcs, mcs);
                        }
                    }
                }
            }
            targetMinimapCtx.imageSmoothingEnabled = false;
            targetMinimapCtx.drawImage(tempMinimapCompositeCanvas, 0, 0, currentGridSize, currentGridSize,
                                    0, 0, minimapDisplayWidth, minimapDisplayHeight);
        }

        function renderMinimap() {
            if (!minimapCanvasElement || !minimapCtx || currentGridSize <= 0) return;
            const minimapDisplaySize = MINIMAP_MAX_SIZE;
            minimapCanvasElement.width = minimapDisplaySize;
            minimapCanvasElement.height = minimapDisplaySize;
            const minimapCellSize = minimapDisplaySize / currentGridSize;
            renderMinimapContent(minimapCtx, minimapDisplaySize, minimapDisplaySize);
            if (renderedCellSize <= 0) return;
            const viewportLogicWidth = pixelCanvas.width / renderedCellSize;
            const viewportLogicHeight = pixelCanvas.height / renderedCellSize;
            const indicatorX = currentViewOffsetX * minimapCellSize;
            const indicatorY = currentViewOffsetY * minimapCellSize;
            const indicatorWidth = Math.max(1, viewportLogicWidth * minimapCellSize);
            const indicatorHeight = Math.max(1, viewportLogicHeight * minimapCellSize);
            minimapCtx.strokeStyle = 'red';
            minimapCtx.lineWidth = Math.max(1, Math.floor(minimapDisplaySize / 100));
            minimapCtx.strokeRect(indicatorX + 0.5, indicatorY + 0.5, indicatorWidth -1 , indicatorHeight -1);
        }
        function drawMovingSelectionPreview() {
            if (!isMovingSelection || !movingSelectionBuffer || !movingSelectionCurrentTopLeft || renderedCellSize <= 0 || !activeLayer || currentActiveLayerIndex === -1) {
                return;
            }

            const previewTopLeftX = movingSelectionCurrentTopLeft.x;
            const previewTopLeftY = movingSelectionCurrentTopLeft.y;

            for (let r_buffer = 0; r_buffer < movingSelectionBuffer.length; r_buffer++) {
                const bufferRow = movingSelectionBuffer[r_buffer];
                for (let c_buffer = 0; c_buffer < bufferRow.length; c_buffer++) {
                    const rawMovingPixelColor = bufferRow[c_buffer];

                    // Skip if the selection's pixel itself is empty/transparent
                    if (rawMovingPixelColor === EMPTY_COLOR || rawMovingPixelColor === undefined) {
                        continue;
                    }

                    const gX = previewTopLeftX + c_buffer; // Grid X of the current preview pixel
                    const gY = previewTopLeftY + r_buffer; // Grid Y of the current preview pixel

                    // Skip if pixel is outside canvas bounds
                    if (gX < 0 || gX >= currentGridSize || gY < 0 || gY >= currentGridSize) {
                        continue;
                    }

                    // 1. Start with the effective color of the moving pixel from its original layer
                    let currentBlendedRgba = getEffectivePixelRgba(rawMovingPixelColor, activeLayer.opacity);

                    // If the moving pixel itself (after its layer opacity) is transparent, nothing to draw from it.
                    if (currentBlendedRgba.a < 0.001) {
                        continue;
                    }

                    // 2. Iterate through layers *above* the active layer and blend them on top
                    for (let layerIdx = currentActiveLayerIndex + 1; layerIdx < currentLayers.length; layerIdx++) {
                        const upperLayer = currentLayers[layerIdx];
                        if (!upperLayer.isVisible || !upperLayer.pixels) {
                            continue;
                        }

                        // Check bounds for the upper layer's pixel data
                        if (gY >= 0 && gY < currentGridSize && upperLayer.pixels[gY] &&
                            gX >= 0 && gX < currentGridSize) {

                            const upperPixelRawColor = upperLayer.pixels[gY][gX];

                            if (upperPixelRawColor !== EMPTY_COLOR && upperPixelRawColor !== undefined) {
                                const upperPixelEffectiveRgba = getEffectivePixelRgba(upperPixelRawColor, upperLayer.opacity);

                                if (upperPixelEffectiveRgba.a > 0.001) {
                                    // Blend: 'currentBlendedRgba' is the background (what's "below" so far),
                                    // 'upperPixelEffectiveRgba' is the source (what's "on top" from this upper layer).
                                    currentBlendedRgba = blendRgbaObjects(currentBlendedRgba, upperPixelEffectiveRgba);
                                }
                            }
                        }

                        // Optimization: If the current blended color becomes fully opaque from an upper layer,
                        // no layers above that can make it more transparent, only change its color if they themselves are opaque.
                        // However, for simplicity and to correctly account for color changes even if alpha is 1,
                        // we'll continue blending. If performance becomes an issue, this is an area for optimization.
                    }

                    // 3. Draw the final blended color onto the gridCanvas if it's visible
                    if (currentBlendedRgba.a > 0.001) {
                        const finalPreviewColorString = `rgba(${currentBlendedRgba.r},${currentBlendedRgba.g},${currentBlendedRgba.b},${currentBlendedRgba.a})`;
                        drawSinglePixelCellOnGrid(gX, gY, finalPreviewColorString);
                    }
                }
            }

            // Draw the selection border (this part remains the same as your existing logic)
            const selWidth = movingSelectionBuffer[0] ? movingSelectionBuffer[0].length : 0;
            const selHeight = movingSelectionBuffer.length;

            if (selWidth > 0 && selHeight > 0) {
                const screenX = (previewTopLeftX - currentViewOffsetX) * renderedCellSize;
                const screenY = (previewTopLeftY - currentViewOffsetY) * renderedCellSize;
                const screenW = selWidth * renderedCellSize;
                const screenH = selHeight * renderedCellSize;

                if (screenX < gridCanvas.width && screenX + screenW > 0 &&
                    screenY < gridCanvas.height && screenY + screenH > 0) {

                    gridCtx.strokeStyle = 'rgba(0, 100, 255, 0.75)';
                    gridCtx.lineWidth = 1;
                    gridCtx.setLineDash([3, 2]);
                    gridCtx.strokeRect(
                        Math.round(screenX) + 0.5,
                        Math.round(screenY) + 0.5,
                        Math.round(screenW) - 1,
                        Math.round(screenH) - 1
                    );
                    gridCtx.setLineDash([]);
                }
            }
        }
        function stopPanAndHideMinimap() {
        if (isPanning) {
            isPanning = false;
            // For right-click pan, isRightButtonPanning is set false separately
            // For pan tool, the tool remains 'pan'
            updateCursor(); // Reverts cursor from 'grabbing'
            if (minimapCanvasElement) {
                minimapCanvasElement.style.display = 'none';
            }
        }
    }
        function applyOcclusionMaskForMovingSelection() {
            if (!isMovingSelection || !movingSelectionBuffer || !movingSelectionCurrentTopLeft || !activeLayer || currentActiveLayerIndex === -1) {
                return;
            }

            const previewTopLeftX = movingSelectionCurrentTopLeft.x;
            const previewTopLeftY = movingSelectionCurrentTopLeft.y;
            const selWidth = movingSelectionBuffer[0] ? movingSelectionBuffer[0].length : 0;
            const selHeight = movingSelectionBuffer.length;

            if (selWidth === 0 || selHeight === 0) return;

            // Iterate through the grid cells where the moving selection preview will be drawn
            for (let r_buffer = 0; r_buffer < selHeight; r_buffer++) {
                for (let c_buffer = 0; c_buffer < selWidth; c_buffer++) {
                    const gX = previewTopLeftX + c_buffer; // Grid X of the current preview pixel
                    const gY = previewTopLeftY + r_buffer; // Grid Y of the current preview pixel

                    if (gX < 0 || gX >= currentGridSize || gY < 0 || gY >= currentGridSize) continue;

                    // Check layers above the activeLayer
                    for (let layerIdx = currentActiveLayerIndex + 1; layerIdx < currentLayers.length; layerIdx++) {
                        const upperLayer = currentLayers[layerIdx];
                        if (upperLayer.isVisible && upperLayer.pixels && upperLayer.pixels[gY]) {
                            const upperPixelColorString = upperLayer.pixels[gY][gX];
                            if (upperPixelColorString && upperPixelColorString !== EMPTY_COLOR) {
                                const upperPixelRgba = parseRgba(upperPixelColorString);
                                const effectiveUpperAlpha = upperPixelRgba.a * (upperLayer.opacity / 100.0);

                                // If the upper layer pixel is significantly opaque, clear this part of the gridCtx
                                // A threshold of 0.5 means if the upper layer pixel (considering its own alpha and layer opacity)
                                // is more than 50% opaque, it occludes the moving selection preview.
                                if (effectiveUpperAlpha > 0.5) {
                                    const screenXStart = (gX - currentViewOffsetX) * renderedCellSize;
                                    const screenYStart = (gY - currentViewOffsetY) * renderedCellSize;
                                    const screenXEnd = (gX + 1 - currentViewOffsetX) * renderedCellSize;
                                    const screenYEnd = (gY + 1 - currentViewOffsetY) * renderedCellSize;

                                    const drawX = Math.round(screenXStart);
                                    const drawY = Math.round(screenYStart);
                                    const drawWidth = Math.round(screenXEnd) - drawX;
                                    const drawHeight = Math.round(screenYEnd) - drawY;

                                    if (drawWidth > 0 && drawHeight > 0) {
                                        gridCtx.clearRect(drawX, drawY, drawWidth, drawHeight);
                                    }
                                    break; // Occluded by this upperLayer, no need to check further upper layers for this (gX,gY)
                                }
                            }
                        }
                    }
                }
            }
        }
        function getEffectivePixelRgba(pixelColorString, layerOpacityPercent) {
            if (pixelColorString === EMPTY_COLOR || pixelColorString === undefined) {
                return { r: 0, g: 0, b: 0, a: 0 }; // Fully transparent
            }
            const parsed = parseRgba(pixelColorString); // Assumes parseRgba returns {r,g,b,a} with a from 0-1
            const layerAlphaFactor = Math.max(0, Math.min(1, layerOpacityPercent / 100.0));
            return {
                r: parsed.r,
                g: parsed.g,
                b: parsed.b,
                a: parsed.a * layerAlphaFactor
            };
        }
        // --- MODIFIED: drawOnPreviewCanvas ---
        // --- REVISED: drawOnPreviewCanvas for SHARPNESS with UPSCALE_FACTOR ---
        function drawOnPreviewCanvas(targetCtx, targetCanvasElement, sourceCanvasDataForPreview, displayDimensions, drawBlankPlaceholder = false) {
            targetCtx.imageSmoothingEnabled = false;
            targetCtx.clearRect(0, 0, targetCanvasElement.width, targetCanvasElement.height);

            targetCanvasElement.width = PREVIEW_BOX_SIZE;
            targetCanvasElement.height = PREVIEW_BOX_SIZE;

            // Calculate drawing area within the PREVIEW_BOX_SIZE, maintaining aspect ratio
            let drawContentWidth = PREVIEW_BOX_SIZE;
            let drawContentHeight = PREVIEW_BOX_SIZE;
            const contentAspectRatio = displayDimensions.width / Math.max(1, displayDimensions.height);

            if (PREVIEW_BOX_SIZE / contentAspectRatio <= PREVIEW_BOX_SIZE) { // Fit by width
                drawContentWidth = PREVIEW_BOX_SIZE;
                drawContentHeight = PREVIEW_BOX_SIZE / contentAspectRatio;
            } else { // Fit by height
                drawContentHeight = PREVIEW_BOX_SIZE;
                drawContentWidth = PREVIEW_BOX_SIZE * contentAspectRatio;
            }
            // Ensure dimensions are at least 1px to prevent errors
            drawContentWidth = Math.max(1, Math.round(drawContentWidth));
            drawContentHeight = Math.max(1, Math.round(drawContentHeight));

            const offsetX = (PREVIEW_BOX_SIZE - drawContentWidth) / 2;
            const offsetY = (PREVIEW_BOX_SIZE - drawContentHeight) / 2;

            if (drawBlankPlaceholder || !sourceCanvasDataForPreview || !sourceCanvasDataForPreview.layers || sourceCanvasDataForPreview.layers.length === 0 || !sourceCanvasDataForPreview.layers[0].pixels) {
                targetCtx.fillStyle = '#fff';
                targetCtx.fillRect(offsetX, offsetY, drawContentWidth, drawContentHeight);
                const checkerSize = Math.max(2, Math.floor(drawContentWidth / (Math.max(1, displayDimensions.width) / 4)));
                targetCtx.fillStyle = '#ccc';
                for (let i = 0; i * checkerSize < drawContentWidth; i++) {
                    for (let j = 0; j * checkerSize < drawContentHeight; j++) {
                        if ((i + j) % 2 === 0) {
                            targetCtx.fillRect(Math.round(offsetX + i * checkerSize), Math.round(offsetY + j * checkerSize), Math.ceil(checkerSize), Math.ceil(checkerSize));
                        }
                    }
                }
            } else {
                // 1. Create tempCompositeCanvas at the content's actual grid dimensions (e.g., 64x64)
                const tempCompositeCanvas = document.createElement('canvas');
                tempCompositeCanvas.width = displayDimensions.width;
                tempCompositeCanvas.height = displayDimensions.height;
                const tempCompositeCtx = tempCompositeCanvas.getContext('2d');
                tempCompositeCtx.imageSmoothingEnabled = false;

                // Draw background (checkerboard/white) onto this tempCompositeCanvas
                tempCompositeCtx.fillStyle = '#fff';
                tempCompositeCtx.fillRect(0, 0, displayDimensions.width, displayDimensions.height);
                if (checkerboardToggle.checked) {
                    const compCheckerSize = Math.max(1, Math.floor(displayDimensions.width / 8));
                    tempCompositeCtx.fillStyle = '#ccc';
                    for (let i = 0; i * compCheckerSize < displayDimensions.width; i++) {
                        for (let j = 0; j * compCheckerSize < displayDimensions.height; j++) {
                            if ((i + j) % 2 === 0) {
                                tempCompositeCtx.fillRect(i * compCheckerSize, j * compCheckerSize, compCheckerSize, compCheckerSize);
                            }
                        }
                    }
                }

                // Draw all layers of the source data onto tempCompositeCanvas
                sourceCanvasDataForPreview.layers.forEach(layer => {
                    if (layer.isVisible && layer.pixels) {
                        tempCompositeCtx.globalAlpha = layer.opacity / 100;
                        for (let y = 0; y < displayDimensions.height; y++) {
                            if (layer.pixels[y]) {
                                for (let x = 0; x < displayDimensions.width; x++) {
                                    const pixelData = layer.pixels[y][x];
                                    if (pixelData !== EMPTY_COLOR && pixelData !== undefined) {
                                        tempCompositeCtx.fillStyle = pixelData;
                                        tempCompositeCtx.fillRect(x, y, 1, 1);
                                    }
                                }
                            }
                        }
                    }
                });
                tempCompositeCtx.globalAlpha = 1.0;

                // 2. Create an UPSCALE_FACTOR canvas.
                const UPSCALE_FACTOR = 10;
                const upscaledCanvas = document.createElement('canvas');
                upscaledCanvas.width = Math.max(1, displayDimensions.width * UPSCALE_FACTOR);
                upscaledCanvas.height = Math.max(1, displayDimensions.height * UPSCALE_FACTOR);
                const upscaledCtx = upscaledCanvas.getContext('2d');
                upscaledCtx.imageSmoothingEnabled = false;

                // Draw tempCompositeCanvas (original res) onto upscaledCanvas (sharp upscale)
                upscaledCtx.drawImage(tempCompositeCanvas, 0, 0, displayDimensions.width, displayDimensions.height,
                                      0, 0, upscaledCanvas.width, upscaledCanvas.height);

                // 3. Draw the upscaledCanvas onto the target preview canvas, scaled to fit.
                targetCtx.drawImage(upscaledCanvas, 0, 0, upscaledCanvas.width, upscaledCanvas.height,
                                    offsetX, offsetY, drawContentWidth, drawContentHeight);
            }
        }
        // MINIMAP HELPER FUNCTIONS END
        pixelCanvas.addEventListener('contextmenu',e=>e.preventDefault());
        pixelCanvas.addEventListener('wheel',e=>{e.preventDefault();const r=pixelCanvas.getBoundingClientRect(),mXOC=e.clientX-r.left,mYOC=e.clientY-r.top;if(renderedCellSize<=0)return;const lGXUM=currentViewOffsetX+mXOC/renderedCellSize,lGYUM=currentViewOffsetY+mYOC/renderedCellSize;let nZF=parseFloat(zoomSlider.value);const zM=1.1;e.deltaY<0?nZF*=zM:nZF/=zM;nZF=Math.max(parseFloat(zoomSlider.min),Math.min(parseFloat(zoomSlider.max),nZF));if(Math.abs(nZF-currentZoomFactor)<1e-4&&((currentZoomFactor===parseFloat(zoomSlider.min)&&nZF<=currentZoomFactor)||(currentZoomFactor===parseFloat(zoomSlider.max)&&nZF>=currentZoomFactor)))return;currentZoomFactor=nZF;zoomSlider.value=currentZoomFactor.toFixed(2);renderedCellSize=(currentGridSize>0&&minFitCellSize>0?minFitCellSize:pixelCanvas.width>0?pixelCanvas.width:1)*currentZoomFactor;renderedCellSize=Math.max(.001,renderedCellSize);currentViewOffsetX=lGXUM-mXOC/renderedCellSize;currentViewOffsetY=lGYUM-mYOC/renderedCellSize;if(Math.abs(currentZoomFactor-1)<.01){currentViewOffsetX=0;currentViewOffsetY=0;if(currentGridSize>0&&minFitCellSize>0)renderedCellSize=minFitCellSize}clampViewOffset();updateCursor();requestFullRedraw()},{passive:!1});
        pixelCanvas.addEventListener('mousedown', e => {
            const cT = toolSelector.value;
            const mDC = getCell(e); // Mouse Down Cell

            // Check if this mouse button matches the Auto Pan keybind
            const autoPanKeybind = currentKeybinds['Auto Pan'] || 'rightclick';
            const isAutoPanTrigger = (autoPanKeybind === 'rightclick' && e.button === 2) ||
                                   (autoPanKeybind === 'middleclick' && e.button === 1);

            if (isAutoPanTrigger) {
                isRightButtonPanning = true;
                startPanAndShowMinimap(e);
                e.preventDefault();
                return;
            }
            if ((e.button === 1 && !drawing && !isMovingSelection && currentZoomFactor > 1.01) || (e.button === 0 && cT === 'pan')) {
                startPanAndShowMinimap(e);
                e.preventDefault();
                return;
            }
            if (e.button !== 0) return;

            const isOffCanvasForTool = (currentGridSize <= 0 || mDC.x < 0 || mDC.x >= currentGridSize || mDC.y < 0 || mDC.y >= currentGridSize);

            // --- SELECT TOOL LOGIC ---
            if (cT === 'select') {
                // Check for handle clicks first (highest priority)
                const rect = pixelCanvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                const handleHit = getSelectionHandleAtPoint(mouseX, mouseY);

                if (handleHit) {
                    // Start resize operation
                    isResizingSelection = true;
                    resizeHandleType = handleHit.type;
                    drawing = false; // Not drawing a new selection

                    saveState(); // Save state before resize operation

                    // Store original selection for resize calculations
                    originalSelectionForResize = {
                        start: { ...currentSelectionStart },
                        end: { ...currentSelectionEnd }
                    };

                    // Extract the current selection content into a buffer
                    const selLogicX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
                    const selLogicY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
                    const selLogicX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
                    const selLogicY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);

                    const originalWidth = selLogicX1 - selLogicX0 + 1;
                    const originalHeight = selLogicY1 - selLogicY0 + 1;

                    // Store the original selection content
                    resizeSelectionBuffer = [];
                    if (activeLayer && activeLayer.pixels) {
                        for (let r = 0; r < originalHeight; r++) {
                            const layerY = selLogicY0 + r;
                            const bufferRow = Array(originalWidth).fill(EMPTY_COLOR);
                            if (layerY >= 0 && layerY < currentGridSize && activeLayer.pixels[layerY]) {
                                for (let c = 0; c < originalWidth; c++) {
                                    const layerX = selLogicX0 + c;
                                    if (layerX >= 0 && layerX < currentGridSize) {
                                        bufferRow[c] = activeLayer.pixels[layerY][layerX] || EMPTY_COLOR;
                                    }
                                }
                            }
                            resizeSelectionBuffer.push(bufferRow);
                        }
                    }

                    // Clear the original selection area
                    if (activeLayer && activeLayer.pixels) {
                        for (let y = selLogicY0; y <= selLogicY1; y++) {
                            if (y >= 0 && y < currentGridSize && activeLayer.pixels[y]) {
                                for (let x = selLogicX0; x <= selLogicX1; x++) {
                                    if (x >= 0 && x < currentGridSize) {
                                        if (activeLayer.pixels[y][x] !== EMPTY_COLOR && activeLayer.pixels[y][x] !== undefined) {
                                            activeLayer.pixels[y][x] = EMPTY_COLOR;
                                            expandDirtyRect(x, y);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Set anchor point based on handle type
                    if (handleHit.type === 'nw') resizeAnchorPoint = { x: selLogicX1, y: selLogicY1 };
                    else if (handleHit.type === 'ne') resizeAnchorPoint = { x: selLogicX0, y: selLogicY1 };
                    else if (handleHit.type === 'sw') resizeAnchorPoint = { x: selLogicX1, y: selLogicY0 };
                    else if (handleHit.type === 'se') resizeAnchorPoint = { x: selLogicX0, y: selLogicY0 };
                    else if (handleHit.type === 'n') resizeAnchorPoint = { x: selLogicX0 + (selLogicX1 - selLogicX0) / 2, y: selLogicY1 };
                    else if (handleHit.type === 's') resizeAnchorPoint = { x: selLogicX0 + (selLogicX1 - selLogicX0) / 2, y: selLogicY0 };
                    else if (handleHit.type === 'w') resizeAnchorPoint = { x: selLogicX1, y: selLogicY0 + (selLogicY1 - selLogicY0) / 2 };
                    else if (handleHit.type === 'e') resizeAnchorPoint = { x: selLogicX0, y: selLogicY0 + (selLogicY1 - selLogicY0) / 2 };

                    requestFullRedraw();
                    return; // Handle click processed
                }

                let clickedInsideStaticSelection = false;
                if (isSelecting && currentSelectionStart && currentSelectionEnd && !isMovingSelection && !isOffCanvasForTool &&
                    typeof currentSelectionStart.x === 'number' && typeof currentSelectionStart.y === 'number' &&
                    typeof currentSelectionEnd.x === 'number' && typeof currentSelectionEnd.y === 'number') {
                    const minSelX = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
                    const maxSelX = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
                    const minSelY = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
                    const maxSelY = Math.max(currentSelectionStart.y, currentSelectionEnd.y);
                    if (mDC.x >= minSelX && mDC.x <= maxSelX && mDC.y >= minSelY && mDC.y <= maxSelY) {
                        clickedInsideStaticSelection = true;
                    }
                }

                if (isMovingSelection) { // If a move was in progress (e.g. from paste, should be placed on click)
                    // This click should place the selection IF it's outside the current moving bounds
                    // If inside, it effectively does nothing on mousedown, move continues until mouseup or another click.
                    // The main finalization of a move happens on mouseup or tool change.
                    // For now, we assume that if a move is active, any click outside would finalize it
                    // by the mouseup handler. If clicked inside, nothing on mousedown, mousemove handles.
                    // This case can be complex if trying to re-initiate a move from a moving selection.
                    // The provided mouseup logic is more robust for placement.
                    // To prevent unexpected behavior, let's ensure only one action (move or new draw) starts here.
                } else if (clickedInsideStaticSelection && activeLayer && activeLayer.isVisible) {
                    // Start MOVING an existing static selection
                    isInitialPasteFloat = false;
                    isMovingSelection = true;
                    drawing = false; // Not drawing a new selection box marquee
                    // isSelecting remains true

                    saveState();

                    const selX0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
                    const selY0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
                    const selX1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
                    const selY1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);
                    movingSelectionCurrentTopLeft = { x: selX0, y: selY0 };
                    movingSelectionDelta = { dx: mDC.x - selX0, dy: mDC.y - selY0 };
                    originalSelectionBeforeMove = { start: { ...currentSelectionStart }, end: { ...currentSelectionEnd } };

                    movingSelectionBuffer = [];
                    if (activeLayer.pixels) {
                        for (let r_idx = selY0; r_idx <= selY1; r_idx++) {
                            let bufferRowLocal = Array((selX1 - selX0) + 1).fill(EMPTY_COLOR);
                            if (r_idx >= 0 && r_idx < currentGridSize && activeLayer.pixels[r_idx]) {
                                for (let c_idx = selX0; c_idx <= selX1; c_idx++) {
                                    if (c_idx >= 0 && c_idx < currentGridSize) {
                                        bufferRowLocal[c_idx - selX0] = activeLayer.pixels[r_idx][c_idx] || EMPTY_COLOR;
                                        if (activeLayer.pixels[r_idx][c_idx] !== EMPTY_COLOR && activeLayer.pixels[r_idx][c_idx] !== undefined) {
                                            activeLayer.pixels[r_idx][c_idx] = EMPTY_COLOR;
                                            expandDirtyRect(c_idx, r_idx);
                                        }
                                    }
                                }
                            }
                            movingSelectionBuffer.push(bufferRowLocal);
                        }
                    }
                } else if (!isOffCanvasForTool) {
                    // Start drawing a NEW selection box (deselects any previous static selection)
                    if (isSelecting && !isMovingSelection) { // Deselect previous static
                        currentSelectionStart = null; currentSelectionEnd = null; isSelecting = false;
                        if (canvases[activeCanvasIndex]) {
                            canvases[activeCanvasIndex].selectionStart = null; canvases[activeCanvasIndex].selectionEnd = null; canvases[activeCanvasIndex].isSelecting = false;
                        }
                    }
                    isInitialPasteFloat = false;
                    isSelecting = true;      // A selection process is starting
                    drawing = true;          // We are now "drawing" the selection marquee
                    currentSelectionStart = { x: mDC.x, y: mDC.y }; // Use copies
                    currentSelectionEnd = { x: mDC.x, y: mDC.y };   // Use copies
                    if (canvases[activeCanvasIndex]) {
                        canvases[activeCanvasIndex].selectionStart = { ...currentSelectionStart };
                        canvases[activeCanvasIndex].selectionEnd = { ...currentSelectionEnd };
                        canvases[activeCanvasIndex].isSelecting = true;
                    }
                    isMovingSelection = false; // Reset this, as we are drawing a new one
                    movingSelectionBuffer = null;
                } else {
                    // Clicked off-canvas when not interacting with an existing selection: Deselect
                    currentSelectionStart = null; currentSelectionEnd = null; isSelecting = false;
                    if (canvases[activeCanvasIndex]) {
                        canvases[activeCanvasIndex].selectionStart = null; canvases[activeCanvasIndex].selectionEnd = null; canvases[activeCanvasIndex].isSelecting = false;
                    }
                }
                updateBrushOptionsBarContent();
                requestFullRedraw();
                return; // Select tool mousedown action handled
            } // End of cT === 'select'

            // --- Fallthrough for other tools ---
            if (!activeLayer || !activeLayer.isVisible) {
                if (!['pan', 'eyedropper'].includes(cT)) return;
            }
            if (isOffCanvasForTool && cT !== 'pan') return;

            if (cT !== 'pan' && cT !== 'eyedropper') { saveState(); }

            startCell = mDC;
            if (cT === 'fill') { drawing = false; } // Fill is instant
            else { drawing = true; } // For draw, erase, line, rect, circle

            if (drawing && (isVerticalMirrorActive || isHorizontalMirrorActive || isRadialMirrorActive)) {
                mirrorCenterX = startCell.x; mirrorCenterY = startCell.y;
            } else {
                mirrorCenterX = null; mirrorCenterY = null;
            }

            if (drawing && (cT === 'draw' || cT === 'erase' || ['line', 'rectangle', 'circle'].includes(cT)) ) {
                if (!activeLayer) { drawing = false; return; }
                layerDataBeforeStroke = activeLayer.pixels ? JSON.parse(JSON.stringify(activeLayer.pixels)) : createNewPixelData(currentGridSize);
                currentStrokeBuffer = Array(currentGridSize).fill(null).map(() => Array(currentGridSize).fill(undefined));
                // Clear dirty regions for the new stroke to optimize performance
                clearStrokeBufferDirtyRegions();

                // Initialize performance preview mode if enabled
                if (brushPerformanceMode && (cT === 'draw' || cT === 'erase')) {
                    isPerformancePreviewActive = true;
                    performancePreviewPath = [{ x: mDC.x, y: mDC.y }];
                }
            }

            if (drawing && cT === 'draw') {
                if (currentBrushType === 'taper') {
                    currentPathPoints = [{ x: startCell.x, y: startCell.y }];
                }
                applyBrush(startCell.x, startCell.y, currentSelectedColor);
                _applyStrokeBufferToActiveLayerForPreview();
            } else if (drawing && cT === 'erase') {
                applyBrush(startCell.x, startCell.y, EMPTY_COLOR);
                _applyStrokeBufferToActiveLayerForPreview();
            } else if (cT === 'eyedropper') { // Eyedropper logic (moved here from the top for clarity, happens on mousedown)
                if (isOffCanvasForTool) return;
                let accumulatedColor = { r:0,g:0,b:0,a:0 };
                for (let i=0; i<currentLayers.length; i++) {
                    const layer = currentLayers[i];
                    if (layer.isVisible && layer.opacity > 0 && layer.pixels && layer.pixels[mDC.y]) {
                        const cellData = layer.pixels[mDC.y][mDC.x];
                        if (cellData !== undefined && cellData !== EMPTY_COLOR) {
                            const sourcePixelColor = parseRgba(cellData);
                            const layerOpacityFactor = layer.opacity / 100.0;
                            const As_effective = sourcePixelColor.a * layerOpacityFactor;
                            if (As_effective > 0.001) {
                                const Rs=sourcePixelColor.r, Gs=sourcePixelColor.g, Bs=sourcePixelColor.b;
                                const Rb=accumulatedColor.r, Gb=accumulatedColor.g, Bb=accumulatedColor.b, Ab=accumulatedColor.a;
                                const A_out = As_effective + Ab * (1 - As_effective);
                                if (A_out > 0.001) {
                                    accumulatedColor = {r:(Rs*As_effective + Rb*Ab*(1-As_effective))/A_out, g:(Gs*As_effective + Gb*Ab*(1-As_effective))/A_out, b:(Bs*As_effective + Bb*Ab*(1-As_effective))/A_out, a:A_out };
                                } else { accumulatedColor = { r:0,g:0,b:0,a:0 }; }
                            }
                        }
                    }
                }
                if (accumulatedColor.a > 0.01) {
                    currentSelectedColor = rgbToHex(accumulatedColor.r, accumulatedColor.g, accumulatedColor.b);
                    colorPickerInput.value = currentSelectedColor; hexInput.value = currentSelectedColor;
                    let switchToTool = previousDrawingTool;
                    if (!switchToTool || ['eyedropper','pan','select','clear'].includes(switchToTool)) { switchToTool = 'draw'; }
                    toolSelector.value = switchToTool; toolSelector.dispatchEvent(new Event('change'));
                }
                drawing = false; // Eyedropper is instant
            } else if (cT === 'fill') {
                if (activeLayer && activeLayer.isVisible && !isOffCanvasForTool) {
                    // Save the current state before filling
                    saveState();
                    
                    const targetColorRgba = (activeLayer.pixels && activeLayer.pixels[startCell.y] && typeof activeLayer.pixels[startCell.y][startCell.x] !== 'undefined') ? 
                        activeLayer.pixels[startCell.y][startCell.x] : EMPTY_COLOR;
                    
                    // Show a loading cursor for large canvases
                    if (currentGridSize >= 256) {
                        pixelCanvas.style.cursor = 'progress';
                    }
                    
                    // Execute the flood fill operation
                    floodFill(startCell.x, startCell.y, targetColorRgba, currentSelectedColor);
                    
                    // Schedule a state save after the fill completes (for large canvases)
                    if (currentGridSize >= 256) {
                        setTimeout(() => {
                            pixelCanvas.style.cursor = 'crosshair';
                            debouncedSaveToLocalStorage();
                        }, 100);
                    } else {
                        debouncedSaveToLocalStorage();
                    }
                }
                startCell = null; // Fill is instant
                drawing = false;
            }
            requestFullRedraw();
        });
        document.addEventListener('mousemove', e => {
            // Check if auto pan key is held and we should start panning
            if (isAutoPanKeyPressed && !isPanning) {
                // Start panning like right-click would
                startPanAndShowMinimap(e);
            }

            if (isPanning) {
                const dMX = e.clientX - panStartMouse.x; const dMY = e.clientY - panStartMouse.y;
                if (renderedCellSize <= 0.001) return;
                const dCX = dMX / renderedCellSize; const dCY = dMY / renderedCellSize;
                currentViewOffsetX = panStartViewOffset.x - dCX; currentViewOffsetY = panStartViewOffset.y - dCY;
                clampViewOffset(); requestFullRedraw();
                if (minimapCanvasElement && minimapCanvasElement.style.display === 'block') { renderMinimap(); }
                return;
            }

            const currentTool = toolSelector.value;
            const clc = getCell(e);
            let newCursorStyle = pixelCanvas.style.cursor;

            if (currentTool === 'select') {
                if (isResizingSelection && resizeHandleType && resizeAnchorPoint && originalSelectionForResize && resizeSelectionBuffer) {
                    // Handle resize operation
                    let newX = Math.max(0, Math.min(clc.x, currentGridSize - 1));
                    let newY = Math.max(0, Math.min(clc.y, currentGridSize - 1));

                    let newStart = { ...currentSelectionStart };
                    let newEnd = { ...currentSelectionEnd };

                    // For corner handles with Shift key, maintain 1:1 aspect ratio
                    const isCornerHandle = ['nw', 'ne', 'sw', 'se'].includes(resizeHandleType);
                    if (isCornerHandle && e.shiftKey) {
                        const originalWidth = originalSelectionForResize.end.x - originalSelectionForResize.start.x + 1;
                        const originalHeight = originalSelectionForResize.end.y - originalSelectionForResize.start.y + 1;
                        const aspectRatio = originalWidth / originalHeight;

                        // Calculate the distance from anchor point
                        const deltaX = Math.abs(newX - resizeAnchorPoint.x);
                        const deltaY = Math.abs(newY - resizeAnchorPoint.y);

                        // Use the larger dimension to maintain aspect ratio, but ensure minimum size
                        let constrainedDeltaX, constrainedDeltaY;
                        if (deltaX / aspectRatio > deltaY) {
                            constrainedDeltaY = Math.max(1, deltaX / aspectRatio);
                            constrainedDeltaX = Math.max(1, constrainedDeltaY * aspectRatio);
                        } else {
                            constrainedDeltaX = Math.max(1, deltaY * aspectRatio);
                            constrainedDeltaY = Math.max(1, constrainedDeltaX / aspectRatio);
                        }

                        // Apply the constrained deltas with proper direction
                        newX = resizeAnchorPoint.x + (newX > resizeAnchorPoint.x ? constrainedDeltaX : -constrainedDeltaX);
                        newY = resizeAnchorPoint.y + (newY > resizeAnchorPoint.y ? constrainedDeltaY : -constrainedDeltaY);

                        // Clamp to grid bounds
                        newX = Math.max(0, Math.min(Math.round(newX), currentGridSize - 1));
                        newY = Math.max(0, Math.min(Math.round(newY), currentGridSize - 1));
                    }

                    // Update selection bounds based on handle type
                    if (resizeHandleType === 'nw') {
                        newStart.x = newX; newStart.y = newY;
                        newEnd.x = resizeAnchorPoint.x; newEnd.y = resizeAnchorPoint.y;
                    } else if (resizeHandleType === 'ne') {
                        newStart.x = resizeAnchorPoint.x; newStart.y = newY;
                        newEnd.x = newX; newEnd.y = resizeAnchorPoint.y;
                    } else if (resizeHandleType === 'sw') {
                        newStart.x = newX; newStart.y = resizeAnchorPoint.y;
                        newEnd.x = resizeAnchorPoint.x; newEnd.y = newY;
                    } else if (resizeHandleType === 'se') {
                        newStart.x = resizeAnchorPoint.x; newStart.y = resizeAnchorPoint.y;
                        newEnd.x = newX; newEnd.y = newY;
                    } else if (resizeHandleType === 'n') {
                        newStart.y = newY; newEnd.y = resizeAnchorPoint.y;
                    } else if (resizeHandleType === 's') {
                        newStart.y = resizeAnchorPoint.y; newEnd.y = newY;
                    } else if (resizeHandleType === 'w') {
                        newStart.x = newX; newEnd.x = resizeAnchorPoint.x;
                    } else if (resizeHandleType === 'e') {
                        newStart.x = resizeAnchorPoint.x; newEnd.x = newX;
                    }

                    // Ensure start is always top-left and end is bottom-right
                    currentSelectionStart = {
                        x: Math.min(newStart.x, newEnd.x),
                        y: Math.min(newStart.y, newEnd.y)
                    };
                    currentSelectionEnd = {
                        x: Math.max(newStart.x, newEnd.x),
                        y: Math.max(newStart.y, newEnd.y)
                    };

                    // Safety check: ensure selection has valid dimensions (at least 1x1)
                    if (currentSelectionEnd.x <= currentSelectionStart.x) {
                        currentSelectionEnd.x = currentSelectionStart.x;
                    }
                    if (currentSelectionEnd.y <= currentSelectionStart.y) {
                        currentSelectionEnd.y = currentSelectionStart.y;
                    }

                    // Calculate new dimensions
                    const newWidth = currentSelectionEnd.x - currentSelectionStart.x + 1;
                    const newHeight = currentSelectionEnd.y - currentSelectionStart.y + 1;
                    const originalWidth = resizeSelectionBuffer[0] ? resizeSelectionBuffer[0].length : 1;
                    const originalHeight = resizeSelectionBuffer.length;

                    // Only update preview if dimensions are valid
                    if (newWidth > 0 && newHeight > 0 && originalWidth > 0 && originalHeight > 0) {
                        // Clear a larger area to prevent artifacts from fast movements
                        // Calculate the bounds that could have been affected by previous previews
                        const originalSelStart = originalSelectionForResize.start;
                        const originalSelEnd = originalSelectionForResize.end;
                        const clearMinX = Math.min(originalSelStart.x, currentSelectionStart.x) - 1;
                        const clearMaxX = Math.max(originalSelEnd.x, currentSelectionEnd.x) + 1;
                        const clearMinY = Math.min(originalSelStart.y, currentSelectionStart.y) - 1;
                        const clearMaxY = Math.max(originalSelEnd.y, currentSelectionEnd.y) + 1;

                        // Clear the expanded area to remove any artifacts
                        if (activeLayer && activeLayer.pixels) {
                            for (let y = Math.max(0, clearMinY); y <= Math.min(currentGridSize - 1, clearMaxY); y++) {
                                if (activeLayer.pixels[y]) {
                                    for (let x = Math.max(0, clearMinX); x <= Math.min(currentGridSize - 1, clearMaxX); x++) {
                                        // Only clear if this pixel was part of the original selection or could be affected
                                        const wasInOriginal = (x >= originalSelStart.x && x <= originalSelEnd.x &&
                                                             y >= originalSelStart.y && y <= originalSelEnd.y);
                                        const isInCurrent = (x >= currentSelectionStart.x && x <= currentSelectionEnd.x &&
                                                           y >= currentSelectionStart.y && y <= currentSelectionEnd.y);

                                        if (wasInOriginal || isInCurrent) {
                                            activeLayer.pixels[y][x] = EMPTY_COLOR;
                                            expandDirtyRect(x, y);
                                        }
                                    }
                                }
                            }
                        }

                        // Resample the content to new dimensions
                        const resampledBuffer = resampleSelectionBuffer(
                            resizeSelectionBuffer, originalWidth, originalHeight, newWidth, newHeight
                        );

                        // Apply the resampled content as preview
                        if (activeLayer && activeLayer.pixels && resampledBuffer) {
                            for (let r = 0; r < resampledBuffer.length; r++) {
                                const targetY = currentSelectionStart.y + r;
                                if (targetY >= 0 && targetY < currentGridSize) {
                                    if (!activeLayer.pixels[targetY]) {
                                        activeLayer.pixels[targetY] = Array(currentGridSize).fill(EMPTY_COLOR);
                                    }
                                    for (let c = 0; c < resampledBuffer[r].length; c++) {
                                        const targetX = currentSelectionStart.x + c;
                                        if (targetX >= 0 && targetX < currentGridSize) {
                                            const pixelData = resampledBuffer[r][c];
                                            if (pixelData !== EMPTY_COLOR && pixelData !== undefined) {
                                                activeLayer.pixels[targetY][targetX] = pixelData;
                                                expandDirtyRect(targetX, targetY);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Update canvas state
                    if (canvases[activeCanvasIndex]) {
                        canvases[activeCanvasIndex].selectionStart = { ...currentSelectionStart };
                        canvases[activeCanvasIndex].selectionEnd = { ...currentSelectionEnd };
                        canvases[activeCanvasIndex].isSelecting = true;
                    }

                    // Set appropriate cursor
                    if (['nw', 'se'].includes(resizeHandleType)) newCursorStyle = 'nw-resize';
                    else if (['ne', 'sw'].includes(resizeHandleType)) newCursorStyle = 'ne-resize';
                    else if (['n', 's'].includes(resizeHandleType)) newCursorStyle = 'ns-resize';
                    else if (['w', 'e'].includes(resizeHandleType)) newCursorStyle = 'ew-resize';
                } else if (isMovingSelection && movingSelectionCurrentTopLeft && movingSelectionBuffer) {
                    let newTopLeftX, newTopLeftY;
                    const selWidth = movingSelectionBuffer[0] ? movingSelectionBuffer[0].length : 0;
                    const selHeight = movingSelectionBuffer.length;
                    if (isInitialPasteFloat) {
                        if (selWidth > 0 && selHeight > 0) {
                            newTopLeftX = clc.x - Math.floor(selWidth / 2); newTopLeftY = clc.y - Math.floor(selHeight / 2);
                        } else { newTopLeftX = clc.x; newTopLeftY = clc.y; }
                    } else if (movingSelectionDelta) {
                        newTopLeftX = clc.x - movingSelectionDelta.dx; newTopLeftY = clc.y - movingSelectionDelta.dy;
                    } else { newTopLeftX = clc.x; newTopLeftY = clc.y; }

                    movingSelectionCurrentTopLeft.x = newTopLeftX;
                    movingSelectionCurrentTopLeft.y = newTopLeftY;
                    currentSelectionStart = { ...movingSelectionCurrentTopLeft };
                    currentSelectionEnd = { x: newTopLeftX + (selWidth > 0 ? selWidth - 1 : 0), y: newTopLeftY + (selHeight > 0 ? selHeight - 1 : 0) };

                    if (canvases[activeCanvasIndex]) {
                        canvases[activeCanvasIndex].selectionStart = {...currentSelectionStart};
                        canvases[activeCanvasIndex].selectionEnd = {...currentSelectionEnd};
                        canvases[activeCanvasIndex].isSelecting = true;
                    }
                    // Use dirty rectangle for moving selection instead of full redraw
                    expandDirtyRect(Math.min(currentSelectionStart.x, currentSelectionEnd.x), Math.min(currentSelectionStart.y, currentSelectionEnd.y));
                    expandDirtyRect(Math.max(currentSelectionStart.x, currentSelectionEnd.x), Math.max(currentSelectionStart.y, currentSelectionEnd.y));
                    newCursorStyle = 'grabbing';
                } else if (drawing && isSelecting && currentSelectionStart) { // Drawing a new selection box (drawing is true from mousedown)
                    const cEX = Math.max(0, Math.min(clc.x, currentGridSize - 1));
                    const cEY = Math.max(0, Math.min(clc.y, currentGridSize - 1));
                    currentSelectionEnd = { x: cEX, y: cEY }; // Update the end corner
                    if (canvases[activeCanvasIndex]) { canvases[activeCanvasIndex].selectionEnd = {...currentSelectionEnd}; }
                    // Use dirty rectangle for selection box instead of full redraw
                    expandDirtyRect(Math.min(currentSelectionStart.x, cEX), Math.min(currentSelectionStart.y, cEY));
                    expandDirtyRect(Math.max(currentSelectionStart.x, cEX), Math.max(currentSelectionStart.y, cEY));
                    newCursorStyle = 'crosshair';
                } else if (isSelecting && currentSelectionStart && currentSelectionEnd && !isMovingSelection) { // Hovering over a static selection
                    const minSelX = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
                    const maxSelX = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
                    const minSelY = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
                    const maxSelY = Math.max(currentSelectionStart.y, currentSelectionEnd.y);
                    const isOffCanvas = (clc.x < 0 || clc.x >= currentGridSize || clc.y < 0 || clc.y >= currentGridSize);

                    // Check for handle hover first
                    const rect = pixelCanvas.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;
                    const handleHit = getSelectionHandleAtPoint(mouseX, mouseY);

                    if (handleHit) {
                        // Set cursor based on handle type
                        if (['nw', 'se'].includes(handleHit.type)) newCursorStyle = 'nw-resize';
                        else if (['ne', 'sw'].includes(handleHit.type)) newCursorStyle = 'ne-resize';
                        else if (['n', 's'].includes(handleHit.type)) newCursorStyle = 'ns-resize';
                        else if (['w', 'e'].includes(handleHit.type)) newCursorStyle = 'ew-resize';
                    } else if (!isOffCanvas && clc.x >= minSelX && clc.x <= maxSelX && clc.y >= minSelY && clc.y <= maxSelY) {
                        newCursorStyle = 'move';
                    } else {
                        newCursorStyle = 'crosshair';
                    }
            } else { // Not selecting, or selection is invalid (e.g. if currentSelectionStart is null)
                    newCursorStyle = 'crosshair';
            }

            if (pixelCanvas.style.cursor !== newCursorStyle) {
                pixelCanvas.style.cursor = newCursorStyle;
            }
            return; // Select tool mousemove handled
            }

            // Mousemove for other tools
            if (!drawing || !startCell) { return; }
            if (!activeLayer || !activeLayer.isVisible) { if (currentTool !== 'pan') return; }

            const currentThicknessValue = parseInt(thicknessInput.value) || 1;
            if (currentTool === 'draw') {
                if (isPerformancePreviewActive && brushPerformanceMode) {
                    // Performance mode: just track the path with single pixels
                    if (performancePreviewPath.length > 0) {
                        const lastPoint = performancePreviewPath[performancePreviewPath.length - 1];
                        if (lastPoint.x !== clc.x || lastPoint.y !== clc.y) {
                            performancePreviewPath.push({ x: clc.x, y: clc.y });
                            // Draw simple single-pixel line preview
                            drawSimpleLinePreview(lastPoint.x, lastPoint.y, clc.x, clc.y, currentSelectedColor);
                        }
                    }
                } else {
                    // Normal mode: full brush rendering
                    if (currentBrushType === 'taper') {
                        if (currentPathPoints.length > 0) {
                            const lastPoint = currentPathPoints[currentPathPoints.length - 1];
                            if (lastPoint.x !== clc.x || lastPoint.y !== clc.y) {
                                currentPathPoints.push({ x: clc.x, y: clc.y });
                                drawLine(lastPoint.x, lastPoint.y, clc.x, clc.y, currentSelectedColor);
                                _applyStrokeBufferToActiveLayerForPreview();
                            }
                        }
                    } else {
                        const colorForBrush = currentSelectedColor;
                        if (currentBrushType === 'spray_paint') { applyBrush(clc.x, clc.y, colorForBrush); }
                        else if (startCell.x !== clc.x || startCell.y !== clc.y) {
                            drawLine(startCell.x, startCell.y, clc.x, clc.y, colorForBrush);
                            startCell = {x: clc.x, y: clc.y};
                        } else if (['calligraphy', 'glow', 'anti_alias'].includes(currentBrushType) || currentBrushType.includes('hatch')) {
                            applyBrush(clc.x, clc.y, colorForBrush);
                        }
                        _applyStrokeBufferToActiveLayerForPreview();
                    }
                }
                // For draw tool, rely on dirtyRect updates via _applyStrokeBufferToActiveLayerForPreview
                // and subsequent mainRenderLoop iterations. Requesting full redraw here can be too much.
            } else if (currentTool === 'erase') {
                if (isPerformancePreviewActive && brushPerformanceMode) {
                    // Performance mode: just track the path with single pixels
                    if (performancePreviewPath.length > 0) {
                        const lastPoint = performancePreviewPath[performancePreviewPath.length - 1];
                        if (lastPoint.x !== clc.x || lastPoint.y !== clc.y) {
                            performancePreviewPath.push({ x: clc.x, y: clc.y });
                            // Draw simple single-pixel line preview for eraser
                            drawSimpleLinePreview(lastPoint.x, lastPoint.y, clc.x, clc.y, EMPTY_COLOR);
                        }
                    }
                } else {
                    // Normal mode: full eraser rendering
                    const colorForBrush = EMPTY_COLOR;
                    if (startCell.x !== clc.x || startCell.y !== clc.y) {
                        drawLine(startCell.x, startCell.y, clc.x, clc.y, colorForBrush);
                        startCell = {x: clc.x, y: clc.y};
                    } else { applyBrush(clc.x, clc.y, colorForBrush); }
                    _applyStrokeBufferToActiveLayerForPreview();
                }
            } else if (['line', 'rectangle', 'circle'].includes(currentTool)) {
                gridCtx.clearRect(0, 0, gridCanvas.width, gridCanvas.height);
                drawGrid();
                // Re-draw static selection box if it exists and select tool is not active (or this is the shape tool)
                if (isSelecting && currentSelectionStart && currentSelectionEnd && !isMovingSelection) {
                    drawSelectionBox();
                }

                const previewColor = currentSelectedColor;
                const previewThickness = currentThicknessValue;
                if (currentTool === 'line') { previewLineOnGrid(startCell.x, startCell.y, clc.x, clc.y, previewColor, previewThickness); }
                else if (currentTool === 'rectangle') { previewRectangleOnGrid(startCell.x, startCell.y, clc.x, clc.y, previewColor, previewThickness); }
                else if (currentTool === 'circle') { previewCircleOnGrid(startCell.x, startCell.y, clc.x, clc.y, previewColor, previewThickness, e.shiftKey); }
            }
        });

        document.addEventListener('mouseup', e => {
            const wasPanningWithButton = isPanning && (e.button === 1 || (e.button === 2 && isRightButtonPanning));
            const wasDrawingSelectionMarquee = (toolSelector.value === 'select' && drawing && !isMovingSelection);
            const currentToolFinalizing = toolSelector.value; // Store before potential change by 'clear'

            // Check if this mouse button matches the Auto Pan keybind
            const autoPanKeybind = currentKeybinds['Auto Pan'] || 'rightclick';
            const isAutoPanRelease = (autoPanKeybind === 'rightclick' && e.button === 2) ||
                                   (autoPanKeybind === 'middleclick' && e.button === 1);

            if (isAutoPanRelease && isRightButtonPanning) {
                isRightButtonPanning = false;
                stopPanAndHideMinimap();
                e.preventDefault(); // Prevent context menu
                // return; // Don't return if other actions might need to be finalized by left button later
            }
            if (isPanning && (toolSelector.value === 'pan' || e.button === 1)) {
                stopPanAndHideMinimap();
                if (e.button === 1) e.preventDefault();
            }

            if (e.button === 0) { // Left mouse button up
                const endCell = getCell(e);
                let canvasAlteredByThisAction = false;

                try {
                    if (currentToolFinalizing === 'select') {
                        if (isResizingSelection) {
                            // Finalize resize operation - the content is already in place from mousemove
                            // Just clean up the resize state
                            isResizingSelection = false;
                            resizeHandleType = null;
                            originalSelectionForResize = null;
                            resizeSelectionBuffer = null;
                            resizeAnchorPoint = null;
                            canvasAlteredByThisAction = true;
                        } else if (isMovingSelection) {
                            finalizeMoveSelection(); // This will set isMovingSelection = false
                            canvasAlteredByThisAction = true;
                        } else if (drawing && isSelecting && currentSelectionStart) { // Finalize drawing a new selection box
                            if (currentSelectionEnd) {
                                let finalEndX = endCell.x; let finalEndY = endCell.y;
                                finalEndX = Math.max(0, Math.min(finalEndX, currentGridSize - 1));
                                finalEndY = Math.max(0, Math.min(finalEndY, currentGridSize - 1));
                                currentSelectionEnd = { x: finalEndX, y: finalEndY };

                                if (currentSelectionStart.x > currentSelectionEnd.x) { [currentSelectionStart.x, currentSelectionEnd.x] = [currentSelectionEnd.x, currentSelectionStart.x]; }
                                if (currentSelectionStart.y > currentSelectionEnd.y) { [currentSelectionStart.y, currentSelectionEnd.y] = [currentSelectionEnd.y, currentSelectionStart.y]; }

                                if (currentSelectionEnd.x < currentSelectionStart.x) currentSelectionEnd.x = currentSelectionStart.x;
                                if (currentSelectionEnd.y < currentSelectionStart.y) currentSelectionEnd.y = currentSelectionStart.y;

                                if (canvases[activeCanvasIndex]) {
                                    canvases[activeCanvasIndex].selectionStart = { ...currentSelectionStart };
                                    canvases[activeCanvasIndex].selectionEnd = { ...currentSelectionEnd };
                                    canvases[activeCanvasIndex].isSelecting = true;
                                }
                                // isSelecting should remain true for the static selection
                                canvasAlteredByThisAction = true;
                            } else { // Mouseup without mousemove (single click)
                                isSelecting = false;
                                currentSelectionStart = null; currentSelectionEnd = null;
                                if (canvases[activeCanvasIndex]) {
                                    canvases[activeCanvasIndex].isSelecting = false;
                                    canvases[activeCanvasIndex].selectionStart = null;
                                    canvases[activeCanvasIndex].selectionEnd = null;
                                }
                            }
                        }
                        // `drawing` flag for select tool (marquee) is reset in finally
                    } else if (drawing && startCell && activeLayer && activeLayer.isVisible) {
                        if (currentToolFinalizing === 'draw' && currentBrushType === 'taper') {
                            if (currentPathPoints.length > 0 && currentStrokeBuffer && layerDataBeforeStroke) {
                                currentStrokeBuffer = Array(currentGridSize).fill(null).map(() => Array(currentGridSize).fill(undefined));
                                // Clear dirty regions for the new stroke to optimize performance
                                clearStrokeBufferDirtyRegions();
                                const maxThickness = parseInt(thicknessInput.value) || 1;
                                const pathSegmentCount = currentPathPoints.length -1;
                                if (pathSegmentCount >= 0) {
                                    if (pathSegmentCount === 0) {
                                        const point = currentPathPoints[0];
                                        const colorToApply = currentSelectedColor === EMPTY_COLOR ? EMPTY_COLOR : hexToRgba(currentSelectedColor, currentBrushOpacity);
                                        const halfSize = Math.floor((maxThickness -1) / 2);
                                        for (let dy = -halfSize; dy <= halfSize; dy++) {
                                            for (let dx = -halfSize; dx <= halfSize; dx++) {
                                                const gX = point.x + dx; const gY = point.y + dy;
                                                if (gX >=0 && gX < currentGridSize && gY >=0 && gY < currentGridSize) {
                                                    if (maxThickness === 1 || (dx*dx + dy*dy <= halfSize*halfSize + 0.5) ) {
                                                        if(!currentStrokeBuffer[gY]) currentStrokeBuffer[gY] = Array(currentGridSize).fill(undefined);
                                                        currentStrokeBuffer[gY][gX] = colorToApply;
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        for (let i = 0; i < pathSegmentCount; i++) {
                                            const p0 = currentPathPoints[i]; const p1 = currentPathPoints[i+1];
                                            const t0_progress = i / pathSegmentCount; const t1_progress = (i + 1) / pathSegmentCount;
                                            const taperFactor0 = 4 * t0_progress * (1 - t0_progress); const taperFactor1 = 4 * t1_progress * (1 - t1_progress);
                                            let thick0 = 1 + (maxThickness - 1) * taperFactor0; let thick1 = 1 + (maxThickness - 1) * taperFactor1;
                                            thick0 = Math.max(1, Math.round(thick0)); thick1 = Math.max(1, Math.round(thick1));
                                            drawTaperedSegmentToBuffer(currentStrokeBuffer, p0.x, p0.y, p1.x, p1.y, currentSelectedColor, currentBrushOpacity, thick0, thick1, 'circular');
                                        }
                                    }
                                    commitStrokeBufferToLayer();
                                    canvasAlteredByThisAction = true;
                                }
                            }
                        } else if (['draw', 'erase'].includes(currentToolFinalizing)) {
                            if (currentStrokeBuffer && layerDataBeforeStroke) {
                                // Handle performance mode finalization
                                if (isPerformancePreviewActive && brushPerformanceMode && performancePreviewPath.length > 0) {
                                    debugLog(`🖱️ Mouse up - creating operation`, {
                                        pathLength: performancePreviewPath.length,
                                        currentTool: currentToolFinalizing,
                                        brushType: currentBrushType,
                                        queueSize: drawingOperationQueue.length,
                                        operationsProcessingCount
                                    });

                                    // Create a new drawing operation
                                    const colorForBrush = currentToolFinalizing === 'draw' ? currentSelectedColor : EMPTY_COLOR;
                                    const operation = createDrawingOperation(
                                        performancePreviewPath,
                                        currentBrushType,
                                        layerDataBeforeStroke,
                                        activeLayer,
                                        currentGridSize,
                                        colorForBrush
                                    );

                                    // Show drawing status indicator for this operation
                                    showDrawingStatusIndicator(operation);

                                    // Use setTimeout to allow UI to update before heavy processing
                                    setTimeout(async () => {
                                        try {
                                            debugLog(`🚀 Starting operation ${operation.id} processing`, {
                                                slotIndex: operation.slotIndex,
                                                pathLength: operation.path.length,
                                                brushType: operation.brushType,
                                                queueSize: drawingOperationQueue.length
                                            });

                                            // Check if this specific operation was cancelled
                                            if (operation.cancelled) {
                                                debugLog(`❌ Operation ${operation.id} was cancelled before processing`);
                                                removeDrawingOperation(operation.id);
                                                return;
                                            }

                                            // Increment counter to prevent preview interference
                                            operationsProcessingCount++;
                                            debugLog(`🔒 Incrementing operations count to ${operationsProcessingCount} for operation ${operation.id}`);

                                            // Switch to this operation's global variable set
                                            switchToOperationGlobals(operation.slotIndex);

                                            if (operation.brushType === 'spray_paint') {
                                                // For spray paint, apply brush at each recorded point to preserve natural spacing
                                                await processDrawingWithProgress(async (progressCallback) => {
                                                    for (let i = 0; i < operation.path.length; i++) {
                                                        if (operation.cancelled) return;

                                                        const point = operation.path[i];
                                                        applyBrush(point.x, point.y, operation.color);

                                                        // Update progress every 10 operations or on last operation
                                                        if (i % 10 === 0 || i === operation.path.length - 1) {
                                                            const progress = ((i + 1) / operation.path.length) * 100;
                                                            progressCallback(progress);
                                                            await new Promise(resolve => requestAnimationFrame(resolve));
                                                        }
                                                    }
                                                }, operation);
                                            } else {
                                                // For other brushes, draw lines between points
                                                const totalOperations = Math.max(operation.path.length - 1, 1);

                                                await processDrawingWithProgress(async (progressCallback) => {
                                                    for (let i = 0; i < operation.path.length - 1; i++) {
                                                        if (operation.cancelled) return;

                                                        const p0 = operation.path[i];
                                                        const p1 = operation.path[i + 1];
                                                        drawLine(p0.x, p0.y, p1.x, p1.y, operation.color);

                                                        // Update progress every operation for lines (they're typically fewer)
                                                        const progress = ((i + 1) / totalOperations) * 100;
                                                        progressCallback(progress);
                                                        await new Promise(resolve => requestAnimationFrame(resolve));
                                                    }

                                                    // Also apply brush at the final point for single clicks
                                                    if (operation.path.length === 1) {
                                                        const point = operation.path[0];
                                                        applyBrush(point.x, point.y, operation.color);
                                                        progressCallback(100);
                                                    }
                                                }, operation);
                                            }

                                            // Check if cancelled before committing
                                            if (operation.cancelled) {
                                                debugLog(`❌ Operation ${operation.id} cancelled before commit`);
                                                // Decrement counter and clean up
                                                operationsProcessingCount--;
                                                debugLog(`🔓 Decremented operations count to ${operationsProcessingCount} for cancelled operation ${operation.id}`);

                                                // Only restore main globals when ALL operations are complete
                                                if (operationsProcessingCount === 0) {
                                                    debugLog(`🏁 All operations complete, restoring main globals`);
                                                    restoreMainGlobals();
                                                }

                                                removeDrawingOperation(operation.id);
                                                return;
                                            }

                                            debugLog(`💾 Committing operation ${operation.id}`, {
                                                slotIndex: operation.slotIndex,
                                                hasStrokeBuffer: !!currentStrokeBuffer,
                                                hasLayerData: !!layerDataBeforeStroke
                                            });

                                            // Switch back to operation globals for commit
                                            switchToOperationGlobals(operation.slotIndex);

                                            // Commit the stroke buffer to the layer using synchronized commit function
                                            await commitStrokeBufferToLayerSynchronized(operation.id);

                                            debugLog(`✅ Operation ${operation.id} committed successfully`);

                                            // Decrement operations processing counter
                                            operationsProcessingCount--;
                                            debugLog(`🔓 Decremented operations count to ${operationsProcessingCount} for operation ${operation.id}`);

                                            // Only restore main globals when ALL operations are complete
                                            if (operationsProcessingCount === 0) {
                                                debugLog(`🏁 All operations complete, restoring main globals`);
                                                restoreMainGlobals();
                                            }

                                            hideDrawingStatusIndicator(operation);
                                            removeDrawingOperation(operation.id);
                                            requestFullRedraw();

                                            debugLog(`🎯 Operation ${operation.id} completed, queue size: ${drawingOperationQueue.length}`);

                                            // Save to localStorage since canvas was altered
                                            debouncedSaveToLocalStorage();
                                        } catch (error) {
                                            debugLog(`💥 Error in operation ${operation.id}:`, error);
                                            console.error('Error during performance mode brush application:', error);
                                            operationsProcessingCount--;
                                            debugLog(`🔓 Decremented operations count to ${operationsProcessingCount} for error in operation ${operation.id}`);

                                            // Only restore main globals when ALL operations are complete
                                            if (operationsProcessingCount === 0) {
                                                debugLog(`🏁 All operations complete, restoring main globals`);
                                                restoreMainGlobals();
                                            }

                                            hideDrawingStatusIndicator(operation);
                                            removeDrawingOperation(operation.id);
                                        }
                                    }, 10); // Small delay to allow UI update

                                    // Mark that canvas will be altered by the async operation
                                    canvasAlteredByThisAction = true;
                                    // Don't commit here - let the async function handle it
                                    return; // Exit early to prevent double commit
                                } else {
                                    // Normal mode - commit immediately
                                    commitStrokeBufferToLayer();
                                    canvasAlteredByThisAction = true;
                                }
                            }
                        } else if (['line', 'rectangle', 'circle'].includes(currentToolFinalizing)) {
                            if (currentStrokeBuffer && layerDataBeforeStroke && startCell) {
                                currentStrokeBuffer = Array(currentGridSize).fill(null).map(() => Array(currentGridSize).fill(undefined));
                                // Clear dirty regions for the new stroke to optimize performance
                                clearStrokeBufferDirtyRegions();
                                const finalColor = currentSelectedColor;
                                if (currentToolFinalizing === 'line') drawLine(startCell.x, startCell.y, endCell.x, endCell.y, finalColor);
                                else if (currentToolFinalizing === 'rectangle') drawRectangle(startCell.x, startCell.y, endCell.x, endCell.y, finalColor);
                                else if (currentToolFinalizing === 'circle') drawCircle(startCell.x, startCell.y, endCell.x, endCell.y, finalColor, e.shiftKey);
                                commitStrokeBufferToLayer();
                                canvasAlteredByThisAction = true;
                            }
                        }
                    }
                } catch (err) {
                    console.error("Error during mouseup operation:", err);
                } finally {
                    if (wasDrawingSelectionMarquee) {
                        drawing = false; // Reset for select tool marquee drawing
                    } else if (drawing && currentToolFinalizing !== 'pan') { // Reset for other drawing tools, but not if it was only a pan
                        drawing = false;
                    }

                    // isMovingSelection is reset by finalizeMoveSelection
                    // If it wasn't a move, it should already be false.

                    startCell = null;
                    if (!isMovingSelection) { // Only clear these if not in a move (which would have its own buffers)
                        currentStrokeBuffer = null;
                        layerDataBeforeStroke = null;
                    }
                    if (currentToolFinalizing !== 'draw' || currentBrushType !== 'taper') {
                        currentPathPoints = [];
                    }

                    // Reset performance preview state
                    isPerformancePreviewActive = false;
                    performancePreviewPath = [];

                    mirrorCenterX = null; mirrorCenterY = null;

                    updateCursor();
                    updateBrushOptionsBarContent();

                    if (canvasAlteredByThisAction || currentToolFinalizing === 'select' || wasPanningWithButton) {
                        requestFullRedraw();
                    }

                    // Save to localStorage if canvas was altered by drawing
                    if (canvasAlteredByThisAction) {
                        debouncedSaveToLocalStorage();
                    }
                }
            }
        });

        // Touch Event Handlers for Mobile Support - Prevent page scrolling and enable drawing
        pixelCanvas.addEventListener('touchstart', e => {
            e.preventDefault();
            e.stopPropagation();

            const touch = e.touches[0];

            // Create synthetic mouse event
            const mouseEvent = new MouseEvent('mousedown', {
                button: 0,
                clientX: touch.clientX,
                clientY: touch.clientY,
                bubbles: true,
                cancelable: true
            });

            pixelCanvas.dispatchEvent(mouseEvent);
        }, { passive: false });

        pixelCanvas.addEventListener('touchmove', e => {
            e.preventDefault();
            e.stopPropagation();

            if (e.touches.length === 1) {
                const touch = e.touches[0];

                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    bubbles: true,
                    cancelable: true
                });

                document.dispatchEvent(mouseEvent);
            }
        }, { passive: false });

        pixelCanvas.addEventListener('touchend', e => {
            e.preventDefault();
            e.stopPropagation();

            if (e.changedTouches.length > 0) {
                const touch = e.changedTouches[0];

                const mouseEvent = new MouseEvent('mouseup', {
                    button: 0,
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    bubbles: true,
                    cancelable: true
                });

                document.dispatchEvent(mouseEvent);
            }
        }, { passive: false });

        // Prevent context menu and other touch interference
        pixelCanvas.addEventListener('contextmenu', e => {
            e.preventDefault();
        });

        document.addEventListener('keydown', e => {
            const keyLower = e.key.toLowerCase();
            const keyActual = e.key; // For keys like 'Delete', 'Backspace', 'Enter' where case isn't the issue

            if (e.ctrlKey || e.metaKey) { // Handle Ctrl/Meta combinations first
                let sTRD = null;

                if (keyLower === 'z' || keyLower === 'y') { // Undo/Redo
                    e.preventDefault();
                    if (keyLower === 'z') {
                        performUndo();
                    } else if (keyLower === 'y') {
                        performRedo();
                    }
                    return; // Processed Undo/Redo
                } else if (keyLower === 'c') { // Copy
                    if (!isInputActive() && toolSelector.value === 'select' && currentSelectionStart && currentSelectionEnd && activeLayer) {
                        e.preventDefault();
                        handleCopySelection();
                        return; // Processed App Copy
                    }
                    // Allow default browser Ctrl+C if app conditions not met (e.g., in input)
                } else if (keyLower === 'v') { // Paste
                    if (!isInputActive() && toolSelector.value === 'select' && activeLayer && clipboardBuffer) {
                        e.preventDefault();
                        handlePasteSelection();
                        return; // Processed App Paste
                    }
                    // Allow default browser Ctrl+V if app conditions not met
                }

                // If Ctrl/Meta was pressed but not for an app-handled shortcut (Z,Y,C,V that met conditions),
                // return to allow default browser behaviors for other Ctrl combinations (e.g. Ctrl+F)
                // and prevent falling through to single-key app shortcuts.
                return;
            }

            // --- Non-Ctrl key shortcuts ---
            if (isInputActive()) {
                return; // Don't process app shortcuts if an input field is active
            }

            // Handle Delete and Backspace for selected area (Select tool only)
            if ((keyActual === 'Delete' || keyActual === 'Backspace') &&
                toolSelector.value === 'select' &&
                isSelecting && currentSelectionStart && currentSelectionEnd &&
                activeLayer && activeLayer.isVisible) {

                e.preventDefault(); // Prevent browser back navigation for Backspace, or text deletion if in a contenteditable
                handleDeleteSelection();
                return; // Delete action handled
            }

            // Handle Enter for placing a moving selection (Select tool only)
            if (keyActual === 'Enter' && toolSelector.value === 'select' && isMovingSelection) {
                e.preventDefault();
                isInitialPasteFloat = false; // Finalizing, so no longer initial float
                finalizeMoveSelection();
                return;
            }

            // Check for Auto Pan keybind first (if it's a keyboard key)
            const autoPanKeybind = currentKeybinds['Auto Pan'] || 'rightclick';
            if (autoPanKeybind !== 'rightclick' && autoPanKeybind !== 'middleclick' && keyLower === autoPanKeybind.toLowerCase()) {
                e.preventDefault();
                // Start auto pan mode (like holding right-click)
                if (!isAutoPanKeyPressed && !isPanning) {
                    isAutoPanKeyPressed = true;
                    isRightButtonPanning = true; // Use the same flag as right-click panning
                    // We'll start panning on the next mouse move
                }
                return;
            }

            // Tool shortcuts (using custom keybinds)
            let nT = null;

            // Check custom keybinds first
            if (keyLower === (currentKeybinds['Draw Tool'] || 'd').toLowerCase()) {
                nT = 'draw';
            } else if (keyLower === (currentKeybinds['Eyedropper Tool'] || 't').toLowerCase()) {
                nT = 'eyedropper';
            } else if (keyLower === (currentKeybinds['Selection Tool'] || 's').toLowerCase()) {
                nT = 'select';
            } else if (keyLower === (currentKeybinds['Pan Tool'] || 'p').toLowerCase()) {
                nT = 'pan';
            } else {
                // Fallback to hardcoded keys for tools not in keybind settings
                switch (keyLower) {
                    case 'e': nT = 'erase'; break;
                    case 'f': nT = 'fill'; break;
                    case 'l': nT = 'line'; break;
                    case 'r': nT = 'rectangle'; break;
                    case 'c': nT = 'circle'; break;
                }
            }

            if (nT) {
                e.preventDefault();
                if (toolSelector.value !== nT) {
                    toolSelector.value = nT;
                    toolSelector.dispatchEvent(new Event('change'));
                }
            }

            // Brush size shortcuts (using custom keybinds)
            const brushSizeUpKey = (currentKeybinds['Brush Size +'] || '=').toLowerCase();
            const brushSizeDownKey = (currentKeybinds['Brush Size -'] || '-').toLowerCase();

            if (keyLower === brushSizeUpKey) {
                e.preventDefault();
                const currentSize = parseInt(thicknessInput.value);
                const maxSize = parseInt(thicknessInput.max);
                if (currentSize < maxSize) {
                    thicknessInput.value = (currentSize + 1).toString();
                    validateAndClampInput(thicknessInput, parseInt(thicknessInput.min), parseInt(thicknessInput.max));
                }
            } else if (keyLower === brushSizeDownKey) {
                e.preventDefault();
                const currentSize = parseInt(thicknessInput.value);
                const minSize = parseInt(thicknessInput.min);
                if (currentSize > minSize) {
                    thicknessInput.value = (currentSize - 1).toString();
                    validateAndClampInput(thicknessInput, parseInt(thicknessInput.min), parseInt(thicknessInput.max));
                }
            }

            // Thickness shortcuts (single key presses: 0-9)
            let newThickness = -1;
            if (keyLower >= '1' && keyLower <= '9') {
                newThickness = parseInt(keyLower);
            } else if (keyLower === '0') {
                newThickness = 10;
            }

            if (newThickness !== -1) {
                e.preventDefault();
                if (thicknessInput.value !== newThickness.toString()) {
                    thicknessInput.value = newThickness.toString();
                    validateAndClampInput(thicknessInput, parseInt(thicknessInput.min), parseInt(thicknessInput.max));
                }
            }
        });

        // Add keyup handler for auto pan
        document.addEventListener('keyup', e => {
            const keyLower = e.key.toLowerCase();
            const autoPanKeybind = currentKeybinds['Auto Pan'] || 'rightclick';

            // Check if the released key is the auto pan key
            if (autoPanKeybind !== 'rightclick' && autoPanKeybind !== 'middleclick' &&
                keyLower === autoPanKeybind.toLowerCase() && isAutoPanKeyPressed) {

                e.preventDefault();
                isAutoPanKeyPressed = false;

                // Stop panning if it was started by the auto pan key
                if (isRightButtonPanning && isPanning) {
                    isRightButtonPanning = false;
                    stopPanAndHideMinimap();
                }
            }
        });

        toolSelector.addEventListener('change', () => {
            const newTool = toolSelector.value;

            if (newTool !== 'select') {
                if (clipboardBuffer) {
                    clipboardBuffer = null;
                    clipboardBufferDimensions = { width: 0, height: 0 };
                }
                isInitialPasteFloat = false;
                if (isMovingSelection) {
                    finalizeMoveSelection();
                }
                // --- ADDED/MODIFIED LINES START ---
                // Explicitly turn off selection state when not on select tool
                currentSelectionStart = null;
                currentSelectionEnd = null;
                isSelecting = false;
                if (canvases[activeCanvasIndex]) {
                    canvases[activeCanvasIndex].isSelecting = false;
                    canvases[activeCanvasIndex].selectionStart = null;
                    canvases[activeCanvasIndex].selectionEnd = null;
                }
                // --- ADDED/MODIFIED LINES END ---
            } else { // Switching TO select tool
                isMovingSelection = false;
                isInitialPasteFloat = false;
                if (canvases[activeCanvasIndex] && canvases[activeCanvasIndex].selectionStart) {
                    currentSelectionStart = { ...canvases[activeCanvasIndex].selectionStart };
                    currentSelectionEnd = { ...canvases[activeCanvasIndex].selectionEnd };
                    isSelecting = canvases[activeCanvasIndex].isSelecting;
                } else {
                    currentSelectionStart = null;
                    currentSelectionEnd = null;
                    isSelecting = false;
                }
            }

            drawing = false;
            startCell = null;
            currentPathPoints = [];

            if (newTool === 'clear') {
                if (activeLayer && activeLayer.isVisible) {
                    saveState();
                    activeLayer.pixels = createNewPixelData(currentGridSize);
                    debouncedSaveToLocalStorage();
                }
                let toolToRevertTo = previousDrawingTool;
                if (!toolToRevertTo || ['eyedropper', 'pan', 'select', 'clear'].includes(toolToRevertTo)) {
                    toolToRevertTo = 'draw';
                }
                toolSelector.value = toolToRevertTo;
                toolSelector.dispatchEvent(new Event('change'));
                return;
            } else {
                if (newTool !== 'eyedropper' && newTool !== 'pan' && newTool !== 'select') {
                    previousDrawingTool = newTool;
                }
            }

            updateCursor();
            updateBrushOptionsBarContent();
            updateSelectionActionButtons();
            requestFullRedraw();
        });

        // --- Mobile Undo/Redo Functions ---
        function performUndo() {
            if (currentUndoStack.length > 0) {
                const lA = currentUndoStack.pop();
                if (lA && currentLayers[lA.layerIndex]) {
                    const redoState = {
                        layerIndex: lA.layerIndex,
                        pixels: JSON.parse(JSON.stringify(currentLayers[lA.layerIndex].pixels))
                    };
                    
                    // Update memory usage tracking
                    currentUndoMemoryUsage -= calculateUndoStateSize(lA);
                    const redoStateSize = calculateUndoStateSize(redoState);
                    currentUndoMemoryUsage += redoStateSize;
                    
                    currentRedoStack.push(redoState);
                    currentLayers[lA.layerIndex].pixels = lA.pixels;
                    activeLayer = currentLayers[currentActiveLayerIndex];
                    requestFullRedraw();
                    debouncedSaveToLocalStorage();
                    updateMobileUndoRedoButtons();
                }
            }
        }

        function performRedo() {
            if (currentRedoStack.length > 0) {
                const nA = currentRedoStack.pop();
                if (nA && currentLayers[nA.layerIndex]) {
                    const undoState = {
                        layerIndex: nA.layerIndex,
                        pixels: JSON.parse(JSON.stringify(currentLayers[nA.layerIndex].pixels))
                    };
                    
                    // Update memory usage tracking
                    currentUndoMemoryUsage -= calculateUndoStateSize(nA);
                    
                    // Use secure undo stack management
                    addToUndoStack(undoState);
                    
                    currentLayers[nA.layerIndex].pixels = nA.pixels;
                    activeLayer = currentLayers[currentActiveLayerIndex];
                    requestFullRedraw();
                    debouncedSaveToLocalStorage();
                    updateMobileUndoRedoButtons();
                }
            }
        }

        function updateMobileUndoRedoButtons() {
            if (mobileUndoBtn) {
                mobileUndoBtn.disabled = currentUndoStack.length === 0;
            }
            if (mobileRedoBtn) {
                mobileRedoBtn.disabled = currentRedoStack.length === 0;
            }
        }

        function updateSelectionActionButtons() {
            const isSelectTool = toolSelector.value === 'select';
            const hasSelection = currentSelectionStart && currentSelectionEnd && activeLayer;
            const hasClipboard = clipboardBuffer && clipboardBufferDimensions;

            if (mobileCopyBtn) {
                mobileCopyBtn.disabled = !isSelectTool || !hasSelection;
            }
            if (mobilePasteBtn) {
                mobilePasteBtn.disabled = !isSelectTool || !hasClipboard;
            }
            if (mobileDeleteSelectionBtn) {
                mobileDeleteSelectionBtn.disabled = !isSelectTool || !hasSelection;
            }
        }

        // Mobile undo/redo button event listeners
        if (mobileUndoBtn) {
            mobileUndoBtn.addEventListener('click', (e) => {
                e.preventDefault();
                performUndo();
            });
        }

        if (mobileRedoBtn) {
            mobileRedoBtn.addEventListener('click', (e) => {
                e.preventDefault();
                performRedo();
            });
        }

        // Selection action button event listeners
        if (mobileCopyBtn) {
            mobileCopyBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (toolSelector.value === 'select' && currentSelectionStart && currentSelectionEnd && activeLayer) {
                    handleCopySelection();
                    updateSelectionActionButtons();
                }
            });
        }

        if (mobilePasteBtn) {
            mobilePasteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (toolSelector.value === 'select' && clipboardBuffer && clipboardBufferDimensions) {
                    handlePasteSelection();
                    updateSelectionActionButtons();
                }
            });
        }

        if (mobileDeleteSelectionBtn) {
            mobileDeleteSelectionBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (toolSelector.value === 'select' && currentSelectionStart && currentSelectionEnd && activeLayer) {
                    handleDeleteSelection();
                    updateSelectionActionButtons();
                }
            });
        }

        gridStyleSelector.addEventListener('change',()=>{requestFullRedraw()});
        // New function to handle applying the grid size
        function applyNewGridSize(newSizeValueString) {
            const nGSV = parseInt(newSizeValueString);
            // No need to check isNaN or nGSV <= 0 here if values are from our predefined list
            // but good for robustness if source could be different.
            if (isNaN(nGSV) || nGSV <= 0) {
                // console.warn("Invalid size value passed to applyNewGridSize:", newSizeValueString);
                return; // Don't proceed if invalid
            }

            // Show canvas size warning for large canvases (256x256 and above)
            if (nGSV >= 256 && showCanvasSizeWarning) {
                showCanvasSizeWarningTooltip(nGSV);
            }

            const oGSV = currentGridSize;
            currentGridSize = nGSV;
            const aC = canvases[activeCanvasIndex];
            if (!aC) {
                // console.error("No active canvas to apply size change to.");
                return;
            }
            aC.gridSize = currentGridSize;
            aC.layers.forEach(l => {
                const oPD = l.pixels;
                let nPDA = createNewPixelData(currentGridSize);
                const rTCC = Math.min(oGSV, currentGridSize), cTCC = Math.min(oGSV, currentGridSize);
                for (let y = 0; y < rTCC; y++) {
                    if (oPD && oPD[y]) {
                        for (let x = 0; x < cTCC; x++) {
                            if (typeof oPD[y][x] !== 'undefined') nPDA[y][x] = oPD[y][x];
                        }
                    }
                }
                l.pixels = nPDA;
            });

            if (!aC.layers[aC.activeLayerIndex] && aC.layers.length > 0) aC.activeLayerIndex = 0;
            else if (aC.layers.length === 0) initializeLayersForCanvas(aC);

            currentLayers = aC.layers;
            currentActiveLayerIndex = aC.activeLayerIndex;
            activeLayer = currentLayers[currentActiveLayerIndex];

            clearUndoRedoStacks();
            aC.undoStack = currentUndoStack; aC.redoStack = currentRedoStack;

            aC.isSelecting = false; aC.selectionStart = null; aC.selectionEnd = null;
            isSelecting = false; currentSelectionStart = null; currentSelectionEnd = null;

            aC.viewOffsetX = 0; aC.viewOffsetY = 0; aC.zoomFactor = 1;
            currentViewOffsetX = 0; currentViewOffsetY = 0; currentZoomFactor = 1;
            zoomSlider.value = 1;

            drawing = false; startCell = null; isPanning = false; isRightButtonPanning = false;

            renderLayersUI();
            resizeCanvases(); // This will call updateZoom, which calls requestFullRedraw
            debouncedSaveToLocalStorage();
        }

        // REMOVE or COMMENT OUT the old event listener:
        /*
        gridSizeSelector.addEventListener('change',()=>{
            const nGSV=parseInt(gridSizeSelector.value);
            // ... (old logic here) ...
        });
        */
       // --- Modal Event Listeners ---
       if (openSizeModalBtn) {
            openSizeModalBtn.addEventListener('click', openSizeModal);
        }
        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', () => closeSizeModal(false));
        }
        if (cancelSizeChangeBtn) {
            cancelSizeChangeBtn.addEventListener('click', () => closeSizeModal(false));
        }
        if (confirmSizeChangeBtn) {
            confirmSizeChangeBtn.addEventListener('click', () => closeSizeModal(true));
        }

        // Optional: Close modal if clicked outside of .modal-content
        if (sizeModal) {
            sizeModal.addEventListener('click', (event) => {
                if (event.target === sizeModal) { // Clicked on the overlay itself
                    closeSizeModal(false);
                }
            });
        }
        saveBtn.addEventListener('click',()=>{
            // Check if user is logged in and verified
            if (isAuthenticated && user && user.email_verified) {
                // Show cloud save modal for logged-in users
                showCloudSaveModal();
            } else {
                // Original download functionality for non-logged-in users
                downloadCanvasOrSelection();
            }
        });
        zoomSlider.addEventListener('input',()=>{currentZoomFactor=parseFloat(zoomSlider.value);updateZoom(!0)});
        window.addEventListener('resize',resizeCanvases);
        addNewCanvasBtn.addEventListener('click',addNewCanvas);

        function startPanAndShowMinimap(e) {
        // Only start a new pan if not already panning
        // isRightButtonPanning is handled by its own mouseup
        if (!isPanning) {
            isPanning = true;
            panStartMouse = { x: e.clientX, y: e.clientY };
            panStartViewOffset = { x: currentViewOffsetX, y: currentViewOffsetY };
            updateCursor(); // Sets cursor to 'grabbing' or similar
            if (isViewEffectivelyZoomedIn() && minimapCanvasElement) {
                minimapCanvasElement.style.display = 'block';
                renderMinimap(); // Render initial state of minimap
            }
        }
    }

        // Fit-to-view image import (original behavior)
        function renderImageToGridFitToView(img){
            if(!activeLayer||!activeLayer.isVisible){alert("Cannot import image: active layer is not visible or does not exist.");return}
            if(renderedCellSize<=0||currentGridSize<=0){console.warn("Cannot determine viewport or grid for image import.");return}
            const vLW=gridCanvas.width/renderedCellSize,vLH=gridCanvas.height/renderedCellSize;if(vLW<=0||vLH<=0){console.warn("Viewport logical dimensions are zero or negative.");return}
            const iOW=img.width,iOH=img.height,sRW=vLW/iOW,sRH=vLH/iOH,s=Math.min(sRW,sRH);
            let dWILC=Math.max(1,Math.floor(iOW*s)),dHILC=Math.max(1,Math.floor(iOH*s));
            if(dWILC===0||dHILC===0){console.warn("Calculated draw dimensions for image import are zero after fitting to viewport.");return}
            const tC=document.createElement('canvas');tC.width=dWILC;tC.height=dHILC;
            const tCtx=tC.getContext('2d');tCtx.imageSmoothingEnabled=!1;tCtx.drawImage(img,0,0,dWILC,dHILC);
            const iD=tCtx.getImageData(0,0,dWILC,dHILC),data=iD.data;
            const oXIV=Math.floor((vLW-dWILC)/2),oYIV=Math.floor((vLH-dHILC)/2);
            const fLSX=Math.floor(currentViewOffsetX+oXIV),fLSY=Math.floor(currentViewOffsetY+oYIV);

            for(let yT=0;yT<dHILC;yT++){
                for(let xT=0;xT<dWILC;xT++){
                    const idx=(yT*dWILC+xT)*4;
                    const rV=data[idx],gV=data[idx+1],bV=data[idx+2],aV=data[idx+3];
                    const tGX=fLSX+xT,tGY=fLSY+yT;
                    if(tGX>=0&&tGX<currentGridSize&&tGY>=0&&tGY<currentGridSize){
                        if(aV>=128){
                            const imagePixelRgba = `rgba(${rV},${gV},${bV},${(aV/255).toFixed(3)})`;
                            setPixel(tGX,tGY, imagePixelRgba);
                        } else {
                            setPixel(tGX,tGY, EMPTY_COLOR);
                        }
                    }
                }
            }
            requestFullRedraw();
        }

        // Pixel-perfect image import (preserves original resolution)
        function renderImageToGridPixelPerfect(img){
            if(!activeLayer||!activeLayer.isVisible){alert("Cannot import image: active layer is not visible or does not exist.");return}
            if(currentGridSize<=0){console.warn("Cannot determine grid for image import.");return}

            const iOW = img.width; // Original image width
            const iOH = img.height; // Original image height

            // Use original image dimensions (1:1 pixel mapping)
            const dWILC = iOW;
            const dHILC = iOH;

            // Center the image on the canvas (starting from top-left of visible area if image is larger)
            const startX = Math.floor((currentGridSize - dWILC) / 2);
            const startY = Math.floor((currentGridSize - dHILC) / 2);

            // Create temporary canvas to extract pixel data
            const tC = document.createElement('canvas');
            tC.width = dWILC;
            tC.height = dHILC;
            const tCtx = tC.getContext('2d');
            tCtx.imageSmoothingEnabled = false; // Preserve sharp pixels

            // Draw image at original size
            tCtx.drawImage(img, 0, 0, dWILC, dHILC);
            const data = tCtx.getImageData(0, 0, dWILC, dHILC).data;

            // Place pixels on grid with 1:1 mapping
            for(let yT = 0; yT < dHILC; yT++){
                for(let xT = 0; xT < dWILC; xT++){
                    const idx = (yT * dWILC + xT) * 4;
                    const rV = data[idx], gV = data[idx + 1], bV = data[idx + 2], aV = data[idx + 3];
                    const tGX = startX + xT, tGY = startY + yT;

                    // Only place pixels that fit within the canvas
                    if(tGX >= 0 && tGX < currentGridSize && tGY >= 0 && tGY < currentGridSize){
                        if(aV >= 128){
                            const imagePixelRgba = `rgba(${rV},${gV},${bV},${(aV/255).toFixed(3)})`;
                            setPixel(tGX, tGY, imagePixelRgba);
                        } else {
                            setPixel(tGX, tGY, EMPTY_COLOR);
                        }
                    }
                }
            }
            requestFullRedraw();
        }



        // Smart scale detection system for nearest-neighbor upscaled pixel art
        function detectImageScaling(imageData, width, height) {
            console.log(`🔍 Smart scale detection for ${width}x${height} image`);

            // Helper function to calculate color distance between two pixels
            function colorDistance(idx1, idx2) {
                const r1 = imageData.data[idx1], g1 = imageData.data[idx1 + 1], b1 = imageData.data[idx1 + 2], a1 = imageData.data[idx1 + 3];
                const r2 = imageData.data[idx2], g2 = imageData.data[idx2 + 1], b2 = imageData.data[idx2 + 2], a2 = imageData.data[idx2 + 3];
                return Math.sqrt((r1-r2)**2 + (g1-g2)**2 + (b1-b2)**2 + (a1-a2)**2);
            }

            // Check if all pixels in a block are uniform (within tolerance)
            function isBlockUniform(blockX, blockY, scale, tolerance = 5) {
                const baseX = blockX * scale;
                const baseY = blockY * scale;
                const baseIdx = (baseY * width + baseX) * 4;

                for (let dy = 0; dy < scale; dy++) {
                    for (let dx = 0; dx < scale; dx++) {
                        const checkX = baseX + dx;
                        const checkY = baseY + dy;

                        if (checkX >= width || checkY >= height) continue;

                        const checkIdx = (checkY * width + checkX) * 4;
                        if (colorDistance(baseIdx, checkIdx) > tolerance) {
                            return false;
                        }
                    }
                }
                return true;
            }

            let bestScale = 1;
            let bestConfidence = 0;

            // Test scale factors from 2 to 30
            for (let scale = 2; scale <= 30; scale++) {
                // Check if dimensions are divisible by scale factor
                if (width % scale !== 0 || height % scale !== 0) continue;

                const blocksX = width / scale;
                const blocksY = height / scale;
                const totalBlocks = blocksX * blocksY;

                console.log(`🔍 Testing ${scale}x scale (${blocksX}x${blocksY} = ${totalBlocks} blocks)`);

                // Sample blocks for testing (max 200 blocks for performance)
                const maxSamples = Math.min(200, totalBlocks);
                const sampleStep = Math.max(1, Math.floor(totalBlocks / maxSamples));

                let uniformBlocks = 0;
                let sampledBlocks = 0;

                for (let i = 0; i < totalBlocks; i += sampleStep) {
                    const blockX = i % blocksX;
                    const blockY = Math.floor(i / blocksX);

                    if (blockY >= blocksY) break;

                    sampledBlocks++;

                    if (isBlockUniform(blockX, blockY, scale)) {
                        uniformBlocks++;
                    }

                    // Early exit if confidence is clearly too low
                    if (sampledBlocks >= 50 && (uniformBlocks / sampledBlocks) < 0.7) {
                        break;
                    }
                }

                const confidence = sampledBlocks > 0 ? uniformBlocks / sampledBlocks : 0;
                console.log(`🔍 Scale ${scale}x: ${uniformBlocks}/${sampledBlocks} uniform blocks (${(confidence * 100).toFixed(1)}% confidence)`);

                // Update best scale if this has higher confidence
                // For upscaling detection, we want the LARGEST scale factor that works
                // So we prefer higher scale factors when confidence is similar
                if (confidence > bestConfidence || (confidence >= 0.90 && scale > bestScale)) {
                    bestConfidence = confidence;
                    bestScale = scale;
                }

                // Don't stop early - we want to find the maximum scale factor that works
            }

            // Only return a scale factor if confidence is above threshold
            if (bestConfidence >= 0.90) {
                console.log(`✅ Detected ${bestScale}x upscaling with ${(bestConfidence * 100).toFixed(1)}% confidence`);
                return bestScale;
            } else {
                console.log(`❌ No upscaling detected (best: ${bestScale}x at ${(bestConfidence * 100).toFixed(1)}% confidence)`);
                return 1;
            }
        }

        // Downscale an image by the detected scale factor
        function downscaleImage(img, scaleFactor) {
            const originalWidth = img.width;
            const originalHeight = img.height;
            const newWidth = Math.floor(originalWidth / scaleFactor);
            const newHeight = Math.floor(originalHeight / scaleFactor);

            console.log(`📉 Downscaling image from ${originalWidth}x${originalHeight} to ${newWidth}x${newHeight} (${scaleFactor}x factor)`);

            // Create canvas to get original image data
            const sourceCanvas = document.createElement('canvas');
            sourceCanvas.width = originalWidth;
            sourceCanvas.height = originalHeight;
            const sourceCtx = sourceCanvas.getContext('2d');
            sourceCtx.imageSmoothingEnabled = false;
            sourceCtx.drawImage(img, 0, 0);
            const sourceData = sourceCtx.getImageData(0, 0, originalWidth, originalHeight);

            // Create target canvas for downscaled image
            const targetCanvas = document.createElement('canvas');
            targetCanvas.width = newWidth;
            targetCanvas.height = newHeight;
            const targetCtx = targetCanvas.getContext('2d');
            targetCtx.imageSmoothingEnabled = false;
            const targetData = targetCtx.createImageData(newWidth, newHeight);

            // Sample one pixel from each scaled block (top-left pixel of each block)
            for (let y = 0; y < newHeight; y++) {
                for (let x = 0; x < newWidth; x++) {
                    const sourceX = x * scaleFactor;
                    const sourceY = y * scaleFactor;

                    // Make sure we don't go out of bounds
                    if (sourceX < originalWidth && sourceY < originalHeight) {
                        const sourceIdx = (sourceY * originalWidth + sourceX) * 4;
                        const targetIdx = (y * newWidth + x) * 4;

                        targetData.data[targetIdx] = sourceData.data[sourceIdx];         // R
                        targetData.data[targetIdx + 1] = sourceData.data[sourceIdx + 1]; // G
                        targetData.data[targetIdx + 2] = sourceData.data[sourceIdx + 2]; // B
                        targetData.data[targetIdx + 3] = sourceData.data[sourceIdx + 3]; // A
                    }
                }
            }

            targetCtx.putImageData(targetData, 0, 0);

            console.log(`✅ Downscaling complete: ${newWidth}x${newHeight}`);

            // Create a new image from the downscaled canvas
            const downscaledImg = new Image();
            downscaledImg.src = targetCanvas.toDataURL();
            return downscaledImg;
        }

        // Main image import function that chooses the method based on settings
        function renderImageToGrid(img){
            if (imageImportMode === 'pixel-perfect') {
                renderImageToGridPixelPerfect(img);
            } else {
                renderImageToGridFitToView(img);
            }
        }
        function processDroppedImage(f){
            if(!activeLayer||!activeLayer.isVisible){alert("Please select a visible layer before importing an image.");return}

            // Show image import warning if enabled and using fit-to-view mode
            if (showImageImportWarning && imageImportMode === 'fit-to-view') {
                showImageImportWarningModal(f);
                return;
            }

            // Proceed with import
            importImageFile(f);
        }

        async function importImageAsNewLayer(f) {
            if (currentLayers.length >= MAX_LAYERS) {
                alert(`Max layers (${MAX_LAYERS}) reached.`);
                return;
            }

            // Show image import warning if enabled and using fit-to-view mode
            if (showImageImportWarning && imageImportMode === 'fit-to-view') {
                showImageImportWarningModal(f, true); // Pass true to indicate this is for new layer
                return;
            }

            // Proceed with import as new layer
            await importImageFileAsNewLayer(f);
        }

        async function importImageFileAsNewLayer(f) {
            // Show import progress indicator
            showImageImportStatusIndicator();

            try {
                // Step 1: Read file (10% progress)
                updateImageImportProgress(10, 'Reading image file...');
                const imageDataUrl = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = e => resolve(e.target.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(f);
                });

                if (isImageImportCancelled) {
                    hideImageImportStatusIndicator();
                    return;
                }

                // Step 2: Load image (30% progress)
                updateImageImportProgress(30, 'Loading image...');
                const img = await new Promise((resolve, reject) => {
                    const image = new Image();
                    image.onload = () => resolve(image);
                    image.onerror = reject;
                    image.src = imageDataUrl;
                });

                if (isImageImportCancelled) {
                    hideImageImportStatusIndicator();
                    return;
                }

                let finalImage = img;

                // Step 3: Check for scaling (pixel-perfect mode only)
                if (imageImportMode === 'pixel-perfect') {
                    updateImageImportProgress(50, 'Analyzing image scaling...');

                    // Create canvas to analyze image data
                    const analysisCanvas = document.createElement('canvas');
                    analysisCanvas.width = img.width;
                    analysisCanvas.height = img.height;
                    const analysisCtx = analysisCanvas.getContext('2d');
                    analysisCtx.imageSmoothingEnabled = false;
                    analysisCtx.drawImage(img, 0, 0);
                    const imageData = analysisCtx.getImageData(0, 0, img.width, img.height);

                    if (isImageImportCancelled) {
                        hideImageImportStatusIndicator();
                        return;
                    }

                    // Detect scaling
                    const detectedScale = detectImageScaling(imageData, img.width, img.height);

                    if (detectedScale > 1) {
                        updateImageImportProgress(70, `Detected ${detectedScale}x scaling, downscaling image...`);

                        // Downscale the image
                        finalImage = await new Promise((resolve) => {
                            const downscaled = downscaleImage(img, detectedScale);
                            downscaled.onload = () => resolve(downscaled);
                        });

                        if (isImageImportCancelled) {
                            hideImageImportStatusIndicator();
                            return;
                        }

                        console.log(`Image downscaled from ${img.width}x${img.height} to ${finalImage.width}x${finalImage.height} (${detectedScale}x scale detected)`);
                    }
                }

                if (isImageImportCancelled) {
                    hideImageImportStatusIndicator();
                    return;
                }

                // Step 4: Create new layer and import to it (90% progress)
                updateImageImportProgress(90, 'Creating new layer and importing...');

                // Small delay to show progress
                await new Promise(resolve => setTimeout(resolve, 100));

                if (isImageImportCancelled) {
                    hideImageImportStatusIndicator();
                    return;
                }

                // Create new layer
                const aC = canvases[activeCanvasIndex];
                if (!aC) return;

                // Generate auto-numbered name for imported image
                const nextImportNumber = findNextAvailableImportedImageNumber(currentLayers);
                const importedImageName = `Imported Image (${nextImportNumber})`;

                const newLayer = createNewLayer(importedImageName, currentGridSize, aC);
                const insertionIndex = currentActiveLayerIndex + 1;
                currentLayers.splice(insertionIndex, 0, newLayer);

                // Set the new layer as active
                setActiveLayer(insertionIndex);

                // Reset undo/redo for the canvas
                clearUndoRedoStacks();
                aC.undoStack = currentUndoStack; aC.redoStack = currentRedoStack;

                // Import the image to the new layer
                renderImageToGrid(finalImage);

                // Step 5: Complete (100% progress)
                updateImageImportProgress(100, 'Import complete!');

                // Hide indicator after a short delay
                setTimeout(() => {
                    hideImageImportStatusIndicator();
                }, 500);

                debouncedSaveToLocalStorage();

            } catch (error) {
                console.error('Error importing image as new layer:', error);
                hideImageImportStatusIndicator();
                alert('Error importing image file as new layer.');
            }
        }

        async function importImageFile(f) {
            saveState();

            // Show import progress indicator
            showImageImportStatusIndicator();

            try {
                // Step 1: Read file (10% progress)
                updateImageImportProgress(10, 'Reading image file...');
                const imageDataUrl = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = e => resolve(e.target.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(f);
                });

                if (isImageImportCancelled) {
                    hideImageImportStatusIndicator();
                    return;
                }

                // Step 2: Load image (30% progress)
                updateImageImportProgress(30, 'Loading image...');
                const img = await new Promise((resolve, reject) => {
                    const image = new Image();
                    image.onload = () => resolve(image);
                    image.onerror = reject;
                    image.src = imageDataUrl;
                });

                if (isImageImportCancelled) {
                    hideImageImportStatusIndicator();
                    return;
                }

                let finalImage = img;

                // Step 3: Check for scaling (pixel-perfect mode only)
                if (imageImportMode === 'pixel-perfect') {
                    updateImageImportProgress(50, 'Analyzing image scaling...');

                    // Create canvas to analyze image data
                    const analysisCanvas = document.createElement('canvas');
                    analysisCanvas.width = img.width;
                    analysisCanvas.height = img.height;
                    const analysisCtx = analysisCanvas.getContext('2d');
                    analysisCtx.imageSmoothingEnabled = false;
                    analysisCtx.drawImage(img, 0, 0);
                    const imageData = analysisCtx.getImageData(0, 0, img.width, img.height);

                    if (isImageImportCancelled) {
                        hideImageImportStatusIndicator();
                        return;
                    }

                    // Detect scaling
                    const detectedScale = detectImageScaling(imageData, img.width, img.height);

                    if (detectedScale > 1) {
                        updateImageImportProgress(70, `Detected ${detectedScale}x scaling, downscaling image...`);

                        // Downscale the image
                        finalImage = await new Promise((resolve) => {
                            const downscaled = downscaleImage(img, detectedScale);
                            downscaled.onload = () => resolve(downscaled);
                        });

                        if (isImageImportCancelled) {
                            hideImageImportStatusIndicator();
                            return;
                        }

                        console.log(`Image downscaled from ${img.width}x${img.height} to ${finalImage.width}x${finalImage.height} (${detectedScale}x scale detected)`);
                    }
                }

                // Step 4: Import to canvas (90% progress)
                updateImageImportProgress(90, 'Importing to canvas...');

                // Small delay to show progress
                await new Promise(resolve => setTimeout(resolve, 100));

                if (isImageImportCancelled) {
                    hideImageImportStatusIndicator();
                    return;
                }

                // Import the image
                renderImageToGrid(finalImage);

                // Step 5: Complete (100% progress)
                updateImageImportProgress(100, 'Import complete!');

                // Hide indicator after a short delay
                setTimeout(() => {
                    hideImageImportStatusIndicator();
                }, 500);

            } catch (error) {
                console.error('Error importing image:', error);
                hideImageImportStatusIndicator();
                alert('Error importing image file.');
            }
        }
        canvasContainer.addEventListener('dragover',e=>{e.preventDefault();e.dataTransfer.dropEffect='copy';canvasContainer.style.outline='2px dashed #007bff'});
        canvasContainer.addEventListener('dragleave',e=>{canvasContainer.style.outline='none'});
        canvasContainer.addEventListener('drop',e=>{e.preventDefault();canvasContainer.style.outline='none';if(e.dataTransfer.files&&e.dataTransfer.files.length>0){const f=e.dataTransfer.files[0];if(f.type.startsWith('image/'))processDroppedImage(f);else alert('Please drop an image file (e.g., PNG, JPG, GIF).');e.dataTransfer.clearData()}});
        function updateLayerCountDisplay() {
            layerCountSpan.textContent = currentLayers.length;
            if (mergeLayerDownBtn) mergeLayerDownBtn.disabled = !(currentLayers.length > 1 && currentActiveLayerIndex > 0);
            // Add disable logic for duplicate button if max layers reached or no active layer
            if (duplicateLayerBtn) {
                duplicateLayerBtn.disabled = (currentLayers.length >= MAX_LAYERS || activeCanvasIndex === -1 || currentLayers.length === 0);
            }
            // Ensure newLayerBtn is also correctly managed here or elsewhere for MAX_LAYERS
            if (newLayerBtn) {
                newLayerBtn.disabled = (currentLayers.length >= MAX_LAYERS);
            }
        }
        function duplicateActiveLayer() {
            if (activeCanvasIndex === -1 || !currentLayers || currentLayers.length === 0) {
                alert("No active layer to duplicate.");
                return;
            }
            if (currentLayers.length >= MAX_LAYERS) {
                alert(`Maximum number of layers (${MAX_LAYERS}) reached.`);
                return;
            }

            const originalLayer = currentLayers[currentActiveLayerIndex];
            if (!originalLayer) {
                // console.error("Original layer not found for duplication.");
                return;
            }

            saveState(); // Save state *before* duplication for potential undo of the duplication itself if needed
                         // Though the requirement mentions resetting undo/redo for the canvas after duplication.
                         // Let's stick to resetting undo/redo as per typical layer duplication behavior.

            const newLayerName = `${originalLayer.name} (1)`;
            const newLayerPixelData = originalLayer.pixels ? JSON.parse(JSON.stringify(originalLayer.pixels)) : createNewPixelData(currentGridSize);

            const newLayer = {
                id: ++nextLayerIdGlobal,
                name: newLayerName,
                pixels: newLayerPixelData,
                isVisible: originalLayer.isVisible,
                opacity: originalLayer.opacity
            };

            const insertionIndex = currentActiveLayerIndex + 1;
            currentLayers.splice(insertionIndex, 0, newLayer);

            // Set the new duplicated layer as active
            currentActiveLayerIndex = insertionIndex; // Update currentActiveLayerIndex directly before calling setActiveLayer
                                                  // to ensure setActiveLayer uses the correct new index

            const activeC = canvases[activeCanvasIndex];
            if (activeC) {
                activeC.activeLayerIndex = currentActiveLayerIndex; // Update canvas state
                // Reset undo/redo stacks for this canvas as layer structure changed significantly
                clearUndoRedoStacks();
                activeC.undoStack = currentUndoStack;
                activeC.redoStack = currentRedoStack;
            }

            activeLayer = newLayer; // Update global activeLayer reference

            renderLayersUI(); // This will also update counts and button states
            requestFullRedraw();
            debouncedSaveToLocalStorage();
        }
        function onLayerItemMouseDown(event) {
            // Prevent drag if clicking on interactive elements or not left button
            if (event.target.closest('button, input, .layer-visibility, .layer-opacity-control') || event.button !== 0) {
                return;
            }

            const currentTargetLi = event.currentTarget; // The li element
            const originalIndex = parseInt(currentTargetLi.dataset.layerIndex);

            isPotentialLayerDrag = true;
            layerMouseDownPos = { x: event.clientX, y: event.clientY };
            draggedLayerElement = currentTargetLi; // Store temporarily
            draggedLayerOriginalIndex = originalIndex; // Store temporarily

            // Listener to cancel hold if mouse moves too much
            document.addEventListener('mousemove', onInitialMouseMoveDuringHold);
            // Listener for mouseup (handles click or end of a successful drag)
            document.addEventListener('mouseup', onMouseUpAfterLayerInteraction);

            layerDragHoldTimeout = setTimeout(() => {
                if (!isPotentialLayerDrag) { // Mouseup or significant move occurred before timeout
                    return;
                }
                // Hold duration met, and mouse didn't move significantly (or mouseup didn't occur)
                isLayerDragging = true; // Officially start dragging
                isPotentialLayerDrag = false; // Potential phase is over

                // No need to re-assign draggedLayerElement and draggedLayerOriginalIndex, they are already set

                if (draggedLayerElement) {
                    draggedLayerElement.classList.add('dragging');
                }
                layersList.classList.add('dragging-active'); // Show all indicators
                currentDropTargetFinalIndex = -1;

                // Now that drag is active, the main drag over logic is needed for indicators
                // The onInitialMouseMoveDuringHold will now branch to onLayerDragOver
                // Or, we could switch listeners here:
                // document.removeEventListener('mousemove', onInitialMouseMoveDuringHold);
                // document.addEventListener('mousemove', onLayerDragOver);
                // For simplicity, onInitialMouseMoveDuringHold will just call onLayerDragOver if isLayerDragging is true.

            }, DRAG_HOLD_DURATION);

            event.preventDefault(); // Prevent text selection, etc.
        }
        function onInitialMouseMoveDuringHold(event) {
            if (!isPotentialLayerDrag && !isLayerDragging) {
                // Neither a potential drag nor an active drag, clean up and return
                clearTimeout(layerDragHoldTimeout);
                document.removeEventListener('mousemove', onInitialMouseMoveDuringHold);
                document.removeEventListener('mouseup', onMouseUpAfterLayerInteraction);
                return;
            }

            if (isLayerDragging) { // Drag has officially started, handle indicator updates
                onLayerDragOver(event);
                return;
            }

            // If still in potential drag phase (timer running)
            if (isPotentialLayerDrag) {
                const dx = Math.abs(event.clientX - layerMouseDownPos.x);
                const dy = Math.abs(event.clientY - layerMouseDownPos.y);

                if (dx > DRAG_INITIATION_THRESHOLD || dy > DRAG_INITIATION_THRESHOLD) {
                    // Mouse moved too much before hold timeout
                    clearTimeout(layerDragHoldTimeout);
                    isPotentialLayerDrag = false;
                    // No drag, no click. Clean up listeners that would have been removed by onMouseUpAfterLayerInteraction
                    document.removeEventListener('mousemove', onInitialMouseMoveDuringHold);
                    // Note: onMouseUpAfterLayerInteraction will still fire and handle full cleanup.
                }
            }
        }

        function onLayerDragOver(event) {
            if (!isLayerDragging) return;
            event.preventDefault();

            const mouseY = event.clientY;
            currentDropTargetFinalIndex = -1; // Reset
            currentDropTargetVisualIndex = -1; // <-- ADD THIS RESET

            let closestIndicator = null;
            let smallestDistance = Infinity;

            dropIndicatorElements.forEach(indicator => {
                indicator.classList.remove('highlighted');
            });

            dropIndicatorElements.forEach(indicator => {
                const rect = indicator.getBoundingClientRect();
                const indicatorCenterY = rect.top + rect.height / 2;
                const distance = Math.abs(mouseY - indicatorCenterY);

                if (distance < smallestDistance) {
                    smallestDistance = distance;
                    closestIndicator = indicator;
                }
            });

            if (closestIndicator) {
                closestIndicator.classList.add('highlighted');
                currentDropTargetFinalIndex = parseInt(closestIndicator.dataset.targetFinalIndex);
                currentDropTargetVisualIndex = parseInt(closestIndicator.dataset.visualIndex); // <-- STORE THIS
            }
        }


        function onMouseUpAfterLayerInteraction(event) {
            clearTimeout(layerDragHoldTimeout);
            document.removeEventListener('mousemove', onInitialMouseMoveDuringHold);
            document.removeEventListener('mouseup', onMouseUpAfterLayerInteraction);

            const M = currentLayers.length;

            if (isLayerDragging) {
                if (draggedLayerElement) {
                    draggedLayerElement.classList.remove('dragging');
                }
                layersList.classList.remove('dragging-active');
                dropIndicatorElements.forEach(ind => ind.classList.remove('highlighted'));

                if (currentDropTargetFinalIndex !== -1) { // A valid drop target was highlighted
                    const fromIndex = draggedLayerOriginalIndex;
                    let toIndex = currentDropTargetFinalIndex; // Base target from indicator's data-target-final-index

                    if (fromIndex > toIndex) { // Dragging "down" the array (from higher index to numerically lower index)
                        // This is visually dragging "up" the reversed list.

                        // Distinguish based on which indicator yielded currentDropTargetFinalIndex = 0
                        if (toIndex === 0 && currentDropTargetVisualIndex === M) {
                            // Target is 0 AND it's the *absolute bottom-most indicator* (visualIndex M).
                            // The layer should become the new bottom (index 0). No +1 adjustment.
                            // 'toIndex' correctly remains 0.
                        } else {
                            // For all other downward drags:
                            // - Includes dragging to the indicator *between the last two items*
                            //   (visualIndex M-1, which also has targetFinalIndex 0).
                            // - Or any other downward drag where targetFinalIndex > 0.
                            // Apply the +1 logic that handles "no-op for bar below self"
                            // and the "off-by-one to make it land right above" for other downward moves.
                            toIndex = toIndex + 1;
                            if (toIndex >= fromIndex) {
                                // This makes it a no-op if the +1 caused it to meet or exceed the original position.
                                // e.g., L3 (from=2), indicator target=1. toIndex becomes 1+1=2.
                                // Since 2 >= 2, toIndex becomes 2 (fromIndex). This is a no-op.
                                toIndex = fromIndex;
                            }
                            // Cap to ensure it's a valid index.
                            toIndex = Math.min(toIndex, M - 1);
                        }
                    }
                    // For upward drags (fromIndex < toIndex), 'toIndex' remains 'currentDropTargetFinalIndex' as is.

                    if (toIndex !== fromIndex) {
                        moveLayer(fromIndex, toIndex);
                    } else {
                        renderLayersUI();
                        requestFullRedraw();
                    }
                } else if (isLayerDragging) { // Dragged but not onto a valid target
                    renderLayersUI();
                    requestFullRedraw();
                }
            } else if (isPotentialLayerDrag) {
                setActiveLayer(draggedLayerOriginalIndex);
            }

            isLayerDragging = false;
            isPotentialLayerDrag = false;
            draggedLayerElement = null;
            draggedLayerOriginalIndex = -1;
            currentDropTargetFinalIndex = -1;
            currentDropTargetVisualIndex = -1; // <-- ADD THIS RESET
        }
        function renderLayersUI() {
            layersList.innerHTML = ''; // Clear existing items
            updateLayerCountDisplay();

            if (!currentLayers) {
                return;
            }

            dropIndicatorElements = []; // Reset for the fresh render
            const M = currentLayers.length;

            if (M === 0) {
                return;
            }

            const topVisualIndicator = document.createElement('div');
            topVisualIndicator.className = 'drop-indicator';
            layersList.appendChild(topVisualIndicator);
            dropIndicatorElements.push(topVisualIndicator);

            currentLayers.slice().reverse().forEach((layer, revIdx) => {
                const actualIndex = M - 1 - revIdx;
                const listItem = document.createElement('li');
                listItem.className = 'layer-item';
                listItem.dataset.layerId = layer.id;
                listItem.dataset.layerIndex = actualIndex;

                if (actualIndex === currentActiveLayerIndex) listItem.classList.add('active-layer');
                if (isLayerDragging && actualIndex === draggedLayerOriginalIndex) {
                    listItem.classList.add('dragging');
                }

                // --- Layer Item Content (visibility, name, controls, opacity) ---
                // [ YOUR EXISTING CODE FOR LIST ITEM CONTENT REMAINS UNCHANGED ]
                // (Copy from your file: detailsRow, visibilityToggle, nameSpan, controlsDiv, etc.)
                const detailsRow = document.createElement('div'); detailsRow.className = 'layer-details-row';
                const visibilityToggle = document.createElement('div'); visibilityToggle.className = 'layer-visibility';
                visibilityToggle.classList.toggle('visible', layer.isVisible); visibilityToggle.classList.toggle('hidden', !layer.isVisible);
                visibilityToggle.title = layer.isVisible ? "Hide Layer" : "Show Layer";
                visibilityToggle.onclick = e => { e.stopPropagation(); layer.isVisible = !layer.isVisible; requestFullRedraw(); renderLayersUI(); debouncedSaveToLocalStorage(); };

                const nameSpan = document.createElement('span'); nameSpan.className = 'layer-name'; nameSpan.textContent = layer.name; nameSpan.title = layer.name;
                const controlsDiv = document.createElement('div'); controlsDiv.className = 'layer-controls';
                const editBtn = document.createElement('button'); editBtn.textContent = '✏️'; editBtn.title = "Rename Layer";
                editBtn.onclick = e => { e.stopPropagation(); if(nameSpan.querySelector('input'))return; const oN=layer.name; nameSpan.innerHTML=''; const i=document.createElement('input');i.type='text';i.value=oN;i.maxLength=LAYER_NAME_MAX_LENGTH;i.spellcheck=false;i.onblur=()=>{const nN=i.value.trim();if(nN){layer.name=nN}else{const otherLayers=currentLayers.filter(l=>l.id!==layer.id);layer.name=`Layer ${findNextAvailableLayerNumber(otherLayers)}`}renderLayersUI();debouncedSaveToLocalStorage()};i.onkeydown=ev=>{ev.stopPropagation();if(ev.key==='Enter'){ev.preventDefault();i.blur()}else if(ev.key==='Escape'){ev.preventDefault();i.value=oN;i.blur()}};i.onclick=ev=>ev.stopPropagation();nameSpan.appendChild(i);i.focus();if(typeof i.select==='function')i.select()};

                const moveUpBtn = document.createElement('button'); moveUpBtn.textContent = '↑'; moveUpBtn.title = "Move Layer Up"; moveUpBtn.disabled = actualIndex === M - 1;
                moveUpBtn.onclick = e => { e.stopPropagation(); moveLayer(actualIndex, actualIndex + 1); };
                const moveDownBtn = document.createElement('button'); moveDownBtn.textContent = '↓'; moveDownBtn.title = "Move Layer Down"; moveDownBtn.disabled = actualIndex === 0;
                moveDownBtn.onclick = e => { e.stopPropagation(); moveLayer(actualIndex, actualIndex - 1); };
                const deleteBtn = document.createElement('button'); deleteBtn.textContent = '✕'; deleteBtn.title = "Delete Layer (Ctrl+Click to bypass warning)"; deleteBtn.disabled = M <= 1;
                deleteBtn.onclick = e => { e.stopPropagation(); if (e.ctrlKey || e.metaKey || confirm(`Delete layer "${layer.name}"?`)) deleteLayer(actualIndex); };

                detailsRow.append(visibilityToggle, nameSpan); controlsDiv.append(editBtn, moveUpBtn, moveDownBtn, deleteBtn); detailsRow.appendChild(controlsDiv); listItem.appendChild(detailsRow);

                const opacityControlWrapper = document.createElement('div'); opacityControlWrapper.className = 'layer-opacity-control';
                const opacitySetDiv = document.createElement('div'); opacitySetDiv.className = 'opacity-control-set';
                const inputLineDiv = document.createElement('div'); inputLineDiv.className = 'opacity-input-line';
                const opacityLabel = document.createElement('label'); opacityLabel.htmlFor = `layerOpacityInput_${layer.id}`; opacityLabel.textContent = 'Opacity:';
                const layerOpacityInput = document.createElement('input'); layerOpacityInput.type = 'number'; layerOpacityInput.id = `layerOpacityInput_${layer.id}`; layerOpacityInput.min = '0'; layerOpacityInput.max = '100'; layerOpacityInput.value = layer.opacity; layerOpacityInput.dataset.layerIndex = actualIndex;
                const percentSpan = document.createElement('span'); percentSpan.textContent = '%';
                inputLineDiv.append(opacityLabel, layerOpacityInput, percentSpan);
                const layerOpacitySlider = document.createElement('input'); layerOpacitySlider.type = 'range'; layerOpacitySlider.id = `layerOpacitySlider_${layer.id}`; layerOpacitySlider.min = '0'; layerOpacitySlider.max = '100'; layerOpacitySlider.value = layer.opacity; layerOpacitySlider.dataset.layerIndex = actualIndex;
                layerOpacityInput.oninput = () => { validateAndClampInput(layerOpacityInput, 0, 100, (val) => { if (currentLayers[actualIndex]) currentLayers[actualIndex].opacity = val; requestFullRedraw(); debouncedSaveToLocalStorage(); }, layerOpacitySlider); };
                layerOpacityInput.onblur = () => { validateAndClampInput(layerOpacityInput, 0, 100, (val) => { if (currentLayers[actualIndex]) currentLayers[actualIndex].opacity = val; debouncedSaveToLocalStorage(); }, layerOpacitySlider); };
                layerOpacitySlider.oninput = ev => { const val = parseInt(ev.target.value); if (currentLayers[actualIndex]) { currentLayers[actualIndex].opacity = val; layerOpacityInput.value = val; requestFullRedraw(); debouncedSaveToLocalStorage(); } };
                [layerOpacityInput, layerOpacitySlider].forEach(el => el.onclick = ev => ev.stopPropagation());
                opacitySetDiv.append(inputLineDiv, layerOpacitySlider); opacityControlWrapper.appendChild(opacitySetDiv); listItem.appendChild(opacityControlWrapper);
                // --- End Layer Item Content ---

                listItem.addEventListener('mousedown', onLayerItemMouseDown);
                layersList.appendChild(listItem);

                const interItemIndicator = document.createElement('div');
                interItemIndicator.className = 'drop-indicator';
                layersList.appendChild(interItemIndicator);
                dropIndicatorElements.push(interItemIndicator);
            });

            // 4. Set targetFinalIndex for all indicators
            if (M > 0 && dropIndicatorElements.length === M + 1) {
                dropIndicatorElements.forEach((indicator, visualIdx) => {
                    // visualIdx is the 0-indexed visual position of the indicator bar
                    // from top (0) to bottom (M).
                    let targetArrayIndex = M - 1 - visualIdx;
                    indicator.dataset.targetFinalIndex = String(Math.max(0, targetArrayIndex));
                    indicator.dataset.visualIndex = String(visualIdx); // <-- ADD THIS LINE
                });
            }
        }

        if (newLayerBtn) { // Check if element exists
            newLayerBtn.addEventListener('click', () => {
                // Use security validation if available
                if (typeof SecurityUtils !== 'undefined' && SecurityUtils.ResourceManager) {
                    const validation = SecurityUtils.ResourceManager.validateLayerCount(currentLayers.length);
                    if (!validation.valid) {
                        showNotification(validation.error, 'error');
                        return;
                    }
                } else {
                    // Fallback validation
                    if (currentLayers.length >= MAX_LAYERS) {
                        showNotification(`Max layers (${MAX_LAYERS}) reached.`, 'error');
                        return;
                    }
                
                }
                const aC = canvases[activeCanvasIndex];
                if (!aC) return;
                
                const nL = createNewLayer(null, currentGridSize, aC);
                if (!nL) return; // createNewLayer failed validation
                
                const iAI = currentActiveLayerIndex + 1;
                currentLayers.splice(iAI, 0, nL);
                setActiveLayer(iAI); // setActiveLayer updates currentActiveLayerIndex, activeLayer, and renders UI

                // Reset undo/redo for the canvas
                clearUndoRedoStacks();
                aC.undoStack = currentUndoStack; aC.redoStack = currentRedoStack;
                requestFullRedraw();
                debouncedSaveToLocalStorage();
            });
        }
        if (duplicateLayerBtn) { // Check if element exists
            duplicateLayerBtn.addEventListener('click', duplicateActiveLayer);
        }

        // Import Image as Layer functionality
        const importImageAsLayerBtn = document.getElementById('importImageAsLayerBtn');
        const importImageAsLayerInput = document.getElementById('importImageAsLayerInput');

        if (importImageAsLayerBtn && importImageAsLayerInput) {
            importImageAsLayerBtn.addEventListener('click', () => {
                if (currentLayers.length >= MAX_LAYERS) {
                    alert(`Max layers (${MAX_LAYERS}) reached.`);
                    return;
                }
                importImageAsLayerInput.click();
            });

            importImageAsLayerInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file && file.type.startsWith('image/')) {
                    importImageAsNewLayer(file);
                }
                // Reset the input so the same file can be selected again
                e.target.value = '';
            });
        }
        function mergeLayerDown() {
            if (currentLayers.length <= 1 || currentActiveLayerIndex <= 0) { alert("Cannot merge down: Not enough layers or selected layer is the bottom layer."); return; }
            const upperLayerIndex = currentActiveLayerIndex;
            const lowerLayerIndex = currentActiveLayerIndex - 1;
            const upperLayer = currentLayers[upperLayerIndex];
            const lowerLayer = currentLayers[lowerLayerIndex];
            if (!upperLayer || !lowerLayer) { console.error("Error finding layers for merge."); return; }

            const tempMergeCanvas = document.createElement('canvas'); tempMergeCanvas.width = currentGridSize; tempMergeCanvas.height = currentGridSize;
            const tempMergeCtx = tempMergeCanvas.getContext('2d'); tempMergeCtx.imageSmoothingEnabled = false;

            if (lowerLayer.pixels) {
                tempMergeCtx.globalAlpha = lowerLayer.opacity / 100;
                for (let y = 0; y < currentGridSize; y++) for (let x = 0; x < currentGridSize; x++) if (lowerLayer.pixels[y] && lowerLayer.pixels[y][x] !== EMPTY_COLOR) { tempMergeCtx.fillStyle = lowerLayer.pixels[y][x]; tempMergeCtx.fillRect(x, y, 1, 1); }
                tempMergeCtx.globalAlpha = 1.0;
            }
            if (upperLayer.pixels) {
                tempMergeCtx.globalAlpha = upperLayer.opacity / 100; // Apply upper layer's opacity during merge
                for (let y = 0; y < currentGridSize; y++) for (let x = 0; x < currentGridSize; x++) if (upperLayer.pixels[y] && upperLayer.pixels[y][x] !== EMPTY_COLOR) { tempMergeCtx.fillStyle = upperLayer.pixels[y][x]; tempMergeCtx.fillRect(x, y, 1, 1); }
                tempMergeCtx.globalAlpha = 1.0;
            }

            const mergedImageData = tempMergeCtx.getImageData(0, 0, currentGridSize, currentGridSize);
            const mergedData = mergedImageData.data;
            const newLowerPixels = createNewPixelData(currentGridSize);
            for (let y = 0; y < currentGridSize; y++) for (let x = 0; x < currentGridSize; x++) {
                const idx = (y * currentGridSize + x) * 4;
                const r = mergedData[idx], g = mergedData[idx+1], b = mergedData[idx+2], a = mergedData[idx+3];
                if (a > 0) newLowerPixels[y][x] = `rgba(${r},${g},${b},${(a/255).toFixed(3)})`;
                else newLowerPixels[y][x] = EMPTY_COLOR;
            }
            lowerLayer.pixels = newLowerPixels;
            lowerLayer.opacity = 100; // Merged layer is fully opaque

            currentLayers.splice(upperLayerIndex, 1);
            setActiveLayer(lowerLayerIndex);
            clearUndoRedoStacks();
            canvases[activeCanvasIndex].undoStack = currentUndoStack; canvases[activeCanvasIndex].redoStack = currentRedoStack;
            requestFullRedraw();
        }
        if(mergeLayerDownBtn)mergeLayerDownBtn.onclick=mergeLayerDown;
        function setActiveLayer(idx){if(idx>=0&&idx<currentLayers.length){const cEI=layersList.querySelector('.layer-name input');if(cEI&&currentActiveLayerIndex!==idx)if(document.activeElement&&document.activeElement.tagName==='INPUT'&&document.activeElement.parentElement.classList.contains('layer-name'))document.activeElement.blur();currentActiveLayerIndex=idx;if(canvases[activeCanvasIndex])canvases[activeCanvasIndex].activeLayerIndex=idx;activeLayer = currentLayers[currentActiveLayerIndex];renderLayersUI();}}
        function deleteLayer(idx){if(currentLayers.length<=1){alert("Cannot delete the last layer.");return}currentLayers.splice(idx,1);if(currentActiveLayerIndex>=idx&&currentActiveLayerIndex>0)currentActiveLayerIndex--;else if(currentActiveLayerIndex>=currentLayers.length)currentActiveLayerIndex=currentLayers.length-1;if(currentLayers.length>0)setActiveLayer(Math.max(0,Math.min(currentActiveLayerIndex,currentLayers.length-1)));else{const aC=canvases[activeCanvasIndex];if(aC){initializeLayersForCanvas(aC);setActiveLayer(0)}}const aC=canvases[activeCanvasIndex];if(aC){clearUndoRedoStacks();aC.undoStack=currentUndoStack;aC.redoStack=currentRedoStack;}requestFullRedraw();debouncedSaveToLocalStorage();}
        function moveLayer(fromIdx, toIdx) { // toIdx IS the FINAL desired index
            if (fromIdx < 0 || fromIdx >= currentLayers.length || toIdx < 0 || toIdx >= currentLayers.length) { // toIdx must be a valid index (0 to length-1)
                console.warn(`moveLayer: Invalid indices from ${fromIdx} to ${toIdx}`);
                return;
            }
            if (fromIdx === toIdx) return; // No change if moving to the same spot

            const item = currentLayers.splice(fromIdx, 1)[0];
            currentLayers.splice(toIdx, 0, item); // Splice at the target final index

            // Update active layer index
            if (currentActiveLayerIndex === fromIdx) {
                currentActiveLayerIndex = toIdx;
            } else {
                // If active layer was between from and to, its index needs adjustment
                if (fromIdx < currentActiveLayerIndex && toIdx >= currentActiveLayerIndex) {
                    currentActiveLayerIndex--;
                } else if (fromIdx > currentActiveLayerIndex && toIdx <= currentActiveLayerIndex) {
                    currentActiveLayerIndex++;
                }
                // If active item moved, and it's now AT or AFTER where the active item WAS,
                // and active item was NOT the one moved.
            }
            // Ensure active index is valid (especially if toIdx was at an extreme)
            currentActiveLayerIndex = Math.max(0, Math.min(currentActiveLayerIndex, currentLayers.length - 1));


            setActiveLayer(currentActiveLayerIndex); // This also updates canvas's activeLayerIndex and renders UI

            const aC = canvases[activeCanvasIndex];
            if (aC) {
                clearUndoRedoStacks();
                aC.undoStack = currentUndoStack;
                aC.redoStack = currentRedoStack;
            }
            requestFullRedraw(); // Often called by renderLayersUI via setActiveLayer, but good for directness
            debouncedSaveToLocalStorage();
        }

        // Mirroring Event Listeners (Adjusted for user's fix description)
        if (verticalMirrorToggleElem) {
            verticalMirrorToggleElem.addEventListener('change', e => {
                // This element (id="verticalMirrorToggle") now controls isHorizontalMirrorActive
                // to match the user's described "swapped functionality" fix.
                isHorizontalMirrorActive = e.target.checked;
                if (!isVerticalMirrorActive && !isHorizontalMirrorActive && !isRadialMirrorActive) {
                    mirrorCenterX = null; mirrorCenterY = null;
                }
            });
        }
        if (horizontalMirrorToggleElem) {
            horizontalMirrorToggleElem.addEventListener('change', e => {
                // This element (id="horizontalMirrorToggle") now controls isVerticalMirrorActive.
                isVerticalMirrorActive = e.target.checked;
                if (!isVerticalMirrorActive && !isHorizontalMirrorActive && !isRadialMirrorActive) {
                    mirrorCenterX = null; mirrorCenterY = null;
                }
            });
        }
        radialMirrorToggle.addEventListener('change', e => {
            isRadialMirrorActive = e.target.checked;
            radialMirrorSettings.style.display = isRadialMirrorActive ? 'block' : 'none';
            if (!isVerticalMirrorActive && !isHorizontalMirrorActive && !isRadialMirrorActive) {
                mirrorCenterX = null; mirrorCenterY = null;
            }
        });
        radialAxesSlider.addEventListener('input', e => {
            radialMirrorAxes = parseInt(e.target.value);
            radialAxesValue.textContent = radialMirrorAxes;
        });

        window.onload = () => {
            if(colorPickerInput)colorPickerInput.value=currentSelectedColor;
            if(hexInput)hexInput.value=currentSelectedColor;

            minimapCanvasElement = document.getElementById('minimapCanvas');
            if (minimapCanvasElement) {
                minimapCtx = minimapCanvasElement.getContext('2d');
                minimapCtx.imageSmoothingEnabled = false;
            } else {
                console.error("Minimap canvas element not found!");
            }
            if (scaleUpSelectionBtn) {
                scaleUpSelectionBtn.addEventListener('click', () => {
                    if (toolSelector.value === 'select' && isSelecting && currentSelectionStart && currentSelectionEnd) {
                        handleScaleSelection(2.0);
                    }
                });
            }
            if (rotate90SelectionBtn) {
                rotate90SelectionBtn.addEventListener('click', () => {
                    if (toolSelector.value === 'select' && isSelecting && currentSelectionStart && currentSelectionEnd) {
                        handleRotateSelection(90);
                    }
                });
            }
            if (rotate180SelectionBtn) {
                rotate180SelectionBtn.addEventListener('click', () => {
                    if (toolSelector.value === 'select' && isSelecting && currentSelectionStart && currentSelectionEnd) {
                        handleRotateSelection(180);
                    }
                });
            }
            if (scaleDownSelectionBtn) {
                scaleDownSelectionBtn.addEventListener('click', () => {
                    if (toolSelector.value === 'select' && isSelecting && currentSelectionStart && currentSelectionEnd) {
                        handleScaleSelection(0.5);
                    }
                });
            }
            if (flipHorizontalBtn) {
                flipHorizontalBtn.addEventListener('click', () => {
                    if (toolSelector.value === 'select' && isSelecting && currentSelectionStart && currentSelectionEnd && activeLayer && activeLayer.isVisible) {
                        handleFlipSelection('horizontal');
                    }
                });
            }
            if (flipVerticalBtn) {
                flipVerticalBtn.addEventListener('click', () => {
                    if (toolSelector.value === 'select' && isSelecting && currentSelectionStart && currentSelectionEnd && activeLayer && activeLayer.isVisible) {
                        handleFlipSelection('vertical');
                    }
                });
            }

            radialMirrorSettings.style.display = radialMirrorToggle.checked ? 'block' : 'none';
            radialAxesValue.textContent = radialAxesSlider.value;
            radialMirrorAxes = parseInt(radialAxesSlider.value);

            initializeEditor();
            setTimeout(()=>{
                resizeCanvases();
                updateBrushOptionsBarContent(); // Initial call
            },100);
        };

        // Xano Configuration
        const XANO_CONFIG = {
            baseURL: 'https://x8ki-letl-twmt.n7.xano.io/api:YVvnFfHl', // Your actual Xano workspace URL
            endpoints: {
                getUserSettings: '/user_settings', // GET /user_settings?user_id={user_id}
                saveUserSettings: '/user_settings', // POST /user_settings
                updateUserSettings: '/user_settings', // PATCH /user_settings/{id}
                checkUsernameAvailability: '/check-username-ability' // GET /check-username-ability?username={username}
            }
        };

        // Xano API Service
        class XanoService {
            static async makeRequest(endpoint, options = {}) {
                try {
                    const url = `${XANO_CONFIG.baseURL}${endpoint}`;
                    const response = await fetch(url, {
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers
                        },
                        ...options
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return await response.json();
                } catch (error) {
                    console.error('Xano API request failed:', error);
                    throw error;
                }
            }

            static async getUserSettings(userEmail) {
                try {
                    // Search by email to link accounts across different auth methods
                    const response = await this.makeRequest(`${XANO_CONFIG.endpoints.getUserSettings}?email=${encodeURIComponent(userEmail)}`);
                    // Your Xano GET function returns a single object, not an array
                    return response || null;
                } catch (error) {
                    console.error('Failed to get user settings from Xano:', error);
                    return null;
                }
            }

            static async saveUserSettings(userId, settings, userEmail = null, emailVerified = false, username = null, profileImage = null) {
                try {
                    // First, check if a record already exists for this email
                    const existingRecord = await this.getUserSettings(userEmail || userId);

                    // Your Xano POST function handles both create and update automatically
                    // Use email as primary identifier to link accounts across auth methods
                    const requestBody = {
                        user_id: userId, // Keep for reference, but email is primary
                        email: userEmail || userId, // Use email as primary identifier
                        username: username,
                        profile_image: profileImage,
                        theme: settings.theme,
                        keybinds: typeof settings.keybinds === 'object' ? JSON.stringify(settings.keybinds) : settings.keybinds,
                        email_verified: emailVerified,
                        brush_performance_mode: settings.brushPerformanceMode || false,
                        show_canvas_size_warning: settings.showCanvasSizeWarning !== undefined ? settings.showCanvasSizeWarning : true,
                        image_import_mode: settings.imageImportMode || 'fit-to-view',
                        show_image_import_warning: settings.showImageImportWarning !== undefined ? settings.showImageImportWarning : true
                    };

                    // If we found an existing record, preserve data that shouldn't be overwritten
                    if (existingRecord) {
                        const existingData = existingRecord.user_settings || existingRecord;
                        console.log('🔗 Found existing record for email, linking accounts:', {
                            existingUserId: existingData.user_id,
                            newUserId: userId,
                            email: userEmail
                        });

                        // Preserve existing username and profile image if not provided
                        if (!username && existingData.username) {
                            requestBody.username = existingData.username;
                        }
                        if (!profileImage && existingData.profile_image) {
                            requestBody.profile_image = existingData.profile_image;
                        }

                        // Update the user_id to the current login method
                        requestBody.user_id = userId;
                    }

                    console.log('🔍 XanoService.saveUserSettings - Request body:', {
                        ...requestBody,
                        profile_image: requestBody.profile_image ? `[Base64 data: ${requestBody.profile_image.length} chars]` : null,
                        keybinds: typeof requestBody.keybinds === 'object' ? '[Object]' : requestBody.keybinds
                    });

                    const response = await this.makeRequest(XANO_CONFIG.endpoints.saveUserSettings, {
                        method: 'POST',
                        body: JSON.stringify(requestBody)
                    });

                    console.log('🔍 XanoService.saveUserSettings - Response:', {
                        ...response,
                        profile_image: response.profile_image ? `[Base64 data: ${response.profile_image.length} chars]` : null
                    });

                    console.log('🔍 XanoService.saveUserSettings - Response fields:', Object.keys(response));
                    console.log('🔍 XanoService.saveUserSettings - Username in response:', response.username);
                    console.log('🔍 XanoService.saveUserSettings - Profile image in response:', !!response.profile_image);

                    return response;
                } catch (error) {
                    console.error('Failed to save user settings to Xano:', error);
                    throw error;
                }
            }

            static async checkUsernameAvailability(username) {
                try {
                    console.log('🔍 Checking username availability:', username);
                    
                    const url = `${XANO_CONFIG.baseURL}${XANO_CONFIG.endpoints.checkUsernameAvailability}?username=${encodeURIComponent(username)}`;
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        // If endpoint doesn't exist yet, fall back to checking existing users
                        if (response.status === 404) {
                            console.log('⚠️ Username availability endpoint not found, using fallback method');
                            return await this.checkUsernameAvailabilityFallback(username);
                        }
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();
                    console.log('✅ Username availability check result:', result);
                    
                    // Parse Xano response format:
                    // [] = username available
                    // [{username: "name"}] = username taken
                    const isAvailable = Array.isArray(result) && result.length === 0;
                    
                    return {
                        available: isAvailable,
                        message: isAvailable ? 'Username is available' : 'Username already taken'
                    };
                } catch (error) {
                    console.error('Failed to check username availability:', error);
                    // Fall back to checking existing users if API fails
                    return await this.checkUsernameAvailabilityFallback(username);
                }
            }

            static async checkUsernameAvailabilityFallback(username) {
                try {
                    console.log('🔄 Using fallback method to check username availability');
                    
                    // Search for existing users with this username
                    const url = `${XANO_CONFIG.baseURL}${XANO_CONFIG.endpoints.getUserSettings}?username=${encodeURIComponent(username)}`;
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        // If we get a result, username is taken
                        const isTaken = result && (Array.isArray(result) ? result.length > 0 : result.username === username);
                        
                        return {
                            available: !isTaken,
                            message: isTaken ? 'Username already taken' : 'Username is available'
                        };
                    } else {
                        // If no result found, username is available
                        return {
                            available: true,
                            message: 'Username is available'
                        };
                    }
                } catch (error) {
                    console.error('Fallback username check failed:', error);
                    // If all else fails, assume available but warn user
                    return {
                        available: true,
                        message: 'Unable to verify username availability. Please try a unique username.',
                        warning: true
                    };
                }
            }

        }

        // Auth0 Configuration and Functions
        let auth0Client = null;
        let isAuthenticated = false;
        let user = null;
        let userSettingsId = null; // Store the Xano settings record ID
        let currentUserData = null; // Store complete user data from Xano

        // Initialize Auth0
        async function initAuth0() {
            try {
                console.log('Initializing Auth0...');
                auth0Client = await auth0.createAuth0Client({
                    domain: 'dev-rikve7ck4yromzzs.us.auth0.com',
                    clientId: 'ekSg2O7lX2MmmKBJGoiW8OTNFFEg9fWK',
                    authorizationParams: {
                        redirect_uri: window.location.origin + window.location.pathname,
                        scope: 'openid profile email', // Request email scope
                        audience: `https://dev-rikve7ck4yromzzs.us.auth0.com/api/v2/` // Add Management API audience
                    },
                    cacheLocation: 'localstorage', // Use localStorage for session persistence
                    useRefreshTokens: true // Enable refresh tokens for better session management
                });

                // Handle redirect callback first if present
                if (window.location.search.includes('code=') || window.location.search.includes('state=')) {
                    console.log('Handling Auth0 redirect callback...');
                    await auth0Client.handleRedirectCallback();
                    // Clean up URL
                    window.history.replaceState({}, document.title, window.location.pathname);
                }

                // Check if user is authenticated
                isAuthenticated = await auth0Client.isAuthenticated();
                console.log('Auth0 authentication status:', isAuthenticated);

                if (isAuthenticated) {
                    // Try to get fresh user info with token
                    try {
                        const token = await auth0Client.getTokenSilently();
                        user = await auth0Client.getUser();
                        console.log('Auth0 user object:', user); // Debug: Check what user data we get
                        console.log('User email:', user.email); // Debug: Check email specifically
                        console.log('Available user fields:', Object.keys(user)); // Debug: See all available fields
                        console.log('User authenticated successfully');
                    } catch (tokenError) {
                        console.log('Token error, using basic user info:', tokenError);
                        user = await auth0Client.getUser();
                    }
                    // Load user settings from Xano after authentication
                    await loadUserSettingsFromXano();
                } else {
                    console.log('User not authenticated');
                }

                // Always update UI after checking authentication state
                updateAuthButton();

            } catch (error) {
                console.error('Auth0 initialization error:', error);
                // Reset auth state on error
                isAuthenticated = false;
                user = null;
                // Ensure UI is updated even if auth fails
                updateAuthButton();
            }
        }

        // Update auth UI based on authentication state
        function updateAuthButton() {
            const authButton = document.getElementById('authButton');
            const settingsButton = document.getElementById('settingsButton');
            const profileDropdown = document.getElementById('profileDropdown');

            if (isAuthenticated && user) {
                // Check if email is verified for database users
                if (user.email_verified === false && user.sub.startsWith('auth0|')) {
                    // Show verify button, hide others
                    authButton.style.display = 'block';
                    authButton.textContent = 'Verify';
                    authButton.className = 'auth-button verify';
                    settingsButton.style.display = 'none';
                    profileDropdown.style.display = 'none';
                    showEmailVerificationMessage();
                } else {
                    // Show profile dropdown, hide others
                    authButton.style.display = 'none';
                    settingsButton.style.display = 'none';
                    profileDropdown.style.display = 'block';
                    hideEmailVerificationMessage();
                    updateProfileDropdown();
                }
            } else {
                // Show login and settings buttons, hide profile dropdown
                authButton.style.display = 'block';
                authButton.textContent = 'Login';
                authButton.className = 'auth-button login';
                settingsButton.style.display = 'block';
                profileDropdown.style.display = 'none';
                hideEmailVerificationMessage();
            }
        }

        // Update profile dropdown with user data
        function updateProfileDropdown() {
            const profileImage = document.getElementById('profileImage');

            console.log('🔍 updateProfileDropdown - currentUserData:', {
                hasData: !!currentUserData,
                hasProfileImage: !!(currentUserData && currentUserData.profile_image),
                username: currentUserData ? currentUserData.username : 'none'
            });

            // Set default profile image or user's custom image
            if (currentUserData && currentUserData.profile_image) {
                console.log('🔍 Setting profile image from currentUserData');
                profileImage.src = currentUserData.profile_image;
            } else {
                console.log('🔍 Setting default profile image');
                // Set default blank profile image
                profileImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFMEUwRTAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K';
            }
        }

        // Generate random username
        function generateRandomUsername() {
            const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
            let result = 'user_';
            for (let i = 0; i < 8; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // Profile dropdown event handlers
        function setupProfileDropdownEvents() {
            const profileAvatar = document.getElementById('profileAvatar');
            const profileMenu = document.getElementById('profileMenu');
            const userSettingsMenuItem = document.getElementById('userSettingsMenuItem');
            const savedArtMenuItem = document.getElementById('savedArtMenuItem');
            const logoutMenuItem = document.getElementById('logoutMenuItem');

            // Toggle dropdown menu
            profileAvatar.addEventListener('click', (e) => {
                e.stopPropagation();
                profileMenu.style.display = profileMenu.style.display === 'block' ? 'none' : 'block';
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                profileMenu.style.display = 'none';
            });

            // Menu item handlers
            userSettingsMenuItem.addEventListener('click', () => {
                profileMenu.style.display = 'none';
                showUserSettingsModal();
            });

            savedArtMenuItem.addEventListener('click', () => {
                profileMenu.style.display = 'none';
                // TODO: Implement saved art functionality
                alert('Saved Art functionality coming soon!');
            });

            logoutMenuItem.addEventListener('click', () => {
                profileMenu.style.display = 'none';
                logout();
            });
        }

        // Show email verification message
        function showEmailVerificationMessage() {
            let messageDiv = document.getElementById('emailVerificationMessage');
            if (!messageDiv) {
                messageDiv = document.createElement('div');
                messageDiv.id = 'emailVerificationMessage';
                messageDiv.className = 'email-verification-message';
                messageDiv.innerHTML = `
                    <p><strong>Email Verification Required</strong></p>
                    <p>Please check your email and click the verification link to complete your registration.</p>
                    <div class="verification-buttons">
                        <button id="resendVerificationBtn" class="resend-verification-btn">Resend Verification Email</button>
                        <button id="logoutFromVerificationBtn" class="logout-verification-btn">Logout</button>
                    </div>
                `;
                document.getElementById('rightToolbar').appendChild(messageDiv);

                // Add event listener for resend button
                document.getElementById('resendVerificationBtn').addEventListener('click', resendVerificationEmail);

                // Add event listener for logout button
                document.getElementById('logoutFromVerificationBtn').addEventListener('click', logout);
            }
            messageDiv.style.display = 'block';
        }

        // Hide email verification message
        function hideEmailVerificationMessage() {
            const messageDiv = document.getElementById('emailVerificationMessage');
            if (messageDiv) {
                messageDiv.style.display = 'none';
            }
        }

        // Show success notification
        function showSuccessNotification(message) {
            // Remove any existing success notification
            const existingNotification = document.getElementById('successNotification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // Create notification element
            const notification = document.createElement('div');
            notification.id = 'successNotification';
            notification.style.cssText = `
                position: fixed;
                top: 50px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 10001;
                background: #28a745;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                opacity: 0;
                transition: opacity 0.3s ease-in-out;
                pointer-events: none;
                max-width: 400px;
                text-align: center;
            `;
            notification.textContent = message;

            // Add to document
            document.body.appendChild(notification);

            // Trigger fade-in animation
            setTimeout(() => {
                notification.style.opacity = '1';
            }, 10);

            // Auto-remove after 3 seconds with fade-out
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);
        }

        // Resend verification email
        async function resendVerificationEmail() {
            try {
                // For client-side applications, we need to redirect to Auth0 to resend verification
                // This is the recommended approach for SPA applications
                const returnUrl = encodeURIComponent(window.location.origin);
                const resendUrl = `https://dev-rikve7ck4yromzzs.us.auth0.com/u/email-verification?email=${encodeURIComponent(user.email)}&client_id=ekSg2O7lX2MmmKBJGoiW8OTNFFEg9fWK&returnUrl=${returnUrl}`;

                // Open in a new tab/window so user doesn't lose their current session
                const newWindow = window.open(resendUrl, '_blank', 'width=500,height=600');

                if (newWindow) {
                    alert('A new window has opened for email verification. Please check your email after completing the process.');
                } else {
                    // Fallback if popup is blocked
                    if (confirm('Please check your email for the verification link. If you didn\'t receive it, would you like to open the verification page?')) {
                        window.location.href = resendUrl;
                    }
                }
            } catch (error) {
                console.error('Error with verification process:', error);
                alert('Error with verification process. Please try logging out and back in, or contact support.');
            }
        }

        // Login function
        async function login() {
            try {
                await auth0Client.loginWithRedirect();
            } catch (error) {
                console.error('Login error:', error);
            }
        }

        // Logout function
        async function logout() {
            try {
                await auth0Client.logout({
                    logoutParams: {
                        returnTo: window.location.origin
                    }
                });
            } catch (error) {
                console.error('Logout error:', error);
            }
        }

        // Auth button click handler
        function handleAuthClick() {
            if (isAuthenticated) {
                // Check if user needs email verification
                if (user.email_verified === false && user.sub.startsWith('auth0|')) {
                    // User is logged in but email not verified - show verification options
                    const action = confirm('Your email is not verified. Would you like to resend the verification email?');
                    if (action) {
                        resendVerificationEmail();
                    }
                } else {
                    // User is fully authenticated - logout
                    logout();
                }
            } else {
                // User not logged in - login
                login();
            }
        }

        // User Settings Sync Functions
        async function loadUserSettingsFromXano() {
            if (!isAuthenticated || !user) {
                console.log('User not authenticated, skipping Xano settings load');
                return;
            }

            try {
                console.log('Loading user settings from Xano...');
                updateSyncStatus('syncing', 'Loading settings from cloud...');

                // Use email as primary identifier to link accounts across auth methods
                const userEmail = user.email || user.sub;
                console.log('Looking up settings by email:', userEmail);
                const xanoSettings = await XanoService.getUserSettings(userEmail);

                if (xanoSettings) {
                    // Handle nested response structure from Xano
                    const settingsData = xanoSettings.user_settings || xanoSettings;
                    userSettingsId = settingsData.id;
                    currentUserData = settingsData; // Store complete user data

                    console.log('Loaded settings from Xano:', settingsData);
                    console.log('🔍 Loaded username:', settingsData.username);
                    console.log('🔍 Loaded profile image:', !!settingsData.profile_image);

                    // Apply theme from Xano
                    if (settingsData.theme) {
                        applyTheme(settingsData.theme, true); // Skip auto-save to prevent overwriting
                        document.getElementById('themeToggle').value = settingsData.theme;
                    }

                    // Apply keybinds from Xano
                    if (settingsData.keybinds) {
                        currentKeybinds = typeof settingsData.keybinds === 'string'
                            ? JSON.parse(settingsData.keybinds)
                            : settingsData.keybinds;
                        updateDropdownText();
                        // Also save to localStorage to keep in sync
                        localStorage.setItem('pixelart-keybinds', JSON.stringify(currentKeybinds));
                    }

                    // Apply brush performance settings from Xano
                    if (settingsData.brush_performance_mode !== undefined) {
                        brushPerformanceMode = settingsData.brush_performance_mode;
                        document.getElementById('brushPerformanceToggle').value = brushPerformanceMode.toString();
                        localStorage.setItem('pixelart-brush-performance', brushPerformanceMode.toString());
                    }

                    if (settingsData.show_canvas_size_warning !== undefined) {
                        showCanvasSizeWarning = settingsData.show_canvas_size_warning;
                        localStorage.setItem('pixelart-show-canvas-warning', showCanvasSizeWarning.toString());
                    }

                    // Apply image import settings from Xano
                    if (settingsData.image_import_mode !== undefined) {
                        imageImportMode = settingsData.image_import_mode;
                        localStorage.setItem('pixelart-image-import-mode', imageImportMode);
                    }

                    if (settingsData.show_image_import_warning !== undefined) {
                        showImageImportWarning = settingsData.show_image_import_warning;
                        localStorage.setItem('pixelart-show-image-import-warning', showImageImportWarning.toString());
                    }

                    console.log('Successfully applied settings from Xano');
                    updateSyncStatus(); // Let function determine status based on verification
                } else {
                    console.log('No settings found in Xano, creating new user profile');
                    // If no settings in Xano, create new user profile with default username
                    await createNewUserProfile();
                }
            } catch (error) {
                console.error('Failed to load settings from Xano:', error);
                console.log('Falling back to local settings');
                updateSyncStatus('error', 'Failed to load from cloud - using local settings');
            }
        }

        // Create new user profile with default username
        async function createNewUserProfile() {
            if (!isAuthenticated || !user) {
                return;
            }

            // Check if user email is verified
            const emailVerified = user.email_verified || false;
            if (!emailVerified) {
                console.log('User email not verified, skipping profile creation');
                updateSyncStatus(); // Show appropriate status for unverified user
                return;
            }

            try {
                updateSyncStatus('syncing', 'Creating user profile...');

                const localTheme = localStorage.getItem('pixelart-theme') || 'light';
                const localKeybinds = localStorage.getItem('pixelart-keybinds');
                const keybinds = localKeybinds ? JSON.parse(localKeybinds) : { ...defaultKeybinds };

                const settings = {
                    theme: localTheme,
                    keybinds: keybinds,
                    brushPerformanceMode: localStorage.getItem('pixelart-brush-performance') === 'true',
                    showCanvasSizeWarning: localStorage.getItem('pixelart-show-canvas-warning') !== 'false',
                    imageImportMode: localStorage.getItem('pixelart-image-import-mode') || 'fit-to-view',
                    showImageImportWarning: localStorage.getItem('pixelart-show-image-import-warning') !== 'false'
                };

                // Generate default username and prepare user data
                const defaultUsername = generateRandomUsername();
                const userEmail = user.email || user.sub;
                const emailVerified = user.email_verified || false;

                console.log('Creating new user profile with username:', defaultUsername);
                const response = await XanoService.saveUserSettings(
                    user.sub,
                    settings,
                    userEmail,
                    emailVerified,
                    defaultUsername,
                    null // No profile image initially
                );

                // Handle nested response structure from Xano
                const responseData = response.user_settings || response;
                userSettingsId = responseData.id;
                currentUserData = responseData; // Store the complete user data
                console.log('✅ createNewUserProfile - Updated currentUserData:', {
                    id: currentUserData.id,
                    username: currentUserData.username,
                    hasProfileImage: !!currentUserData.profile_image,
                    allKeys: Object.keys(currentUserData)
                });
                console.log('Successfully created user profile');
                updateSyncStatus(); // Let function determine status based on verification
            } catch (error) {
                console.error('Failed to create user profile:', error);
                updateSyncStatus('error', 'Failed to create profile - using local settings');
            }
        }

        async function syncLocalSettingsToXano() {
            if (!isAuthenticated || !user) {
                return;
            }

            // Check if user email is verified
            const emailVerified = user.email_verified || false;
            if (!emailVerified) {
                console.log('User email not verified, skipping initial cloud sync');
                updateSyncStatus(); // Show appropriate status for unverified user
                return;
            }

            try {
                updateSyncStatus('syncing', 'Syncing local settings to cloud...');

                const localTheme = localStorage.getItem('pixelart-theme') || 'light';
                const localKeybinds = localStorage.getItem('pixelart-keybinds');
                const keybinds = localKeybinds ? JSON.parse(localKeybinds) : { ...defaultKeybinds };

                const settings = {
                    theme: localTheme,
                    keybinds: keybinds,
                    brushPerformanceMode: localStorage.getItem('pixelart-brush-performance') === 'true',
                    showCanvasSizeWarning: localStorage.getItem('pixelart-show-canvas-warning') !== 'false',
                    imageImportMode: localStorage.getItem('pixelart-image-import-mode') || 'fit-to-view',
                    showImageImportWarning: localStorage.getItem('pixelart-show-image-import-warning') !== 'false'
                };

                console.log('Syncing local settings to Xano:', settings);
                const userEmail = user.email || user.email_verified || user.name || 'No email available';
                const emailVerified = user.email_verified || false;

                // Preserve existing username and profile image from currentUserData
                const existingUsername = currentUserData ? currentUserData.username : null;
                const existingProfileImage = currentUserData ? currentUserData.profile_image : null;

                const response = await XanoService.saveUserSettings(
                    user.sub,
                    settings,
                    userEmail,
                    emailVerified,
                    existingUsername,
                    existingProfileImage
                );
                // Handle nested response structure from Xano
                const responseData = response.user_settings || response;
                userSettingsId = responseData.id;
                currentUserData = responseData; // Update currentUserData with the response
                console.log('Successfully synced local settings to Xano');
                updateSyncStatus(); // Let function determine status based on verification
            } catch (error) {
                console.error('Failed to sync local settings to Xano:', error);
                updateSyncStatus('error', 'Failed to sync to cloud - using local settings');
            }
        }

        async function saveSettingsToXano(theme, keybinds) {
            if (!isAuthenticated || !user) {
                console.log('User not authenticated, skipping Xano save');
                return;
            }

            // Check if user email is verified
            const emailVerified = user.email_verified || false;
            if (!emailVerified) {
                console.log('User email not verified, skipping cloud sync');
                updateSyncStatus(); // Show appropriate status for unverified user
                return;
            }

            try {
                updateSyncStatus('syncing', 'Saving settings to cloud...');
                // Read current values from UI state and variables, not localStorage
                // This ensures we save the most up-to-date values
                const currentBrushPerformance = document.getElementById('brushPerformanceToggle') ?
                    document.getElementById('brushPerformanceToggle').value === 'true' :
                    (document.getElementById('basicBrushPerformanceToggle') ?
                        document.getElementById('basicBrushPerformanceToggle').value === 'true' :
                        brushPerformanceMode);

                const settings = {
                    theme,
                    keybinds,
                    brushPerformanceMode: currentBrushPerformance,
                    showCanvasSizeWarning: showCanvasSizeWarning,
                    imageImportMode: imageImportMode,
                    showImageImportWarning: showImageImportWarning
                };

                // Your Xano POST function handles both create and update automatically
                // Include user email for better record keeping (try multiple email fields)
                const userEmail = user.email ||
                                user['https://pixelartnexus.com/email'] || // Custom claim
                                user.name ||
                                user.nickname ||
                                user.given_name ||
                                user.sub || // Use user ID as fallback
                                'No email available';

                // Get email verification status from Auth0 (server-controlled, not user-modifiable)
                // This comes directly from Auth0 and cannot be tampered with by users
                const emailVerified = user.email_verified || false;

                // Preserve existing username and profile image from currentUserData
                const existingUsername = currentUserData ? currentUserData.username : null;
                const existingProfileImage = currentUserData ? currentUserData.profile_image : null;

                console.log('Full user object when saving:', user); // Debug: Full user object
                console.log('Saving with email:', userEmail); // Debug: Check what email we're sending
                console.log('User sub (Auth0 ID):', user.sub); // Debug: Check Auth0 ID
                console.log('Email verified status:', emailVerified); // Debug: Check verification status
                console.log('Preserving existing username:', existingUsername); // Debug: Check preserved username
                console.log('Preserving existing profile image:', !!existingProfileImage); // Debug: Check preserved image

                const response = await XanoService.saveUserSettings(
                    user.sub,
                    settings,
                    userEmail,
                    emailVerified,
                    existingUsername, // Preserve existing username
                    existingProfileImage // Preserve existing profile image
                );
                // Handle nested response structure from Xano
                const responseData = response.user_settings || response;
                userSettingsId = responseData.id;
                currentUserData = responseData; // Update currentUserData with the response
                console.log('✅ saveSettingsToXano - Updated currentUserData:', {
                    id: currentUserData.id,
                    username: currentUserData.username,
                    hasProfileImage: !!currentUserData.profile_image,
                    allKeys: Object.keys(currentUserData)
                });
                console.log('Saved settings to Xano');
                updateSyncStatus(); // Let function determine status based on verification
            } catch (error) {
                console.error('Failed to save settings to Xano:', error);
                updateSyncStatus('error', 'Failed to save to cloud - saved locally');
                // Don't throw error - let local storage still work
            }
        }

        // Settings functionality
        let currentKeybinds = {};
        let defaultKeybinds = {
            'Draw Tool': 'd',
            'Pan Tool': 'p',
            'Eyedropper Tool': 't',
            'Selection Tool': 's',
            'Brush Size +': '=',
            'Brush Size -': '-',
            'Auto Pan': 'rightclick'
        };

        // Basic Settings Modal Functions (for non-authenticated users)
        function showBasicSettingsModal() {
            const modal = document.getElementById('basicSettingsModal');
            loadBasicSettingsData();
            populateBasicKeybindList();

            // Store original settings for change detection
            storeOriginalBasicSettings();

            modal.style.display = 'flex';
        }

        // Store original basic settings for change detection
        function storeOriginalBasicSettings() {
            originalBasicSettings = {
                theme: localStorage.getItem('pixelart-theme') || 'light',
                keybinds: JSON.parse(JSON.stringify(currentKeybinds)),
                brushPerformanceMode: brushPerformanceMode,
                imageImportMode: imageImportMode,
                showImageImportWarning: showImageImportWarning
            };
        }

        // Check if basic settings have changed
        function hasBasicSettingsChanged() {
            if (!originalBasicSettings) return false;

            const currentTheme = document.getElementById('basicThemeToggle').value;
            const currentThemeStored = localStorage.getItem('pixelart-theme') || 'light';

            // Check if theme changed
            if (currentTheme !== originalBasicSettings.theme || currentThemeStored !== originalBasicSettings.theme) {
                return true;
            }

            // Check if keybinds changed
            if (JSON.stringify(currentKeybinds) !== JSON.stringify(originalBasicSettings.keybinds)) {
                return true;
            }

            // Check if brush performance mode changed
            if (brushPerformanceMode !== originalBasicSettings.brushPerformanceMode) {
                return true;
            }

            // Check if image import settings changed
            if (imageImportMode !== originalBasicSettings.imageImportMode) {
                return true;
            }

            if (showImageImportWarning !== originalBasicSettings.showImageImportWarning) {
                return true;
            }

            return false;
        }

        function loadBasicSettingsData() {
            // Load theme
            const savedTheme = localStorage.getItem('pixelart-theme') || 'light';
            document.getElementById('basicThemeToggle').value = savedTheme;

            // Load brush performance mode
            const savedBrushPerformance = localStorage.getItem('pixelart-brush-performance') === 'true';
            document.getElementById('basicBrushPerformanceToggle').value = savedBrushPerformance.toString();
            brushPerformanceMode = savedBrushPerformance;

            // Load canvas size warning setting
            const savedShowWarning = localStorage.getItem('pixelart-show-canvas-warning') !== 'false';
            showCanvasSizeWarning = savedShowWarning;

            // Load image import settings
            const savedImageImportMode = localStorage.getItem('pixelart-image-import-mode') || 'fit-to-view';
            imageImportMode = savedImageImportMode;
            document.getElementById('basicImageImportModeToggle').value = savedImageImportMode;

            const savedShowImageWarning = localStorage.getItem('pixelart-show-image-import-warning') !== 'false';
            showImageImportWarning = savedShowImageWarning;

            // Load keybinds
            const savedKeybinds = localStorage.getItem('pixelart-keybinds');
            if (savedKeybinds) {
                currentKeybinds = JSON.parse(savedKeybinds);
            } else {
                currentKeybinds = { ...defaultKeybinds };
            }
        }

        function populateBasicKeybindList() {
            const keybindList = document.getElementById('basicKeybindList');
            keybindList.innerHTML = '';

            Object.entries(currentKeybinds).forEach(([action, key]) => {
                const keybindItem = document.createElement('div');
                keybindItem.className = 'keybind-item';
                keybindItem.innerHTML = `
                    <span class="keybind-label">${action}:</span>
                    <input type="text" class="keybind-input" data-action="${action}" value="${key}" readonly>
                    <button type="button" class="restore-keybind-btn" data-action="${action}" title="Restore ${action} to default">↺</button>
                `;
                keybindList.appendChild(keybindItem);
            });

            // Add event listeners to keybind inputs
            document.querySelectorAll('#basicKeybindList .keybind-input').forEach(input => {
                input.addEventListener('focus', handleKeybindInputFocus);
                input.addEventListener('blur', handleKeybindInputBlur);
                input.addEventListener('keydown', handleKeybindKeydown);
                input.addEventListener('mousedown', handleKeybindMousedown);
                input.addEventListener('contextmenu', handleKeybindContextMenu);
            });

            // Add event listeners to restore buttons
            document.querySelectorAll('#basicKeybindList .restore-keybind-btn').forEach(button => {
                button.addEventListener('click', handleRestoreKeybind);
            });
        }

        function saveBasicKeybinds() {
            if (checkForDuplicateKeybinds()) {
                alert('Please fix duplicate keybinds before saving.');
                return;
            }

            localStorage.setItem('pixelart-keybinds', JSON.stringify(currentKeybinds));
            updateDropdownText();
            showSuccessNotification('Keybinds saved locally! Login to sync across devices.');
        }

        function revertBasicKeybinds() {
            if (confirm('Are you sure you want to revert all keybinds to default? This cannot be undone.')) {
                currentKeybinds = { ...defaultKeybinds };
                populateBasicKeybindList();
                localStorage.setItem('pixelart-keybinds', JSON.stringify(currentKeybinds));
                updateDropdownText();
                showSuccessNotification('Keybinds reverted to default.');
            }
        }

        function hideBasicSettingsModal() {
            // Check if settings have changed and auto-save if needed
            if (hasBasicSettingsChanged()) {
                // Apply theme change if it was changed
                const currentTheme = document.getElementById('basicThemeToggle').value;
                applyTheme(currentTheme);
                localStorage.setItem('pixelart-theme', currentTheme);

                // Save keybinds
                localStorage.setItem('pixelart-keybinds', JSON.stringify(currentKeybinds));
                updateDropdownText();

                // Save brush performance mode
                localStorage.setItem('pixelart-brush-performance', brushPerformanceMode.toString());

                // Save image import settings
                localStorage.setItem('pixelart-image-import-mode', imageImportMode);
                localStorage.setItem('pixelart-show-image-import-warning', showImageImportWarning.toString());

                showSuccessNotification('Settings saved successfully!');
            }

            const modal = document.getElementById('basicSettingsModal');
            modal.style.display = 'none';
        }

        // Store original settings when modal opens for change detection
        let originalUserSettings = null;
        let originalBasicSettings = null;

        // User Settings Modal Functions
        function showUserSettingsModal() {
            // Security check: Only allow authenticated and verified users
            if (!isAuthenticated || !user || !user.email_verified) {
                alert('You must be logged in and have a verified email to access user settings.');
                return;
            }

            const modal = document.getElementById('userSettingsModal');
            loadUserSettingsData();
            populateKeybindList();
            updateSyncStatus();
            updateVerificationStatus();

            // Store original settings for change detection
            storeOriginalUserSettings();

            modal.style.display = 'flex';
        }

        // Store original settings when modal opens
        function storeOriginalUserSettings() {
            originalUserSettings = {
                username: document.getElementById('usernameInput').value.trim(),
                theme: document.getElementById('themeToggle').value,
                brushPerformanceMode: document.getElementById('brushPerformanceToggle').value === 'true',
                imageImportMode: document.getElementById('imageImportModeToggle').value,
                showImageImportWarning: showImageImportWarning,
                keybinds: JSON.stringify(currentKeybinds),
                profileImageChanged: false // Will be set to true if user selects a new image
            };
        }

        // Check if user settings have changed
        function hasUserSettingsChanged() {
            if (!originalUserSettings) return false;

            const currentSettings = {
                username: document.getElementById('usernameInput').value.trim(),
                theme: document.getElementById('themeToggle').value,
                brushPerformanceMode: document.getElementById('brushPerformanceToggle').value === 'true',
                imageImportMode: document.getElementById('imageImportModeToggle').value,
                showImageImportWarning: showImageImportWarning,
                keybinds: JSON.stringify(currentKeybinds),
                profileImageChanged: originalUserSettings.profileImageChanged
            };

            return (
                currentSettings.username !== originalUserSettings.username ||
                currentSettings.theme !== originalUserSettings.theme ||
                currentSettings.brushPerformanceMode !== originalUserSettings.brushPerformanceMode ||
                currentSettings.imageImportMode !== originalUserSettings.imageImportMode ||
                currentSettings.showImageImportWarning !== originalUserSettings.showImageImportWarning ||
                currentSettings.keybinds !== originalUserSettings.keybinds ||
                currentSettings.profileImageChanged
            );
        }

        // Load user settings data into the modal
        function loadUserSettingsData() {
            console.log('🔍 loadUserSettingsData - currentUserData:', currentUserData);
            console.log('🔍 currentUserData details:', {
                exists: !!currentUserData,
                hasUsername: !!(currentUserData && currentUserData.username),
                username: currentUserData ? currentUserData.username : 'none',
                hasProfileImage: !!(currentUserData && currentUserData.profile_image),
                allKeys: currentUserData ? Object.keys(currentUserData) : []
            });

            // Load username
            const usernameInput = document.getElementById('usernameInput');
            if (currentUserData && currentUserData.username) {
                console.log('🔍 Using existing username:', currentUserData.username);
                usernameInput.value = currentUserData.username;
            } else {
                console.log('🔍 No username found in currentUserData, leaving input empty');
                usernameInput.value = ''; // Leave empty instead of generating random
            }

            // Load profile image
            const profilePreview = document.getElementById('profilePreview');
            if (currentUserData && currentUserData.profile_image) {
                console.log('🔍 Loading existing profile image, length:', currentUserData.profile_image.length);
                profilePreview.src = currentUserData.profile_image;
            } else {
                console.log('🔍 No profile image found, using default');
                profilePreview.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFMEUwRTAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K';
            }

            // Load theme
            const savedTheme = localStorage.getItem('pixelart-theme') || 'light';
            document.getElementById('themeToggle').value = savedTheme;
            applyTheme(savedTheme, true); // Skip Xano save to prevent overwriting username/profile image

            // Load brush performance mode
            const savedBrushPerformance = localStorage.getItem('pixelart-brush-performance') === 'true';
            document.getElementById('brushPerformanceToggle').value = savedBrushPerformance.toString();
            brushPerformanceMode = savedBrushPerformance;

            // Load canvas size warning setting
            const savedShowWarning = localStorage.getItem('pixelart-show-canvas-warning') !== 'false';
            showCanvasSizeWarning = savedShowWarning;

            // Load image import settings
            const savedImageImportMode = localStorage.getItem('pixelart-image-import-mode') || 'fit-to-view';
            imageImportMode = savedImageImportMode;
            document.getElementById('imageImportModeToggle').value = savedImageImportMode;

            const savedShowImageWarning = localStorage.getItem('pixelart-show-image-import-warning') !== 'false';
            showImageImportWarning = savedShowImageWarning;

            // Load keybinds
            const savedKeybinds = localStorage.getItem('pixelart-keybinds');
            if (savedKeybinds) {
                currentKeybinds = JSON.parse(savedKeybinds);
            } else {
                currentKeybinds = { ...defaultKeybinds };
            }

            // Update dropdown text with loaded keybinds
            updateDropdownText();
        }

        // Update sync status indicator
        function updateSyncStatus(status = null, message = null) {
            const syncStatusDiv = document.getElementById('syncStatus');
            const syncStatusText = document.getElementById('syncStatusText');
            const syncStatusIcon = document.getElementById('syncStatusIcon');

            if (!isAuthenticated || !user) {
                syncStatusDiv.style.display = 'none';
                return;
            }

            // Check if user email is verified
            const emailVerified = user.email_verified || false;

            syncStatusDiv.style.display = 'flex';

            // Remove all status classes
            syncStatusDiv.className = 'sync-status';

            if (status === 'syncing') {
                syncStatusDiv.classList.add('syncing');
                syncStatusText.textContent = message || 'Syncing settings...';
                syncStatusIcon.textContent = '⟳';
            } else if (status === 'error') {
                syncStatusDiv.classList.add('error');
                syncStatusText.textContent = message || 'Sync failed - using local settings';
                syncStatusIcon.textContent = '⚠';
            } else if (status === 'offline') {
                syncStatusDiv.classList.add('offline');
                syncStatusText.textContent = message || 'Offline - using local settings';
                syncStatusIcon.textContent = '⚡';
            } else {
                // Only show "synced" status for verified users
                if (emailVerified) {
                    syncStatusDiv.classList.add('synced');
                    syncStatusText.textContent = message || 'Settings synced to cloud';
                    syncStatusIcon.textContent = '✓';
                } else {
                    // Show offline status for unverified users
                    syncStatusDiv.classList.add('offline');
                    syncStatusText.textContent = 'Settings saved locally only';
                    syncStatusIcon.textContent = '💾';
                }
            }
        }

        // Update email verification status indicator
        function updateVerificationStatus() {
            const verificationStatusDiv = document.getElementById('emailVerificationStatus');
            const verificationStatusText = document.getElementById('verificationStatusText');
            const verificationStatusIcon = document.getElementById('verificationStatusIcon');

            if (!isAuthenticated || !user) {
                verificationStatusDiv.style.display = 'none';
                return;
            }

            verificationStatusDiv.style.display = 'flex';

            const isVerified = user.email_verified || false;
            const userEmail = user.email || 'No email';

            if (isVerified) {
                verificationStatusText.textContent = `✓ Email verified: ${userEmail} - Cloud sync enabled`;
                verificationStatusDiv.className = 'verification-status verified';
                verificationStatusIcon.textContent = '✓';
            } else {
                verificationStatusText.textContent = `⚠ Email not verified: ${userEmail} - Cloud sync disabled`;
                verificationStatusDiv.className = 'verification-status unverified';
                verificationStatusIcon.textContent = '⚠';
            }
        }

        // Hide settings modal
        function hideSettingsModal() {
            const modal = document.getElementById('settingsModal');
            modal.style.display = 'none';
        }

        // Load current settings from localStorage (for initial app load)
        function loadCurrentSettings() {
            // Load theme
            const savedTheme = localStorage.getItem('pixelart-theme') || 'light';
            applyTheme(savedTheme);

            // Load keybinds
            const savedKeybinds = localStorage.getItem('pixelart-keybinds');
            if (savedKeybinds) {
                currentKeybinds = JSON.parse(savedKeybinds);
            } else {
                currentKeybinds = { ...defaultKeybinds };
            }

            // Update dropdown text with loaded keybinds
            updateDropdownText();
        }

        // Apply theme
        function applyTheme(theme, skipXanoSave = false) {
            if (theme === 'dark') {
                document.body.classList.add('dark-theme');
            } else {
                document.body.classList.remove('dark-theme');
            }
            localStorage.setItem('pixelart-theme', theme);

            // Save to Xano if user is authenticated and verified (unless explicitly skipped)
            if (!skipXanoSave && isAuthenticated && user) {
                const emailVerified = user.email_verified || false;
                if (emailVerified) {
                    saveSettingsToXano(theme, currentKeybinds);
                } else {
                    console.log('Theme saved locally - email verification required for cloud sync');
                }
            }
        }

        // Populate keybind list
        function populateKeybindList() {
            const keybindList = document.getElementById('keybindList');
            keybindList.innerHTML = '';

            Object.entries(currentKeybinds).forEach(([action, key]) => {
                const keybindItem = document.createElement('div');
                keybindItem.className = 'keybind-item';
                keybindItem.innerHTML = `
                    <span class="keybind-label">${action}:</span>
                    <input type="text" class="keybind-input" data-action="${action}" value="${key}" readonly>
                    <button type="button" class="restore-keybind-btn" data-action="${action}" title="Restore ${action} to default">↺</button>
                `;
                keybindList.appendChild(keybindItem);
            });

            // Add event listeners to keybind inputs
            document.querySelectorAll('.keybind-input').forEach(input => {
                input.addEventListener('focus', handleKeybindInputFocus);
                input.addEventListener('blur', handleKeybindInputBlur);
                input.addEventListener('keydown', handleKeybindKeydown);
                input.addEventListener('mousedown', handleKeybindMousedown);
                input.addEventListener('contextmenu', handleKeybindContextMenu);
            });

            // Add event listeners to restore buttons
            document.querySelectorAll('.restore-keybind-btn').forEach(button => {
                button.addEventListener('click', handleRestoreKeybind);
            });
        }

        // Handle keybind input focus
        function handleKeybindInputFocus(event) {
            event.target.value = '';
            event.target.placeholder = 'Press a key...';
        }

        // Handle keybind input blur
        function handleKeybindInputBlur(event) {
            const action = event.target.dataset.action;
            if (!event.target.value) {
                event.target.value = currentKeybinds[action];
            }
            event.target.placeholder = '';
        }

        // Handle keybind keydown
        function handleKeybindKeydown(event) {
            event.preventDefault();
            const action = event.target.dataset.action;
            let keyValue = '';

            if (event.button === 2 || event.which === 3) {
                keyValue = 'rightclick';
            } else {
                keyValue = event.key.toLowerCase();
            }

            event.target.value = keyValue;
            currentKeybinds[action] = keyValue;
            event.target.blur();
            checkForDuplicateKeybinds();
        }

        // Handle keybind mousedown (for right-click and middle-click detection)
        function handleKeybindMousedown(event) {
            // Handle right-click (button 2) and middle-click (button 1)
            if (event.button === 2) {
                event.preventDefault();
                const action = event.target.dataset.action;

                event.target.value = 'rightclick';
                currentKeybinds[action] = 'rightclick';
                event.target.blur();
                checkForDuplicateKeybinds();
            } else if (event.button === 1) {
                event.preventDefault();
                const action = event.target.dataset.action;

                event.target.value = 'middleclick';
                currentKeybinds[action] = 'middleclick';
                event.target.blur();
                checkForDuplicateKeybinds();
            }
        }

        // Handle keybind context menu (prevent right-click menu)
        function handleKeybindContextMenu(event) {
            event.preventDefault();
        }

        // Handle restore individual keybind to default
        function handleRestoreKeybind(event) {
            const action = event.target.dataset.action;
            const defaultValue = defaultKeybinds[action];

            if (defaultValue) {
                currentKeybinds[action] = defaultValue;

                // Update the input field
                const input = document.querySelector(`input[data-action="${action}"]`);
                if (input) {
                    input.value = defaultValue;
                }

                checkForDuplicateKeybinds();
                showSuccessNotification(`${action} restored to default (${defaultValue})`);
            }
        }

        // Check for duplicate keybinds
        function checkForDuplicateKeybinds() {
            const inputs = document.querySelectorAll('.keybind-input');
            const values = {};
            let hasDuplicates = false;

            // Clear previous duplicate styling
            inputs.forEach(input => input.classList.remove('duplicate'));

            // Check for duplicates
            inputs.forEach(input => {
                const value = input.value;
                if (values[value]) {
                    values[value].push(input);
                } else {
                    values[value] = [input];
                }
            });

            // Mark duplicates
            Object.values(values).forEach(inputGroup => {
                if (inputGroup.length > 1) {
                    inputGroup.forEach(input => {
                        input.classList.add('duplicate');
                        hasDuplicates = true;
                    });
                }
            });

            return hasDuplicates;
        }

        // Save keybinds
        async function saveKeybinds() {
            if (checkForDuplicateKeybinds()) {
                alert('Please fix duplicate keybinds before saving.');
                return;
            }

            localStorage.setItem('pixelart-keybinds', JSON.stringify(currentKeybinds));
            updateDropdownText();

            // Save to Xano if user is authenticated and verified
            if (isAuthenticated && user) {
                const emailVerified = user.email_verified || false;
                if (emailVerified) {
                    try {
                        const currentTheme = localStorage.getItem('pixelart-theme') || 'light';
                        await saveSettingsToXano(currentTheme, currentKeybinds);
                        showSuccessNotification('Keybinds saved successfully and synced to cloud!');
                    } catch (error) {
                        showSuccessNotification('Keybinds saved locally, but cloud sync failed. Your settings will still be available on this device.');
                    }
                } else {
                    showSuccessNotification('Keybinds saved locally! Please verify your email to enable cloud sync.');
                }
            } else {
                showSuccessNotification('Keybinds saved successfully!');
            }

            // Note: This function is now part of the user settings modal
        }

        // Update dropdown text with current keybinds
        function updateDropdownText() {
            const toolSelector = document.getElementById('tool');
            if (!toolSelector) return;

            const options = toolSelector.querySelectorAll('option');
            options.forEach(option => {
                const value = option.value;
                let keybind = '';

                switch(value) {
                    case 'draw':
                        keybind = currentKeybinds['Draw Tool'] || 'd';
                        option.textContent = `Draw (${keybind.toUpperCase()})`;
                        break;
                    case 'pan':
                        keybind = currentKeybinds['Pan Tool'] || 'p';
                        option.textContent = `Pan (${keybind.toUpperCase()})`;
                        break;
                    case 'eyedropper':
                        keybind = currentKeybinds['Eyedropper Tool'] || 't';
                        option.textContent = `Eyedrop (${keybind.toUpperCase()})`;
                        break;
                    case 'select':
                        keybind = currentKeybinds['Selection Tool'] || 's';
                        option.textContent = `Select (${keybind.toUpperCase()})`;
                        break;
                }
            });
        }

        // Revert keybinds to default
        function revertKeybinds() {
            if (confirm('Are you sure you want to revert all keybinds to default? This cannot be undone.')) {
                currentKeybinds = { ...defaultKeybinds };
                populateKeybindList();
                localStorage.setItem('pixelart-keybinds', JSON.stringify(currentKeybinds));
                updateDropdownText();
                showSuccessNotification('Keybinds reverted to default.');
            }
        }

        // Save all user settings
        async function saveAllUserSettings() {
            if (!isAuthenticated || !user || !user.email_verified) {
                alert('You must be logged in and have a verified email to save settings.');
                return;
            }

            try {
                updateSyncStatus('syncing', 'Saving user settings...');

                // Get form data
                const username = document.getElementById('usernameInput').value.trim();
                const profileImageInput = document.getElementById('profileImageInput');
                const theme = document.getElementById('themeToggle').value;

                // Validate username using security utils
                if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                    const validation = SecurityUtils.InputValidator.validateUsername(username);
                    if (!validation.valid) {
                        alert(validation.error);
                        updateSyncStatus('error', 'Failed to save settings');
                        return;
                    }
                } else {
                    // Fallback validation if security utils not available
                    if (!username) {
                        alert('Username cannot be empty.');
                        updateSyncStatus('error', 'Failed to save settings');
                        return;
                    }
                    if (username.length > 50) {
                        alert('Username cannot exceed 50 characters.');
                        updateSyncStatus('error', 'Failed to save settings');
                        return;
                    }
                }

                // Check if username has changed and if so, verify availability
                const currentUsername = currentUserData ? currentUserData.username : null;
                if (username !== currentUsername) {
                    updateSyncStatus('syncing', 'Checking username availability...');
                    
                    const availabilityCheck = await XanoService.checkUsernameAvailability(username);
                    if (!availabilityCheck.available) {
                        alert(availabilityCheck.message);
                        updateSyncStatus('error', 'Username not available');
                        return;
                    }
                    
                    if (availabilityCheck.warning) {
                        const proceed = confirm(availabilityCheck.message + '\n\nDo you want to proceed anyway?');
                        if (!proceed) {
                            updateSyncStatus('idle', 'Save cancelled');
                            return;
                        }
                    }
                }

                // Handle profile image upload
                let profileImageData = null;
                if (profileImageInput.files && profileImageInput.files[0]) {
                    const file = profileImageInput.files[0];

                    // Validate file before converting
                    if (!validateProfileImage(file)) {
                        alert('Invalid profile image. Please select a valid PNG or JPG file.');
                        return;
                    }

                    profileImageData = await convertImageToBase64(file);
                    console.log('Profile image converted to base64, length:', profileImageData.length);
                } else if (currentUserData && currentUserData.profile_image) {
                    // Keep existing profile image if no new one uploaded
                    profileImageData = currentUserData.profile_image;
                    console.log('Keeping existing profile image');
                }

                // Prepare settings data - include all user preferences
                const settings = {
                    theme,
                    keybinds: currentKeybinds,
                    brushPerformanceMode: document.getElementById('brushPerformanceToggle').value === 'true',
                    showCanvasSizeWarning: showCanvasSizeWarning,
                    imageImportMode: document.getElementById('imageImportModeToggle').value,
                    showImageImportWarning: showImageImportWarning
                };
                const userEmail = user.email || user.sub;
                const emailVerified = user.email_verified || false;

                // Save to Xano
                console.log('Saving user settings with data:', {
                    userId: user.sub,
                    username: username,
                    hasProfileImage: !!profileImageData,
                    email: userEmail,
                    emailVerified: emailVerified
                });

                const response = await XanoService.saveUserSettings(
                    user.sub,
                    settings,
                    userEmail,
                    emailVerified,
                    username,
                    profileImageData
                );

                // Update local data - handle nested response structure from Xano
                const responseData = response.user_settings || response;
                userSettingsId = responseData.id;
                currentUserData = responseData;

                console.log('🔍 Updated currentUserData after save:', {
                    id: currentUserData.id,
                    username: currentUserData.username,
                    hasProfileImage: !!currentUserData.profile_image,
                    email: currentUserData.email
                });

                // Apply theme locally (skip Xano save since we already saved everything above)
                applyTheme(theme, true); // Skip Xano save to prevent overwriting username/profile image
                localStorage.setItem('pixelart-theme', theme);
                localStorage.setItem('pixelart-keybinds', JSON.stringify(currentKeybinds));

                // Update local brush performance and warning settings
                const brushPerformanceValue = document.getElementById('brushPerformanceToggle').value;
                brushPerformanceMode = brushPerformanceValue === 'true';
                localStorage.setItem('pixelart-brush-performance', brushPerformanceValue);
                localStorage.setItem('pixelart-show-canvas-warning', showCanvasSizeWarning.toString());

                // Update local image import settings
                const imageImportModeValue = document.getElementById('imageImportModeToggle').value;
                imageImportMode = imageImportModeValue;
                localStorage.setItem('pixelart-image-import-mode', imageImportModeValue);
                localStorage.setItem('pixelart-show-image-import-warning', showImageImportWarning.toString());

                // Update UI
                updateDropdownText();
                updateProfileDropdown();

                updateSyncStatus();
                showSuccessNotification('User settings saved successfully!');
                hideUserSettingsModal();

            } catch (error) {
                console.error('Failed to save user settings:', error);
                updateSyncStatus('error', 'Failed to save settings');
                alert('Failed to save settings. Please try again.');
            }
        }

        // Validate profile image file
        function validateProfileImage(file) {
            // Check file type
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
            if (!allowedTypes.includes(file.type)) {
                alert('Invalid file type. Please upload a PNG or JPG image only.\n\nSupported formats:\n• PNG (.png)\n• JPEG (.jpg, .jpeg)\n\nNot supported:\n• GIF (animated images)\n• WebP, BMP, SVG, etc.');
                return false;
            }

            // Check file extension as additional validation
            const fileName = file.name.toLowerCase();
            const allowedExtensions = ['.png', '.jpg', '.jpeg'];
            const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
            if (!hasValidExtension) {
                alert('Invalid file extension. Please upload a file with .png, .jpg, or .jpeg extension.');
                return false;
            }

            // Check file size (max 5MB)
            const maxSize = 5 * 1024 * 1024; // 5MB in bytes
            if (file.size > maxSize) {
                alert('File size too large. Please upload an image smaller than 5MB.');
                return false;
            }

            // Check minimum size (at least 1KB to ensure it's a real image)
            const minSize = 1024; // 1KB
            if (file.size < minSize) {
                alert('File size too small. Please upload a valid image file.');
                return false;
            }

            return true;
        }

        // Convert image file to base64
        function convertImageToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // Original download functionality (extracted from save button)
        function downloadCanvasOrSelection() {
            let x0,y0,w,h;let sWC=!0;
            if(isSelecting&&currentSelectionStart&&currentSelectionEnd&&toolSelector.value==='select'){
                x0=Math.min(currentSelectionStart.x,currentSelectionEnd.x);
                y0=Math.min(currentSelectionStart.y,currentSelectionEnd.y);
                const x1=Math.max(currentSelectionStart.x,currentSelectionEnd.x),y1=Math.max(currentSelectionStart.y,currentSelectionEnd.y);
                w=x1-x0+1;h=y1-y0+1;
                if(w>0&&h>0&&x0<currentGridSize&&y0<currentGridSize&&x1>=0&&y1>=0){
                    sWC=!1;
                    x0=Math.max(0,x0);y0=Math.max(0,y0);
                    w=Math.min(currentGridSize-x0,w);h=Math.min(currentGridSize-y0,h);
                } else {
                    alert('Invalid or off-canvas selection. Saving whole canvas instead.');
                    sWC=!0;
                }
            }
            if(sWC){x0=0;y0=0;w=currentGridSize;h=currentGridSize}
            if(w<=0||h<=0){alert("Cannot save empty region.");return}

            const aC=canvases[activeCanvasIndex];
            // MODIFIED LINE: Prioritize filenameInput, then canvas filename, then default.
            let userFilename = filenameInput.value.trim();
            
            // Validate filename using security utils if available
            if (userFilename && typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                const validation = SecurityUtils.InputValidator.validateFilename(userFilename);
                if (!validation.valid) {
                    showNotification(validation.error, 'error');
                    return;
                }
            } else if (userFilename) {
                // Fallback validation
                if (userFilename.length > 100) {
                    showNotification('Filename cannot exceed 100 characters', 'error');
                    return;
                }
                const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
                if (invalidChars.test(userFilename)) {
                    showNotification('Filename contains invalid characters', 'error');
                    return;
                }
            }
            
            if (userFilename === "" && aC && aC.filename) {
                userFilename = aC.filename.replace(/\.png$/i, ""); // Use canvas filename if input is empty
            }
            if (userFilename === "") {
                userFilename = 'pixel-art'; // Default if both are empty
            }
            const fNTU = userFilename.endsWith('.png') ? userFilename : userFilename + '.png';
            // END MODIFIED LINE

            const cC=document.createElement('canvas');cC.width=w;cC.height=h;
            const cCtx=cC.getContext('2d');cCtx.imageSmoothingEnabled=!1;
            for(const l of currentLayers){
                if(!l.isVisible||!l.pixels)continue;
                cCtx.globalAlpha=l.opacity/100;
                for(let rI=0;rI<h;rI++){
                    for(let cI=0;cI<w;cI++){
                        const aPY=y0+rI,aPX=x0+cI;
                        let cTS=EMPTY_COLOR;
                        if(l.pixels[aPY]&&typeof l.pixels[aPY][aPX]!=='undefined')cTS=l.pixels[aPY][aPX];
                        if(cTS!==EMPTY_COLOR){cCtx.fillStyle=cTS;cCtx.fillRect(cI,rI,1,1)}
                    }
                }
                cCtx.globalAlpha=1
            }
            const eC=document.createElement('canvas'),sF=Math.max(1,Math.floor(Math.min(2048/Math.max(w,1),2048/Math.max(h,1))));
            eC.width=w*sF;eC.height=h*sF;
            const eCtx=eC.getContext('2d');eCtx.imageSmoothingEnabled=!1;eCtx.drawImage(cC,0,0,eC.width,eC.height);
            const link=document.createElement('a');link.download=fNTU;link.href=eC.toDataURL('image/png');link.click()
        }

        // Cloud Save Modal Functions
        let currentSaveData = null; // Store current save data

        function showCloudSaveModal() {
            // Prepare save data
            currentSaveData = prepareSaveData();

            if (!currentSaveData) {
                alert("Cannot save empty region.");
                return;
            }

            // Show modal
            const modal = document.getElementById('cloudSaveModal');

            // Update preview
            updateSavePreview(currentSaveData);

            // Set default name
            const nameInput = document.getElementById('saveNameInput');
            nameInput.value = currentSaveData.defaultName;

            // Show/hide canvas save button based on whether it's a selection or full canvas
            const saveCanvasBtn = document.getElementById('saveCanvasBtn');
            if (currentSaveData.isFullCanvas) {
                saveCanvasBtn.style.display = 'flex';
            } else {
                saveCanvasBtn.style.display = 'none';
            }

            // Update storage counts (placeholder for now)
            updateStorageCounts();

            modal.style.display = 'flex';
        }

        function hideCloudSaveModal() {
            const modal = document.getElementById('cloudSaveModal');
            modal.style.display = 'none';
            currentSaveData = null;
        }

        function prepareSaveData() {
            let x0, y0, w, h, isFullCanvas = true;

            // Check if we have a selection
            if (isSelecting && currentSelectionStart && currentSelectionEnd && toolSelector.value === 'select') {
                x0 = Math.min(currentSelectionStart.x, currentSelectionEnd.x);
                y0 = Math.min(currentSelectionStart.y, currentSelectionEnd.y);
                const x1 = Math.max(currentSelectionStart.x, currentSelectionEnd.x);
                const y1 = Math.max(currentSelectionStart.y, currentSelectionEnd.y);
                w = x1 - x0 + 1;
                h = y1 - y0 + 1;

                if (w > 0 && h > 0 && x0 < currentGridSize && y0 < currentGridSize && x1 >= 0 && y1 >= 0) {
                    isFullCanvas = false;
                    x0 = Math.max(0, x0);
                    y0 = Math.max(0, y0);
                    w = Math.min(currentGridSize - x0, w);
                    h = Math.min(currentGridSize - y0, h);
                } else {
                    // Invalid selection, fall back to full canvas
                    isFullCanvas = true;
                }
            }

            if (isFullCanvas) {
                x0 = 0;
                y0 = 0;
                w = currentGridSize;
                h = currentGridSize;
            }

            if (w <= 0 || h <= 0) {
                return null;
            }

            // Get default name
            const activeCanvas = canvases[activeCanvasIndex];
            let defaultName = filenameInput.value.trim();
            if (defaultName === "" && activeCanvas && activeCanvas.filename) {
                defaultName = activeCanvas.filename.replace(/\.png$/i, "");
            }
            if (defaultName === "") {
                defaultName = isFullCanvas ? 'canvas' : 'selection';
            }

            return {
                x0, y0, w, h,
                isFullCanvas,
                defaultName,
                activeCanvas
            };
        }

        function updateSavePreview(saveData) {
            const previewCanvas = document.getElementById('savePreviewCanvas');
            const previewCtx = previewCanvas.getContext('2d');
            const dimensionsText = document.getElementById('savePreviewDimensions');
            const typeText = document.getElementById('savePreviewType');

            // Set preview canvas size (max 150x150)
            const maxPreviewSize = 150;
            const scale = Math.min(maxPreviewSize / saveData.w, maxPreviewSize / saveData.h);
            previewCanvas.width = Math.max(1, Math.floor(saveData.w * scale));
            previewCanvas.height = Math.max(1, Math.floor(saveData.h * scale));

            previewCtx.imageSmoothingEnabled = false;
            previewCtx.clearRect(0, 0, previewCanvas.width, previewCanvas.height);

            // Render the save area to preview
            for (const layer of currentLayers) {
                if (!layer.isVisible || !layer.pixels) continue;
                previewCtx.globalAlpha = layer.opacity / 100;

                for (let rI = 0; rI < saveData.h; rI++) {
                    for (let cI = 0; cI < saveData.w; cI++) {
                        const aPY = saveData.y0 + rI;
                        const aPX = saveData.x0 + cI;
                        let color = EMPTY_COLOR;

                        if (layer.pixels[aPY] && typeof layer.pixels[aPY][aPX] !== 'undefined') {
                            color = layer.pixels[aPY][aPX];
                        }

                        if (color !== EMPTY_COLOR) {
                            previewCtx.fillStyle = color;
                            previewCtx.fillRect(
                                Math.floor(cI * scale),
                                Math.floor(rI * scale),
                                Math.max(1, Math.ceil(scale)),
                                Math.max(1, Math.ceil(scale))
                            );
                        }
                    }
                }
                previewCtx.globalAlpha = 1;
            }

            // Update info text
            dimensionsText.textContent = `Dimensions: ${saveData.w}x${saveData.h}`;
            typeText.textContent = `Type: ${saveData.isFullCanvas ? 'Full Canvas' : 'Selection'}`;
        }

        function updateStorageCounts() {
            // Placeholder - will be implemented when we add the Xano table
            document.getElementById('imageStorageCount').textContent = 'Images: 0/100';
            document.getElementById('canvasStorageCount').textContent = 'Canvases: 0/20';
        }

        // Auto-save user settings without hiding modal
        async function autoSaveUserSettings() {
            if (!isAuthenticated || !user || !user.email_verified) {
                return false;
            }

            try {
                updateSyncStatus('syncing', 'Auto-saving user settings...');

                // Get form data
                const username = document.getElementById('usernameInput').value.trim();
                const profileImageInput = document.getElementById('profileImageInput');
                const theme = document.getElementById('themeToggle').value;

                // Validate username using security utils
                if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                    const validation = SecurityUtils.InputValidator.validateUsername(username);
                    if (!validation.valid) {
                        console.error('Username validation failed for auto-save:', validation.error);
                        return false;
                    }
                } else {
                    // Fallback validation if security utils not available
                    if (!username) {
                        console.error('Username cannot be empty for auto-save');
                        return false;
                    }
                    if (username.length > 50) {
                        console.error('Username cannot exceed 50 characters for auto-save');
                        return false;
                    }
                }

                // Check if username has changed and if so, verify availability (for auto-save, skip if check fails)
                const currentUsername = currentUserData ? currentUserData.username : null;
                if (username !== currentUsername) {
                    try {
                        const availabilityCheck = await XanoService.checkUsernameAvailability(username);
                        if (!availabilityCheck.available) {
                            console.error('Username not available for auto-save:', availabilityCheck.message);
                            // Store the specific error for the hideModal function to use
                            autoSaveUserSettings.lastError = 'USERNAME_TAKEN';
                            autoSaveUserSettings.lastErrorMessage = availabilityCheck.message;
                            updateSyncStatus('error', 'Username not available');
                            return false;
                        }
                    } catch (error) {
                        console.warn('Username availability check failed during auto-save, skipping auto-save:', error);
                        autoSaveUserSettings.lastError = 'USERNAME_CHECK_FAILED';
                        autoSaveUserSettings.lastErrorMessage = 'Unable to verify username availability';
                        updateSyncStatus('error', 'Username check failed');
                        return false;
                    }
                }

                // Handle profile image upload
                let profileImageData = null;
                if (profileImageInput.files && profileImageInput.files[0]) {
                    const file = profileImageInput.files[0];

                    // Validate file before converting
                    if (!validateProfileImage(file)) {
                        console.error('Invalid profile image for auto-save');
                        return false;
                    }

                    profileImageData = await convertImageToBase64(file);
                } else if (currentUserData && currentUserData.profile_image) {
                    // Keep existing profile image if no new one uploaded
                    profileImageData = currentUserData.profile_image;
                }

                // Prepare settings data - include all user preferences
                const settings = {
                    theme,
                    keybinds: currentKeybinds,
                    brushPerformanceMode: document.getElementById('brushPerformanceToggle').value === 'true',
                    showCanvasSizeWarning: showCanvasSizeWarning,
                    imageImportMode: document.getElementById('imageImportModeToggle').value,
                    showImageImportWarning: showImageImportWarning
                };
                const userEmail = user.email || user.sub;
                const emailVerified = user.email_verified || false;

                // Save to Xano
                const response = await XanoService.saveUserSettings(
                    user.sub,
                    settings,
                    userEmail,
                    emailVerified,
                    username,
                    profileImageData
                );

                // Update local data - handle nested response structure from Xano
                const responseData = response.user_settings || response;
                userSettingsId = responseData.id;
                currentUserData = responseData;

                // Apply theme locally (skip Xano save since we already saved everything above)
                applyTheme(theme, true);
                localStorage.setItem('pixelart-theme', theme);
                localStorage.setItem('pixelart-keybinds', JSON.stringify(currentKeybinds));

                // Update local brush performance and warning settings
                const brushPerformanceValue = document.getElementById('brushPerformanceToggle').value;
                brushPerformanceMode = brushPerformanceValue === 'true';
                localStorage.setItem('pixelart-brush-performance', brushPerformanceValue);
                localStorage.setItem('pixelart-show-canvas-warning', showCanvasSizeWarning.toString());

                // Update local image import settings
                const imageImportModeValue = document.getElementById('imageImportModeToggle').value;
                imageImportMode = imageImportModeValue;
                localStorage.setItem('pixelart-image-import-mode', imageImportModeValue);
                localStorage.setItem('pixelart-show-image-import-warning', showImageImportWarning.toString());

                // Update UI
                updateDropdownText();
                updateProfileDropdown();
                updateSyncStatus();

                return true;
            } catch (error) {
                console.error('Failed to auto-save user settings:', error);
                updateSyncStatus('error', 'Failed to auto-save settings');
                return false;
            }
        }

        // Hide user settings modal
        async function hideUserSettingsModal() {
            // Check if settings have changed and auto-save if needed
            if (hasUserSettingsChanged()) {
                try {
                    const saveSuccess = await autoSaveUserSettings();
                    if (saveSuccess) {
                        showSuccessNotification('User settings saved successfully!');
                    } else {
                        // Auto-save failed - check if we have specific error information
                        if (autoSaveUserSettings.lastError === 'USERNAME_TAKEN') {
                            updateSyncStatus('error', 'Username not available');
                            showErrorNotification(`Cannot save settings: ${autoSaveUserSettings.lastErrorMessage || 'Username already taken'}. Please choose a different username.`);
                        } else if (autoSaveUserSettings.lastError === 'USERNAME_CHECK_FAILED') {
                            updateSyncStatus('error', 'Username check failed');
                            showErrorNotification('Cannot save settings: Unable to verify username availability. Please try again.');
                        } else {
                            updateSyncStatus('error', 'Save failed');
                            showErrorNotification('Failed to save settings. Please try again.');
                        }
                        
                        // Clear the error for next time
                        autoSaveUserSettings.lastError = null;
                        autoSaveUserSettings.lastErrorMessage = null;
                        
                        // Don't close the modal so user can fix the issue
                        return;
                    }
                } catch (error) {
                    console.error('Failed to auto-save settings:', error);
                    updateSyncStatus('error', 'Save failed');
                    showErrorNotification('Failed to save settings. Please try again.');
                    // Don't close the modal so user can try again
                    return;
                }
            }

            const modal = document.getElementById('userSettingsModal');
            modal.style.display = 'none';
        }

        // Show canvas size warning tooltip
        function showCanvasSizeWarningTooltip(canvasSize) {
            if (!showCanvasSizeWarning) return; // Don't show if user disabled it

            const tooltip = document.getElementById('canvasSizeWarningTooltip');
            const sizeLabel = document.getElementById('warningCanvasSize');
            sizeLabel.textContent = `${canvasSize}x${canvasSize}`;
            tooltip.style.display = 'block';

            // Auto-hide after 10 seconds if user doesn't interact
            setTimeout(() => {
                if (tooltip.style.display === 'block') {
                    tooltip.style.display = 'none';
                }
            }, 10000);
        }

        // Hide canvas size warning tooltip
        function hideCanvasSizeWarningTooltip() {
            const tooltip = document.getElementById('canvasSizeWarningTooltip');
            tooltip.style.display = 'none';
        }

        // Show image import warning modal
        function showImageImportWarningModal(imageFile, isNewLayer = false) {
            if (!showImageImportWarning) return; // Don't show if user disabled it

            const modal = document.getElementById('imageImportWarningModal');
            modal.style.display = 'flex';

            // Store the image file for later use
            modal.dataset.imageFile = JSON.stringify({
                name: imageFile.name,
                size: imageFile.size,
                type: imageFile.type,
                lastModified: imageFile.lastModified
            });

            // Store the actual file object temporarily and whether it's for new layer
            window.tempImageFile = imageFile;
            window.tempImageFileIsNewLayer = isNewLayer;

            // Auto-hide after 15 seconds if user doesn't interact
            setTimeout(() => {
                if (modal.style.display === 'flex') {
                    hideImageImportWarningModal();
                }
            }, 15000);
        }

        // Hide image import warning modal
        function hideImageImportWarningModal() {
            const modal = document.getElementById('imageImportWarningModal');
            modal.style.display = 'none';

            // Clean up temporary file references
            if (window.tempImageFile) {
                delete window.tempImageFile;
            }
            if (window.tempImageFileIsNewLayer !== undefined) {
                delete window.tempImageFileIsNewLayer;
            }
        }

        // Initialize IndexedDB for large canvas storage
        async function initCanvasDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, DB_VERSION);

                request.onerror = () => {
                    console.warn('IndexedDB not available, falling back to localStorage only');
                    resolve(null);
                };

                request.onsuccess = (event) => {
                    canvasDB = event.target.result;
                    console.log('IndexedDB initialized for large canvas storage');
                    resolve(canvasDB);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains(CANVAS_STORE)) {
                        const store = db.createObjectStore(CANVAS_STORE, { keyPath: 'id' });
                        store.createIndex('timestamp', 'timestamp', { unique: false });
                    }
                };
            });
        }

        // Save canvas data to IndexedDB
        async function saveCanvasToIndexedDB(canvasData) {
            if (!canvasDB) return false;

            return new Promise((resolve) => {
                try {
                    const transaction = canvasDB.transaction([CANVAS_STORE], 'readwrite');
                    const store = transaction.objectStore(CANVAS_STORE);

                    const dataToStore = {
                        id: 'current_canvas',
                        data: canvasData,
                        timestamp: Date.now(),
                        size: JSON.stringify(canvasData).length
                    };

                    const request = store.put(dataToStore);

                    request.onsuccess = () => {
                        console.log(`Canvas saved to IndexedDB (${(dataToStore.size / 1024 / 1024).toFixed(2)}MB)`);
                        resolve(true);
                    };

                    request.onerror = () => {
                        console.warn('Failed to save canvas to IndexedDB');
                        resolve(false);
                    };
                } catch (error) {
                    console.warn('Error saving to IndexedDB:', error);
                    resolve(false);
                }
            });
        }

        // Load canvas data from IndexedDB
        async function loadCanvasFromIndexedDB() {
            if (!canvasDB) return null;

            return new Promise((resolve) => {
                try {
                    const transaction = canvasDB.transaction([CANVAS_STORE], 'readonly');
                    const store = transaction.objectStore(CANVAS_STORE);
                    const request = store.get('current_canvas');

                    request.onsuccess = () => {
                        if (request.result) {
                            console.log(`Canvas loaded from IndexedDB (${(request.result.size / 1024 / 1024).toFixed(2)}MB)`);
                            resolve(request.result.data);
                        } else {
                            resolve(null);
                        }
                    };

                    request.onerror = () => {
                        console.warn('Failed to load canvas from IndexedDB');
                        resolve(null);
                    };
                } catch (error) {
                    console.warn('Error loading from IndexedDB:', error);
                    resolve(null);
                }
            });
        }

        // Load canvases data from IndexedDB
        async function loadCanvasesFromIndexedDB() {
            if (!canvasDB) return false;

            return new Promise((resolve) => {
                try {
                    const transaction = canvasDB.transaction([CANVAS_STORE], 'readonly');
                    const store = transaction.objectStore(CANVAS_STORE);
                    const request = store.get('current_canvas');

                    request.onsuccess = () => {
                        if (request.result && request.result.data) {
                            const canvasData = request.result.data;
                            console.log(`Canvas loaded from IndexedDB (${(request.result.size / 1024 / 1024).toFixed(2)}MB)`);

                            // Validate the data structure
                            if (!canvasData.canvases || !Array.isArray(canvasData.canvases)) {
                                console.warn('Invalid canvas data structure in IndexedDB');
                                resolve(false);
                                return;
                            }

                            // Restore global state
                            nextCanvasId = canvasData.nextCanvasId || 0;
                            nextLayerIdGlobal = canvasData.nextLayerIdGlobal || 0;

                            // Restore canvases
                            canvases = canvasData.canvases.map(canvasState => ({
                                ...canvasState,
                                undoStack: [], // Reset undo/redo stacks
                                redoStack: []
                            }));

                            // Restore active canvas index
                            const savedActiveIndex = canvasData.activeCanvasIndex;
                            if (savedActiveIndex >= 0 && savedActiveIndex < canvases.length) {
                                switchActiveCanvas(savedActiveIndex);
                            } else if (canvases.length > 0) {
                                switchActiveCanvas(0);
                            }

                            console.log(`Loaded ${canvases.length} canvases from IndexedDB`);
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    };

                    request.onerror = () => {
                        console.warn('Failed to load canvases from IndexedDB');
                        resolve(false);
                    };
                } catch (error) {
                    console.warn('Error loading from IndexedDB:', error);
                    resolve(false);
                }
            });
        }

        // Clear canvas data from IndexedDB
        async function clearCanvasFromIndexedDB() {
            if (!canvasDB) return;

            return new Promise((resolve) => {
                try {
                    const transaction = canvasDB.transaction([CANVAS_STORE], 'readwrite');
                    const store = transaction.objectStore(CANVAS_STORE);
                    const request = store.delete('current_canvas');

                    request.onsuccess = () => {
                        console.log('Canvas cleared from IndexedDB');
                        resolve();
                    };

                    request.onerror = () => {
                        console.warn('Failed to clear canvas from IndexedDB');
                        resolve();
                    };
                } catch (error) {
                    console.warn('Error clearing IndexedDB:', error);
                    resolve();
                }
            });
        }

        // Operation Slot Management
        function allocateOperationSlot() {
            if (availableOperationSlots.length === 0) {
                console.warn('No available operation slots! Maximum simultaneous operations reached.');
                return -1;
            }
            return availableOperationSlots.shift();
        }

        function releaseOperationSlot(slotIndex) {
            if (slotIndex >= 0 && slotIndex < MAX_SIMULTANEOUS_OPERATIONS) {
                // Clear the slot data
                operationStrokeBuffers[slotIndex] = null;
                operationLayerDataBeforeStroke[slotIndex] = null;
                operationDirtyRegions[slotIndex] = [];

                // Return slot to available pool
                if (!availableOperationSlots.includes(slotIndex)) {
                    availableOperationSlots.push(slotIndex);
                }
            }
        }

        // Drawing Operation Management
        function createDrawingOperation(path, brushType, layerDataBeforeStroke, activeLayer, gridSize, color) {
            const operationId = nextOperationId++;
            const slotIndex = allocateOperationSlot();

            debugLog(`🔵 Creating operation ${operationId}`, {
                slotIndex,
                pathLength: path.length,
                brushType,
                color,
                availableSlots: availableOperationSlots.length
            });

            if (slotIndex === -1) {
                debugLog(`❌ Cannot create operation ${operationId}: No available slots`);
                console.error('Cannot create operation: No available slots');
                return null;
            }

            const operation = {
                id: operationId,
                slotIndex: slotIndex,
                path: [...path],
                brushType: brushType,
                layerDataBeforeStroke: JSON.parse(JSON.stringify(layerDataBeforeStroke)),
                activeLayer: activeLayer,
                gridSize: gridSize,
                color: color,
                cancelled: false,
                progress: 0,
                indicatorElement: null
            };

            // Initialize the operation's slot data
            operationStrokeBuffers[slotIndex] = Array(gridSize).fill(null).map(() => Array(gridSize).fill(undefined));
            operationLayerDataBeforeStroke[slotIndex] = JSON.parse(JSON.stringify(layerDataBeforeStroke));
            operationDirtyRegions[slotIndex] = [];

            drawingOperationQueue.push(operation);
            debugLog(`✅ Operation ${operationId} created in slot ${slotIndex}, queue size: ${drawingOperationQueue.length}`);
            return operation;
        }

        function removeDrawingOperation(operationId) {
            const index = drawingOperationQueue.findIndex(op => op.id === operationId);
            if (index !== -1) {
                const operation = drawingOperationQueue[index];
                if (operation.indicatorElement) {
                    operation.indicatorElement.remove();
                }
                // Release the operation slot
                releaseOperationSlot(operation.slotIndex);
                drawingOperationQueue.splice(index, 1);
            }
        }

        function cancelDrawingOperation(operationId) {
            const operation = drawingOperationQueue.find(op => op.id === operationId);
            if (operation) {
                operation.cancelled = true;
                if (operation.indicatorElement) {
                    operation.indicatorElement.remove();
                }
                removeDrawingOperation(operationId);
            }
        }

        function cancelAllDrawingOperations() {
            drawingOperationQueue.forEach(operation => {
                operation.cancelled = true;
                if (operation.indicatorElement) {
                    operation.indicatorElement.remove();
                }
                // Release all operation slots
                releaseOperationSlot(operation.slotIndex);
            });
            drawingOperationQueue = [];
        }

        // Global Variable Set Management
        function switchToOperationGlobals(slotIndex) {
            if (slotIndex < 0 || slotIndex >= MAX_SIMULTANEOUS_OPERATIONS) {
                debugLog(`❌ Invalid slot index: ${slotIndex}`);
                console.error('Invalid slot index:', slotIndex);
                return;
            }

            debugLog(`🔄 Switching to operation globals for slot ${slotIndex}`, {
                hasStrokeBuffer: !!operationStrokeBuffers[slotIndex],
                hasLayerData: !!operationLayerDataBeforeStroke[slotIndex],
                dirtyRegionsCount: operationDirtyRegions[slotIndex]?.length || 0
            });

            // Switch to the operation's global variables
            currentStrokeBuffer = operationStrokeBuffers[slotIndex];
            layerDataBeforeStroke = operationLayerDataBeforeStroke[slotIndex];
            clearStrokeBufferDirtyRegions();

            // Copy dirty regions if any
            if (operationDirtyRegions[slotIndex]) {
                strokeBufferDirtyRegions.length = 0;
                strokeBufferDirtyRegions.push(...operationDirtyRegions[slotIndex]);
            }
        }

        function saveOperationGlobals(slotIndex) {
            if (slotIndex < 0 || slotIndex >= MAX_SIMULTANEOUS_OPERATIONS) {
                console.error('Invalid slot index:', slotIndex);
                return;
            }

            // Save current global state back to the operation slot
            operationStrokeBuffers[slotIndex] = currentStrokeBuffer;
            operationLayerDataBeforeStroke[slotIndex] = layerDataBeforeStroke;
            operationDirtyRegions[slotIndex] = [...strokeBufferDirtyRegions];
        }

        function restoreMainGlobals() {
            // Restore to main drawing state (slot 0 is reserved for main drawing)
            currentStrokeBuffer = null;
            layerDataBeforeStroke = null;
            clearStrokeBufferDirtyRegions();
        }

        // Synchronized commit function to prevent concurrent layer modifications
        async function commitStrokeBufferToLayerSynchronized(operationId) {
            return new Promise((resolve) => {
                const commitTask = () => {
                    try {
                        debugLog(`🔐 Starting synchronized commit for operation ${operationId}`);
                        commitStrokeBufferToLayer();
                        debugLog(`🔓 Completed synchronized commit for operation ${operationId}`);
                        resolve();
                    } catch (error) {
                        debugLog(`💥 Error in synchronized commit for operation ${operationId}:`, error);
                        console.error('Error during synchronized commit:', error);
                        resolve();
                    }
                };

                if (layerCommitMutex) {
                    // Another commit is in progress, queue this one
                    debugLog(`⏳ Queueing commit for operation ${operationId}, mutex locked`);
                    pendingCommits.push(commitTask);
                } else {
                    // Lock the mutex and execute immediately
                    layerCommitMutex = true;
                    debugLog(`🔒 Acquired mutex for operation ${operationId}`);
                    commitTask();
                    layerCommitMutex = false;
                    debugLog(`🔓 Released mutex for operation ${operationId}`);

                    // Process any pending commits
                    if (pendingCommits.length > 0) {
                        const nextCommit = pendingCommits.shift();
                        setTimeout(() => {
                            layerCommitMutex = true;
                            nextCommit();
                            layerCommitMutex = false;

                            // Continue processing queue recursively
                            if (pendingCommits.length > 0) {
                                setTimeout(() => {
                                    const nextCommit = pendingCommits.shift();
                                    layerCommitMutex = true;
                                    nextCommit();
                                    layerCommitMutex = false;
                                }, 0);
                            }
                        }, 0);
                    }
                }
            });
        }



        // Show drawing status indicator for a specific operation
        function showDrawingStatusIndicator(operation = null) {
            if (operation) {
                // Create a new stacked indicator for this operation
                const indicator = createStackedDrawingIndicator(operation);
                operation.indicatorElement = indicator;
            } else {
                // Legacy single indicator support
                const indicator = document.getElementById('drawingStatusIndicator');
                isDrawingCancelled = false; // Reset cancellation flag
                drawingProgress = 0; // Reset progress
                updateDrawingProgress(0); // Initialize progress display
                indicator.style.display = 'block';
            }
        }

        // Hide drawing status indicator for a specific operation or all
        function hideDrawingStatusIndicator(operation = null) {
            if (operation && operation.indicatorElement) {
                operation.indicatorElement.remove();
                operation.indicatorElement = null;
            } else {
                // Legacy single indicator support
                const indicator = document.getElementById('drawingStatusIndicator');
                indicator.style.display = 'none';
            }
        }

        // Create a stacked drawing indicator for a specific operation
        function createStackedDrawingIndicator(operation) {
            const indicator = document.createElement('div');
            indicator.className = 'drawing-status-indicator stacked-indicator';
            indicator.style.position = 'fixed';
            indicator.style.top = (80 + (drawingOperationQueue.length - 1) * 60) + 'px';
            indicator.style.right = '20px';
            indicator.style.background = 'rgba(0,0,0,0.8)';
            indicator.style.color = 'white';
            indicator.style.padding = '8px 12px';
            indicator.style.borderRadius = '6px';
            indicator.style.fontSize = '0.85em';
            indicator.style.zIndex = '1001';
            indicator.style.boxShadow = '0 2px 8px rgba(0,0,0,0.3)';
            indicator.style.display = 'block';

            indicator.innerHTML = `
                <div class="status-content" style="display: flex; align-items: center; gap: 8px;">
                    <div class="progress-circle-container" style="position: relative; display: flex; align-items: center; justify-content: center; width: 24px; height: 24px;">
                        <svg class="progress-circle" width="24" height="24" viewBox="0 0 24 24" style="position: absolute; top: 0; left: 0;">
                            <circle class="progress-circle-bg" cx="12" cy="12" r="10" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
                            <circle class="progress-circle-fill" cx="12" cy="12" r="10" fill="none" stroke="white" stroke-width="2"
                                    stroke-linecap="round" stroke-dasharray="62.83" stroke-dashoffset="62.83"
                                    transform="rotate(-90 12 12)" style="transition: stroke-dashoffset 0.3s ease;"/>
                        </svg>
                        <span class="progress-percentage" style="font-size: 0.7em; font-weight: bold;">0%</span>
                    </div>
                    <span class="status-text">Drawing real line from preview...</span>
                    <button class="cancel-drawing-btn" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 2px 6px; border-radius: 3px; cursor: pointer; font-size: 0.8em;">Cancel</button>
                </div>
            `;

            // Add cancel button functionality
            const cancelBtn = indicator.querySelector('.cancel-drawing-btn');
            cancelBtn.addEventListener('click', () => {
                cancelDrawingOperation(operation.id);
                showSuccessNotification('Drawing cancelled');
            });

            document.body.appendChild(indicator);
            return indicator;
        }

        // Update progress for a specific operation
        function updateOperationProgress(operation, percentage) {
            if (!operation.indicatorElement) return;

            const progressCircle = operation.indicatorElement.querySelector('.progress-circle-fill');
            const progressPercentage = operation.indicatorElement.querySelector('.progress-percentage');

            if (progressCircle && progressPercentage) {
                const circumference = 62.83; // 2 * π * 10 (radius)
                const offset = circumference - (percentage / 100) * circumference;
                progressCircle.style.strokeDashoffset = offset;
                progressPercentage.textContent = Math.round(percentage) + '%';
            }

            operation.progress = percentage;
        }





        // Update drawing progress (legacy support)
        function updateDrawingProgress(percentage) {
            const progressCircle = document.querySelector('.progress-circle-fill');
            const progressText = document.getElementById('progressPercentage');

            if (progressCircle && progressText) {
                // Calculate stroke-dashoffset for circular progress
                const circumference = 62.83; // 2 * π * 10 (radius)
                const offset = circumference - (percentage / 100) * circumference;

                progressCircle.style.strokeDashoffset = offset;
                progressText.textContent = Math.round(percentage) + '%';
            }
        }

        // Show image import status indicator
        function showImageImportStatusIndicator() {
            const indicator = document.getElementById('imageImportStatusIndicator');
            isImageImportCancelled = false; // Reset cancellation flag
            imageImportProgress = 0; // Reset progress
            updateImageImportProgress(0, 'Importing image...'); // Initialize progress display
            indicator.style.display = 'block';
        }

        // Hide image import status indicator
        function hideImageImportStatusIndicator() {
            const indicator = document.getElementById('imageImportStatusIndicator');
            indicator.style.display = 'none';
        }

        // Update image import progress
        function updateImageImportProgress(percentage, statusText = null) {
            const progressCircle = document.querySelector('#imageImportStatusIndicator .progress-circle-fill');
            const progressText = document.getElementById('imageImportProgressPercentage');
            const statusTextElement = document.getElementById('imageImportStatusText');

            if (progressCircle && progressText) {
                // Calculate stroke-dashoffset for circular progress
                const circumference = 2 * Math.PI * 10; // radius = 10
                const offset = circumference - (percentage / 100) * circumference;
                progressCircle.style.strokeDashoffset = offset;
                progressText.textContent = `${Math.round(percentage)}%`;
            }

            if (statusText && statusTextElement) {
                statusTextElement.textContent = statusText;
            }

            imageImportProgress = percentage;
        }

        // Process drawing operations with progress updates for a specific operation
        async function processDrawingWithProgress(drawingFunction, operation = null) {
            return new Promise(async (resolve) => {
                const progressCallback = (percentage) => {
                    if (operation) {
                        updateOperationProgress(operation, percentage);
                    } else {
                        // Legacy support
                        drawingProgress = percentage;
                        updateDrawingProgress(percentage);
                    }
                };

                try {
                    await drawingFunction(progressCallback);
                    resolve();
                } catch (error) {
                    console.error('Error in drawing process:', error);
                    resolve();
                }
            });
        }

        // Cancel drawing operation (legacy support - cancels all operations)
        function cancelDrawing() {
            isDrawingCancelled = true;
            hideDrawingStatusIndicator();

            // Cancel all queued operations
            cancelAllDrawingOperations();

            // Reset performance preview state
            isPerformancePreviewActive = false;
            performancePreviewPath = [];

            // Clear stroke buffer
            currentStrokeBuffer = null;
            layerDataBeforeStroke = null;

            // Perform undo to remove the preview line that was already applied
            if (currentUndoStack.length > 0) {
                performUndo();
            }

            showSuccessNotification('Drawing cancelled');
        }



        // Draw simple single-pixel line preview for performance mode
        function drawSimpleLinePreview(x0, y0, x1, y1, color) {
            if (!currentStrokeBuffer) return;

            // Simple Bresenham line algorithm for single-pixel preview
            const dx = Math.abs(x1 - x0);
            const dy = Math.abs(y1 - y0);
            const sx = x0 < x1 ? 1 : -1;
            const sy = y0 < y1 ? 1 : -1;
            let err = dx - dy;

            let x = x0, y = y0;

            while (true) {
                // Draw single pixel
                if (x >= 0 && x < currentGridSize && y >= 0 && y < currentGridSize) {
                    if (!currentStrokeBuffer[y]) currentStrokeBuffer[y] = Array(currentGridSize).fill(undefined);
                    currentStrokeBuffer[y][x] = color;
                    addStrokeBufferDirtyRegion(x, y, x, y);
                }

                if (x === x1 && y === y1) break;

                const e2 = 2 * err;
                if (e2 > -dy) {
                    err -= dy;
                    x += sx;
                }
                if (e2 < dx) {
                    err += dx;
                    y += sy;
                }
            }

            // Apply the simple preview to the layer
            _applyStrokeBufferToActiveLayerForPreview();
        }

        // Initialize Auth0 and set up event listeners
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('DOM loaded, initializing Auth0...');

            // Check if Auth0 is available
            if (typeof auth0 !== 'undefined' && auth0.createAuth0Client) {
                await initAuth0();
            } else {
                console.log('Auth0 not available - running in offline mode');
                // Still update UI for offline mode
                updateAuthButton();
            }

            // Set up button event listeners
            const authButton = document.getElementById('authButton');
            const settingsButton = document.getElementById('settingsButton');

            if (authButton) {
                authButton.addEventListener('click', handleAuthClick);
            }

            if (settingsButton) {
                settingsButton.addEventListener('click', showBasicSettingsModal);
            }

            // Set up profile dropdown events
            setupProfileDropdownEvents();

            // Basic Settings modal event listeners
            const basicSettingsModal = document.getElementById('basicSettingsModal');
            const basicSettingsCloseBtn = basicSettingsModal.querySelector('.modal-close-btn');
            const basicThemeToggle = document.getElementById('basicThemeToggle');
            const basicSaveKeybindsBtn = document.getElementById('basicSaveKeybinds');
            const basicRevertKeybindsBtn = document.getElementById('basicRevertKeybinds');

            // Basic modal close events
            basicSettingsCloseBtn.addEventListener('click', hideBasicSettingsModal);
            basicSettingsModal.addEventListener('click', (e) => {
                if (e.target === basicSettingsModal) hideBasicSettingsModal();
            });

            // Basic theme toggle
            basicThemeToggle.addEventListener('change', (e) => {
                applyTheme(e.target.value);
                localStorage.setItem('pixelart-theme', e.target.value);
            });

            // Basic brush performance toggle
            const basicBrushPerformanceToggle = document.getElementById('basicBrushPerformanceToggle');
            basicBrushPerformanceToggle.addEventListener('change', (e) => {
                brushPerformanceMode = e.target.value === 'true';
                localStorage.setItem('pixelart-brush-performance', e.target.value);
            });

            // Basic image import mode toggle
            const basicImageImportModeToggle = document.getElementById('basicImageImportModeToggle');
            basicImageImportModeToggle.addEventListener('change', (e) => {
                imageImportMode = e.target.value;
                localStorage.setItem('pixelart-image-import-mode', e.target.value);
            });

            // Basic keybind buttons
            basicSaveKeybindsBtn.addEventListener('click', saveBasicKeybinds);
            basicRevertKeybindsBtn.addEventListener('click', revertBasicKeybinds);

            // User Settings modal event listeners
            const userSettingsModal = document.getElementById('userSettingsModal');
            const userSettingsCloseBtn = userSettingsModal.querySelector('.modal-close-btn');
            const themeToggle = document.getElementById('themeToggle');
            const saveUserSettingsBtn = document.getElementById('saveUserSettings');
            const revertKeybindsBtn = document.getElementById('revertKeybinds');
            const uploadProfileBtn = document.getElementById('uploadProfileBtn');
            const profileImageInput = document.getElementById('profileImageInput');

            // User modal close events
            userSettingsCloseBtn.addEventListener('click', hideUserSettingsModal);
            userSettingsModal.addEventListener('click', (e) => {
                if (e.target === userSettingsModal) hideUserSettingsModal();
            });

            // User theme toggle
            themeToggle.addEventListener('change', (e) => {
                applyTheme(e.target.value, true); // Skip Xano save - will be saved when user clicks "Save All Settings"
            });

            // User brush performance toggle
            const brushPerformanceToggle = document.getElementById('brushPerformanceToggle');
            brushPerformanceToggle.addEventListener('change', (e) => {
                brushPerformanceMode = e.target.value === 'true';
                localStorage.setItem('pixelart-brush-performance', e.target.value);
            });

            // User image import mode toggle
            const imageImportModeToggle = document.getElementById('imageImportModeToggle');
            imageImportModeToggle.addEventListener('change', (e) => {
                imageImportMode = e.target.value;
                localStorage.setItem('pixelart-image-import-mode', e.target.value);
            });

            // User save button
            saveUserSettingsBtn.addEventListener('click', saveAllUserSettings);
            revertKeybindsBtn.addEventListener('click', revertKeybinds);

            // Username input validation with availability checking
            const usernameInput = document.getElementById('usernameInput');
            let usernameCheckTimeout = null;
            
            if (usernameInput) {
                usernameInput.addEventListener('input', (e) => {
                    const username = e.target.value.trim();
                    
                    // Clear previous timeout
                    if (usernameCheckTimeout) {
                        clearTimeout(usernameCheckTimeout);
                    }
                    
                    // Basic validation first
                    if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
                        const validation = SecurityUtils.InputValidator.validateUsername(username);
                        if (!validation.valid) {
                            e.target.setCustomValidity(validation.error);
                            return;
                        }
                    } else {
                        // Fallback validation if security utils not available
                        if (username.length > 50) {
                            e.target.setCustomValidity('Username cannot exceed 50 characters');
                            return;
                        }
                    }
                    
                    // Clear validation if basic checks pass
                    e.target.setCustomValidity('');
                    
                    // Check availability after a delay (debouncing)
                    if (username && username !== (currentUserData ? currentUserData.username : '')) {
                        usernameCheckTimeout = setTimeout(async () => {
                            try {
                                const availabilityCheck = await XanoService.checkUsernameAvailability(username);
                                if (!availabilityCheck.available) {
                                    e.target.setCustomValidity(availabilityCheck.message);
                                } else if (availabilityCheck.warning) {
                                    e.target.setCustomValidity(availabilityCheck.message);
                                } else {
                                    e.target.setCustomValidity('');
                                }
                            } catch (error) {
                                console.warn('Username availability check failed:', error);
                                // Don't set validation error for network issues
                            }
                        }, 1000); // 1 second delay
                    }
                });
            }

            // Profile image upload
            uploadProfileBtn.addEventListener('click', () => {
                profileImageInput.click();
            });

            // Canvas Size Warning Tooltip event listeners
            const canvasSizeWarningTooltip = document.getElementById('canvasSizeWarningTooltip');
            const closeWarningTooltip = document.getElementById('closeWarningTooltip');
            const enablePerformanceModeBtn = document.getElementById('enablePerformanceModeBtn');
            const continueNormalModeBtn = document.getElementById('continueNormalModeBtn');
            const dontShowAgainBtn = document.getElementById('dontShowAgainBtn');

            closeWarningTooltip.addEventListener('click', () => {
                hideCanvasSizeWarningTooltip();
            });

            enablePerformanceModeBtn.addEventListener('click', () => {
                brushPerformanceMode = true;
                localStorage.setItem('pixelart-brush-performance', 'true');
                // Update UI toggles
                document.getElementById('basicBrushPerformanceToggle').value = 'true';
                if (document.getElementById('brushPerformanceToggle')) {
                    document.getElementById('brushPerformanceToggle').value = 'true';
                }

                // When enabling performance mode, also set "don't show again" to true
                showCanvasSizeWarning = false;
                localStorage.setItem('pixelart-show-canvas-warning', 'false');

                // Save to Xano if user is authenticated
                if (isAuthenticated && user && user.email_verified) {
                    saveSettingsToXano(
                        localStorage.getItem('pixelart-theme') || 'light',
                        currentKeybinds
                    );
                }

                hideCanvasSizeWarningTooltip();
            });

            continueNormalModeBtn.addEventListener('click', () => {
                // Don't change the "don't show again" setting - keep showing warnings
                hideCanvasSizeWarningTooltip();
            });

            dontShowAgainBtn.addEventListener('click', () => {
                showCanvasSizeWarning = false;
                localStorage.setItem('pixelart-show-canvas-warning', 'false');
                // Save to Xano if user is authenticated
                if (isAuthenticated && user && user.email_verified) {
                    saveSettingsToXano(
                        localStorage.getItem('pixelart-theme') || 'light',
                        currentKeybinds
                    );
                }
                hideCanvasSizeWarningTooltip();
            });

            // Image Import Warning Modal event listeners
            const imageImportWarningModal = document.getElementById('imageImportWarningModal');
            const imageImportCloseBtn = imageImportWarningModal.querySelector('.modal-close-btn');
            const enablePixelPerfectBtn = document.getElementById('enablePixelPerfectBtn');
            const continueWithFitToViewBtn = document.getElementById('continueWithFitToViewBtn');
            const dontShowImageWarningBtn = document.getElementById('dontShowImageWarningBtn');

            // Image import modal close events
            imageImportCloseBtn.addEventListener('click', hideImageImportWarningModal);
            imageImportWarningModal.addEventListener('click', (e) => {
                if (e.target === imageImportWarningModal) hideImageImportWarningModal();
            });

            enablePixelPerfectBtn.addEventListener('click', () => {
                // Update settings
                imageImportMode = 'pixel-perfect';
                showImageImportWarning = false;

                // Update localStorage
                localStorage.setItem('pixelart-image-import-mode', 'pixel-perfect');
                localStorage.setItem('pixelart-show-image-import-warning', 'false');

                // Update UI if elements exist
                if (document.getElementById('imageImportModeToggle')) {
                    document.getElementById('imageImportModeToggle').value = 'pixel-perfect';
                }
                if (document.getElementById('basicImageImportModeToggle')) {
                    document.getElementById('basicImageImportModeToggle').value = 'pixel-perfect';
                }

                // Save to Xano if user is authenticated
                if (isAuthenticated && user && user.email_verified) {
                    saveSettingsToXano(
                        localStorage.getItem('pixelart-theme') || 'light',
                        currentKeybinds
                    );
                }

                // Import the image with pixel-perfect mode
                if (window.tempImageFile) {
                    if (window.tempImageFileIsNewLayer) {
                        importImageFileAsNewLayer(window.tempImageFile);
                    } else {
                        importImageFile(window.tempImageFile);
                    }
                }

                hideImageImportWarningModal();
            });

            continueWithFitToViewBtn.addEventListener('click', () => {
                // Import with current fit-to-view mode
                if (window.tempImageFile) {
                    if (window.tempImageFileIsNewLayer) {
                        importImageFileAsNewLayer(window.tempImageFile);
                    } else {
                        importImageFile(window.tempImageFile);
                    }
                }
                hideImageImportWarningModal();
            });

            dontShowImageWarningBtn.addEventListener('click', () => {
                // Update settings
                showImageImportWarning = false;

                // Update localStorage
                localStorage.setItem('pixelart-show-image-import-warning', 'false');

                // Save to Xano if user is authenticated
                if (isAuthenticated && user && user.email_verified) {
                    saveSettingsToXano(
                        localStorage.getItem('pixelart-theme') || 'light',
                        currentKeybinds
                    );
                }

                // Import with current mode
                if (window.tempImageFile) {
                    importImageFile(window.tempImageFile);
                }

                hideImageImportWarningModal();
            });

            // Cancel drawing button event listener
            const cancelDrawingBtn = document.getElementById('cancelDrawingBtn');
            if (cancelDrawingBtn) {
                cancelDrawingBtn.addEventListener('click', () => {
                    cancelDrawing();
                });
            }

            // Cancel image import button event listener
            const cancelImageImportBtn = document.getElementById('cancelImageImportBtn');
            if (cancelImageImportBtn) {
                cancelImageImportBtn.addEventListener('click', () => {
                    isImageImportCancelled = true;
                    hideImageImportStatusIndicator();
                });
            }

            profileImageInput.addEventListener('change', (e) => {
                if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];

                    // Validate file before processing
                    if (!validateProfileImage(file)) {
                        // Clear the input if validation fails
                        e.target.value = '';
                        return;
                    }

                    // Mark that profile image has changed
                    if (originalUserSettings) {
                        originalUserSettings.profileImageChanged = true;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        document.getElementById('profilePreview').src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Cloud Save Modal event listeners
            const cloudSaveModal = document.getElementById('cloudSaveModal');
            const cloudSaveCloseBtn = cloudSaveModal.querySelector('.modal-close-btn');
            const saveAsImageBtn = document.getElementById('saveAsImageBtn');
            const saveCanvasBtn = document.getElementById('saveCanvasBtn');
            const saveToDeviceBtn = document.getElementById('saveToDeviceBtn');

            // Cloud save modal close events
            cloudSaveCloseBtn.addEventListener('click', hideCloudSaveModal);
            cloudSaveModal.addEventListener('click', (e) => {
                if (e.target === cloudSaveModal) hideCloudSaveModal();
            });

            // Save option buttons
            saveAsImageBtn.addEventListener('click', () => {
                // TODO: Implement save as image to cloud
                alert('Save as image to cloud - Coming soon!');
            });

            saveCanvasBtn.addEventListener('click', () => {
                // TODO: Implement save canvas to cloud
                alert('Save canvas to cloud - Coming soon!');
            });

            saveToDeviceBtn.addEventListener('click', () => {
                hideCloudSaveModal();
                downloadCanvasOrSelection();
            });

            // Load initial settings
            loadCurrentSettings();

            // Update button state
            updateAuthButton();

            // Simple mobile fallback - just update UI after a short delay
            // This doesn't interfere with auth state, just ensures UI is consistent
            setTimeout(() => {
                updateAuthButton();
            }, 500);

            // Add click handler for logo to navigate to home
            const leftToolbar = document.getElementById('leftToolbar');
            if (leftToolbar) {
                leftToolbar.addEventListener('click', function() {
                    window.location.href = '/';
                });
            }
        });

        // Debug function to test Xano connectivity (call from browser console)
        window.testXanoSave = async function() {
            if (!isAuthenticated || !user) {
                console.log('❌ User not authenticated');
                return;
            }

            const testData = {
                user_id: user.sub,
                email: user.email || user.sub,
                username: 'test_user_123',
                profile_image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                theme: 'light',
                keybinds: JSON.stringify({test: 'value'}),
                email_verified: true
            };

            console.log('🧪 Testing Xano save with data:', testData);

            try {
                const response = await fetch(XANO_CONFIG.endpoints.saveUserSettings, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                console.log('🧪 Xano response:', result);

                if (response.ok) {
                    console.log('✅ Xano save test successful!');
                } else {
                    console.log('❌ Xano save test failed:', result);
                }
            } catch (error) {
                console.log('❌ Xano save test error:', error);
            }
        };

        // Initialize the app
        function initializeApp() {
            try {
                // Initialize the editor with saved or default canvases
                initializeEditor();

                // Start the main render loop
                mainRenderLoop();

                console.log('✅ PixelArtNexus initialized successfully');
            } catch (error) {
                console.error('❌ Failed to initialize app:', error);
            }
        }

        initializeApp();

        // AdSense initialization and refresh functionality
        function initializeAds(retryCount = 0) {
            const maxRetries = 30; // Stop after 30 seconds
            
            // Wait for AdSense to load
            if (typeof window.adsbygoogle === 'undefined') {
                if (retryCount < maxRetries) {
                    console.log(`AdSense not loaded yet, retrying in 1 second... (${retryCount + 1}/${maxRetries})`);
                    setTimeout(() => initializeAds(retryCount + 1), 1000);
                } else {
                    console.log('AdSense failed to load after 30 seconds - likely blocked by ad blocker. Continuing without ads.');
                }
                return;
            }

            try {
                // Initialize left banner ad
                const leftAd = document.querySelector('#leftBanner .adsbygoogle');
                if (leftAd && !leftAd.dataset.adsbygoogleStatus) {
                    (window.adsbygoogle = window.adsbygoogle || []).push({});
                    console.log('Left banner ad initialized');
                }

                // Initialize right banner ad
                const rightAd = document.querySelector('#rightBanner .adsbygoogle');
                if (rightAd && !rightAd.dataset.adsbygoogleStatus) {
                    (window.adsbygoogle = window.adsbygoogle || []).push({});
                    console.log('Right banner ad initialized');
                }

                // Set up refresh timer (every 60 seconds)
                setInterval(refreshAds, 60000);
                console.log('Ad refresh timer set for 60 seconds');

            } catch (error) {
                console.error('Error initializing ads:', error);
            }
        }

        function refreshAds() {
            try {
                // Only refresh if AdSense is available
                if (typeof window.adsbygoogle === 'undefined') {
                    console.log('AdSense not available for refresh');
                    return;
                }

                // Refresh left banner ad
                const leftAd = document.querySelector('#leftBanner .adsbygoogle');
                if (leftAd) {
                    // Clear the ad content
                    leftAd.innerHTML = '';
                    leftAd.removeAttribute('data-adsbygoogle-status');

                    // Re-initialize the ad
                    (window.adsbygoogle = window.adsbygoogle || []).push({});
                    console.log('Left banner ad refreshed');
                }

                // Refresh right banner ad
                const rightAd = document.querySelector('#rightBanner .adsbygoogle');
                if (rightAd) {
                    // Clear the ad content
                    rightAd.innerHTML = '';
                    rightAd.removeAttribute('data-adsbygoogle-status');

                    // Re-initialize the ad
                    (window.adsbygoogle = window.adsbygoogle || []).push({});
                    console.log('Right banner ad refreshed');
                }

            } catch (error) {
                console.error('Error refreshing ads:', error);
            }
        }

        // Initialize ads after a short delay to ensure DOM is ready
        setTimeout(initializeAds, 2000);