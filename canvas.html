<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
  <meta name="google-adsense-account" content="ca-pub-****************">
  <meta name="description" content="Professional pixel art editor with layers, multiple canvases, and advanced drawing tools. Create stunning pixel art directly in your browser.">
  <meta name="keywords" content="pixel art, editor, drawing, canvas, layers, digital art, graphics">
  <meta name="author" content="PixelArt Nexus">
  <meta name="robots" content="index, follow">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://pixelartnexus.com/canvas">
  <meta property="og:title" content="Pixel Art Nexus | Professional Pixel Art Editor">
  <meta property="og:description" content="Create stunning pixel art with our professional editor featuring layers, multiple canvases, and advanced drawing tools.">
  <meta property="og:image" content="https://pixelartnexus.com/banner.png">
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://pixelartnexus.com/canvas">
  <meta property="twitter:title" content="Pixel Art Nexus | Professional Pixel Art Editor">
  <meta property="twitter:description" content="Create stunning pixel art with our professional editor featuring layers, multiple canvases, and advanced drawing tools.">
  <meta property="twitter:image" content="https://pixelartnexus.com/banner.png">
  
  <title>Pixel Art Nexus | Pixel Art Editor with Layers & Multiple Canvases</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="apple-touch-icon" href="/logo.png">
  <link rel="manifest" href="/manifest.json">
  <link rel="stylesheet" href="canvas.css">
  <script src="runtime-config.js"></script>
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
       crossorigin="anonymous"></script>
</head>
<body>
    <div id="fullWidthWrapper">
        <div id="leftBanner" class="banner-div">
            <div id="leftToolbar" class="left-toolbar">
                <div class="title-container">
                    <img src="title.webp" alt="PixelArtNexus" class="title-image">
                </div>
            </div>
            <div class="banner-image-container">
                <img src="banner.png" alt="Banner" class="banner-image">
            </div>
            <div class="banner-ad-container">
                <!-- leftbanner -->
                <ins class="adsbygoogle"
                     style="display:block"
                     data-ad-client="ca-pub-****************"
                     data-ad-slot="6832096065"
                     data-ad-format="auto"
                     data-full-width-responsive="true"></ins>
            </div>
        </div>
        <div id="mainContainer">
        <div id="toolbar">
            <label>Tool:
              <select id="tool">
                <option value="draw">Draw (D)</option>
                <option value="pan">Pan (P)</option>
                <option value="eyedropper">Eyedrop (T)</option>
                <option value="erase">Erase (E)</option>
                <option value="fill">Fill (F)</option>
                <option value="line">Line (L)</option>
                <option value="rectangle">Rect (R)</option>
                <option value="circle">Circle (C)</option>
                <option value="select">Select (S)</option>
                <option value="clear">Clear Layer</option>
              </select>
            </label>
            <label>Grid:
              <select id="gridStyle">
                <option value="lines">Lines</option>
                <option value="squares" selected>Squares</option>
                <option value="off">Off</option>
              </select>
            </label>
            <button id="openSizeModalBtn" type="button">Change Size</button>
            <label>Thick: <input type="number" id="thickness" value="1" min="1" max="10" style="width:40px;">
            </label>
            <label for="color-picker">Color:</label>
            <input type="color" id="color-picker" value="#1EC7FA">
            <input type="text" id="hexInput" value="#1EC7FA" maxlength="7" aria-label="Hex Color Input" style="width:70px;">

            <label style="white-space: nowrap;">
                <input type="checkbox" id="checkerboardToggle"> Check BG
            </label>

            <input type="text" id="filename" placeholder="filename.png" style="width: 125px;">
            <button id="saveBtn">Save</button>

            <!-- Mobile-only undo/redo buttons -->
            <div id="mobileUndoRedoContainer" class="mobile-only">
                <button id="mobileUndoBtn" title="Undo (Ctrl+Z)" disabled>↶</button>
                <button id="mobileRedoBtn" title="Redo (Ctrl+Y)" disabled>↷</button>
            </div>
        </div>

        <div id="editorContainer">
        <div id="brushOptionsBar">
            <div id="brushOpacityContainer" class="opacity-control-set">
                <div class="opacity-input-line">
                    <label for="brushOpacityInput">Brush Opacity:</label>
                    <input type="number" id="brushOpacityInput" min="0" max="100" value="100">
                    <span>%</span>
                </div>
                <input type="range" id="brushOpacitySlider" min="0" max="100" value="100">
            </div>
            <div id="commonBrushOptionsContainer">
                <h4>Common</h4>
                <label><input type="radio" name="brushType" value="normal" checked> Normal (Square)</label>
                <label><input type="radio" name="brushType" value="circular"> Circular</label>
                <label><input type="radio" name="brushType" value="calligraphy"> Calligraphy (Vertical)</label>
                <label><input type="radio" name="brushType" value="spray_paint"> Spray Paint</label>
                <label><input type="radio" name="brushType" value="glow"> Glow</label>
                <label><input type="radio" name="brushType" value="anti_alias"> Anti-alias</label>
                <label><input type="radio" name="brushType" value="taper"> Taper</label>
            </div>
            <div id="panOptionsContainer" style="display: none; flex-direction: column; align-items: stretch;">
                <h4>Pan Tool</h4>
                <div class="tool-info-div" style="padding: 8px 5px; font-size: 0.85em; text-align: center; background-color: #f8f8f8; border: 1px solid #ddd; border-radius: 3px;">
                    Click & drag to move
                    <br>around the canvas.
                    <br>
                    <br>Minimap appears when
                    <br>zoomed in.
                </div>
            </div>
            <div id="eyedropperOptionsContainer" style="display: none; flex-direction: column; align-items: stretch;">
                <h4>Eyedropper Tool</h4>
                <div class="tool-info-div" style="padding: 8px 5px; font-size: 0.85em; text-align: center; background-color: #f8f8f8; border: 1px solid #ddd; border-radius: 3px;">
                    Click on any pixel to
                    <br>pick its color.
                    <br>
                    <br>Automatically switches
                    <br>back to draw tool.
                </div>
            </div>
            <div id="eraseOptionsContainer" style="display: none; flex-direction: column; align-items: stretch;">
                <h4>Erase Tool</h4>
                <div class="tool-info-div" style="padding: 8px 5px; font-size: 0.85em; text-align: center; background-color: #f8f8f8; border: 1px solid #ddd; border-radius: 3px;">
                    Click & drag to erase
                    <br>pixels from the canvas.
                    <br>
                    <br>Uses same brush size
                    <br>and options as draw tool.
                </div>
            </div>
            <div id="circleOptionsContainer" style="display: none; flex-direction: column; align-items: stretch;">
                <h4>Circle Tool</h4>
                <div class="tool-info-div" style="padding: 6px 4px; font-size: 0.8em; text-align: center; background-color: #e8f4fd; border: 1px solid #ddd; border-radius: 3px;">
                    Click & drag to draw circle outline.
                    <br>Hold Shift for perfect circle (1:1 ratio).
                    <br>Uses current brush settings.
                </div>
            </div>
            <div id="selectOptionsContainer" style="display: none; flex-direction: column; align-items: stretch;">
                <h4>Select Options</h4>
                <div id="moveSelectionInfo" class="selection-info-div" style="padding: 4px 3px; font-size: 0.75em; text-align: center; background-color: #f8f8f8; border: 1px solid #ddd; border-radius: 3px;">
                    Drag inside to move
                    <br>Click outside to place
                </div>
                <div id="resizeSelectionInfo" class="selection-info-div" style="padding: 4px 3px; font-size: 0.75em; text-align: center; background-color: #e8f4fd; border: 1px solid #ddd; border-radius: 3px; margin-top: 5px;">
                    Blue corners: Scale
                    <br>Green edges: Stretch
                    <br>Shift for 1:1 ratio
                </div>
                <div id="copySelectionInfo" class="selection-info-div" style="padding: 4px 3px; font-size: 0.75em; text-align: center; background-color: #f0f0f0; border: 1px solid #ddd; border-radius: 3px; margin-top: 5px;">
                    Ctrl+C to copy
                    <br>Ctrl+V to paste
                </div>
                <div id="deleteSelectionInfo" class="selection-info-div" style="padding: 4px 3px; font-size: 0.75em; text-align: center; background-color: #f0f0f0; border: 1px solid #ddd; border-radius: 3px; margin-top: 5px;">
                    Delete/Backspace
                    <br>to delete area
                </div>
                <div style="margin-top: 8px; border-top: 1px solid #ddd; padding-top: 8px;">
                    <button id="flipHorizontalBtn" class="selection-option-btn" style="margin-bottom: 5px;">Flip Horizontal</button>
                    <br>
                    <button id="flipVerticalBtn" class="selection-option-btn" style="margin-bottom: 5px;">Flip Vertical</button>
                </div>

                <!-- Selection Actions -->
                <div class="selection-actions" style="margin-top: 8px; border-top: 1px solid #ddd; padding-top: 8px;">
                    <button id="mobileCopyBtn" class="selection-action-btn">📋 Copy</button>
                    <button id="mobilePasteBtn" class="selection-action-btn">📄 Paste</button>
                    <button id="mobileDeleteSelectionBtn" class="selection-action-btn">🗑️ Del</button>
                </div>
                <div style="margin-top: 8px; border-top: 1px solid #ddd; padding-top: 8px;">
                    <button id="scaleUpSelectionBtn" class="selection-option-btn" style="margin-bottom: 5px;">Scale Up (2x)</button>
                    <button id="scaleDownSelectionBtn" class="selection-option-btn" style="margin-bottom: 5px;">Scale Down (0.5x)</button>
                </div>
                <div style="margin-top: 8px; border-top: 1px solid #ddd; padding-top: 8px;">
                    <button id="rotate90SelectionBtn" class="selection-option-btn" style="margin-bottom: 5px;">Rotate 90°</button>
                    <button id="rotate180SelectionBtn" class="selection-option-btn" style="margin-bottom: 5px;">Rotate 180°</button>
                </div>
            </div>
            <div id="hatchingBrushOptionsContainer">
                <h4>Hatching</h4>
                <label><input type="radio" name="brushType" value="hatch"> Hatch (Diagonal)</label>
                <label><input type="radio" name="brushType" value="cross_hatch"> Cross-Hatch (Diagonal)</label>
                <label><input type="radio" name="brushType" value="grid_hatch"> Grid Hatch (+)</label>
                <label><input type="radio" name="brushType" value="dot_hatch"> Dot Hatch (Inverse Grid)</label>
            </div>
            <div id="mirrorOptionsContainer" style="padding: 10px; background-color: #e8e8e8; border: 1px solid #ccc; border-radius: 3px; margin-top:15px;">
                <h4>Mirror Options</h4>
                <label style="display: block; margin-bottom: 5px;">
                    <input type="checkbox" id="verticalMirrorToggle"> Vertical Mirror
                </label>
                <label style="display: block; margin-bottom: 5px;">
                    <input type="checkbox" id="horizontalMirrorToggle"> Horizontal Mirror
                </label>
                <label style="display: block; margin-bottom: 5px;">
                    <input type="checkbox" id="radialMirrorToggle"> Radial Mirror
                </label>
                <div id="radialMirrorSettings" style="display: none; margin-top: 8px; font-size: 0.8em; padding: 5px; border: 1px solid #ccc; border-radius: 3px; background-color: #f8f8f8;">
                    <label for="radialAxesSlider">Axes: <span id="radialAxesValue">3</span> (3-10)</label>
                    <input type="range" id="radialAxesSlider" name="radialAxes" min="3" max="10" value="3" style="width: 100%;">
                </div>
            </div>
            <div id="sprayBrushOptionsContainer" style="display: none;"> <h4>Spray Options</h4>
                <div id="sprayRadiusContainer"> <label for="sprayRadiusSlider">Spray Radius: <span id="sprayRadiusValue">10</span></label>
                    <input type="range" id="sprayRadiusSlider" name="sprayRadius" min="1" max="50" value="10">
                </div>
            </div>
            <div id="glowBrushOptionsContainer" style="display: none;"> <h4>Glow Options</h4>
                <div id="glowSizeContainer" style="margin-top: 8px; font-size: 0.8em; padding: 5px; border: 1px solid #ccc; border-radius: 3px; background-color: #f8f8f8; box-sizing: border-box;"> <label for="glowSizeSlider">Glow Size: <span id="glowSizeValue">5</span></label>
                    <input type="range" id="glowSizeSlider" name="glowSize" min="1" max="50" value="5">
                    </div>
            </div>
            <div id="fillOptionsContainer" style="display: none;">
                <div id="fillOpacityContainer" class="opacity-control-set">
                    <div class="opacity-input-line">
                        <label for="fillOpacityInput">Fill Opacity:</label>
                        <input type="number" id="fillOpacityInput" min="0" max="100" value="100">
                        <span>%</span>
                    </div>
                    <input type="range" id="fillOpacitySlider" min="0" max="100" value="100">
                </div>
                <h4>Fill Type</h4>
                <label><input type="radio" name="fillType" value="normal_fill" checked> Normal</label>
                <label><input type="radio" name="fillType" value="hatch_fill"> Hatch (Diagonal)</label>
                <label><input type="radio" name="fillType" value="cross_hatch_fill"> Cross-Hatch (Diagonal)</label>
                <label><input type="radio" name="fillType" value="grid_hatch_fill"> Grid Hatch (+)</label>
                <label><input type="radio" name="fillType" value="dot_hatch_fill"> Dot Hatch (Inverse Grid)</label>
                <label><input type="radio" name="fillType" value="transparent_fill"> Erase</label>
            </div>

            <div id="hatchScaleContainer" style="margin-top: 10px; display: none;"> <label for="hatchScaleSlider">Hatch Scale: <span id="hatchScaleValue">1</span></label>
                <input type="range" id="hatchScaleSlider" name="hatchScale" min="1" max="10" value="1">
            </div>
        </div>
        <div id="canvasArea">
            <div id="canvasTabsBar">
                <button id="addNewCanvasBtn" title="New Canvas (+)">+</button>
            </div>
            <div id="canvasContainer">
                <canvas id="pixelCanvas"></canvas>
                <canvas id="gridCanvas"></canvas>
                <canvas id="minimapCanvas"></canvas>
            </div>
        </div>
        <div id="rightPanels">
            <div id="zoomControls">
                <div id="zoomSliderContainer">
                    <input type="range" id="zoomSlider" min="1" max="32" value="1" step="0.1" orient="vertical">
                </div>
            </div>
            <div id="layersPanelContainer">
                <h3>Layers (<span id="layerCount">0</span>/20)</h3>
                <div id="layerActions">
                    <button id="newLayerBtn">New Layer</button>
                    <button id="duplicateLayerBtn">Duplicate Layer</button>
                    <button id="importImageAsLayerBtn">Import Image as Layer</button>
                    <button id="mergeLayerDownBtn">Merge Down</button>
                    <input type="file" id="importImageAsLayerInput" accept="image/*" style="display: none;">
                </div>
                <ul id="layersList"></ul>
            </div>
        </div>
    </div>
        <div id="bottomToolbar" class="bottom-toolbar">
            <div class="footer-section core-info">
                <a href="/about" class="footer-link">About</a>
                <a href="/contact" class="footer-link">Contact</a>
                <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
                <a href="/help" class="footer-link">Help / FAQ</a>
            </div>
            <div class="footer-section legal-policy">
                <a href="/terms" class="footer-link">Terms of Service</a>
                <a href="/privacy" class="footer-link">Privacy Policy</a>
            </div>
            <div class="footer-section community-social">
                <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
                <a href="/social" class="footer-link">Social Media</a>
                <a href="/blog" class="footer-link">Blog / Updates</a>
            </div>
            <div class="footer-section copyright">
                <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
            </div>
        </div>
        </div>
        <div id="rightBanner" class="banner-div">
            <div id="rightToolbar" class="right-toolbar">
                <!-- Login button (shown when not authenticated) -->
                <button id="authButton" class="auth-button">Login</button>

                <!-- Settings button (shown when not authenticated) -->
                <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

                <!-- Profile dropdown (shown when authenticated and verified) -->
                <div id="profileDropdown" class="profile-dropdown" style="display: none;">
                    <div id="profileAvatar" class="profile-avatar">
                        <img id="profileImage" src="" alt="Profile" class="profile-image">
                    </div>
                    <div id="profileMenu" class="profile-menu" style="display: none;">
                        <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
                        <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
                        <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
                    </div>
                </div>
            </div>
            <div class="banner-image-container">
                <img src="banner.png" alt="Banner" class="banner-image">
            </div>
            <div class="banner-ad-container">
                <!-- rightbanner -->
                <ins class="adsbygoogle"
                     style="display:block"
                     data-ad-client="ca-pub-****************"
                     data-ad-slot="4302049929"
                     data-ad-format="auto"
                     data-full-width-responsive="true"></ins>
            </div>
        </div>
    </div>
    <!-- Basic Settings Modal (for non-authenticated users) -->
    <div id="basicSettingsModal" class="modal-overlay" style="display: none;">
        <div class="modal-content settings-modal">
            <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
            <h2>Settings</h2>

            <!-- Theme Settings -->
            <div class="settings-section">
                <h3>Appearance</h3>
                <div class="setting-item">
                    <div>
                        <label for="basicThemeToggle">Theme:</label>
                        <select id="basicThemeToggle">
                            <option value="light">Light Mode</option>
                            <option value="dark">Dark Mode</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Performance Settings -->
            <div class="settings-section">
                <h3>Performance</h3>
                <div class="setting-item">
                    <div>
                        <label for="basicBrushPerformanceToggle">Brush Performance Mode:</label>
                        <select id="basicBrushPerformanceToggle">
                            <option value="false">Off (Normal Drawing)</option>
                            <option value="true">On (Preview Mode)</option>
                        </select>
                    </div>
                    <small class="setting-description">When enabled, shows a simple line preview while drawing, then applies the actual brush when you release. Helps reduce lag on large canvases.</small>
                </div>
                <div class="setting-item">
                    <div>
                        <label for="basicImageImportModeToggle">Image Import Mode:</label>
                        <select id="basicImageImportModeToggle">
                            <option value="fit-to-view">Fit-to-View (Scale to zoom)</option>
                            <option value="pixel-perfect">Pixel-Perfect (1:1 mapping)</option>
                        </select>
                    </div>
                    <small class="setting-description">Pixel-Perfect preserves original image resolution for clean pixel art imports. Fit-to-View scales images to match your current zoom level.</small>
                </div>
            </div>

            <!-- Keybind Settings -->
            <div class="settings-section">
                <h3>Keybinds</h3>
                <div class="keybind-list" id="basicKeybindList">
                    <!-- Keybinds will be populated by JavaScript -->
                </div>
                <div class="keybind-actions">
                    <button id="basicRevertKeybinds" class="revert-btn">Revert to Default</button>
                    <button id="basicSaveKeybinds" class="save-btn">Save Keybinds</button>
                </div>
            </div>

            <div class="login-prompt">
                <p><strong>Want to sync your settings across devices?</strong></p>
                <p>Login to save your preferences to the cloud!</p>
            </div>
        </div>
    </div>

    <!-- User Settings Modal -->
    <div id="userSettingsModal" class="modal-overlay" style="display: none;">
        <div class="modal-content user-settings-modal">
            <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
            <h2>User Settings</h2>

            <!-- Sync Status -->
            <div id="syncStatus" class="sync-status" style="display: none;">
                <span id="syncStatusText">Settings synced to cloud</span>
                <span id="syncStatusIcon">✓</span>
            </div>

            <!-- Email Verification Status -->
            <div id="emailVerificationStatus" class="verification-status" style="display: none;">
                <span id="verificationStatusText">Email verification status</span>
                <span id="verificationStatusIcon">?</span>
            </div>

            <!-- Profile Settings -->
            <div class="settings-section">
                <h3>Profile</h3>
                <div class="setting-item">
                    <label for="usernameInput">Username:</label>
                    <input type="text" id="usernameInput" class="username-input" placeholder="Enter username" maxlength="50">
                </div>
                <div class="setting-item">
                    <label for="profileImageInput">Profile Picture:</label>
                    <div class="profile-image-upload">
                        <img id="profilePreview" class="profile-preview" src="" alt="Profile Preview">
                        <input type="file" id="profileImageInput" accept="image/png,image/jpeg,image/jpg" class="file-input">
                        <button id="uploadProfileBtn" class="upload-btn">Choose Image</button>
                    </div>
                </div>
            </div>

            <!-- Appearance Settings -->
            <div class="settings-section">
                <h3>Appearance</h3>
                <div class="setting-item">
                    <div>
                        <label for="themeToggle">Theme:</label>
                        <select id="themeToggle">
                            <option value="light">Light Mode</option>
                            <option value="dark">Dark Mode</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Performance Settings -->
            <div class="settings-section">
                <h3>Performance</h3>
                <div class="setting-item">
                    <div>
                        <label for="brushPerformanceToggle">Brush Performance Mode:</label>
                        <select id="brushPerformanceToggle">
                            <option value="false">Off (Normal Drawing)</option>
                            <option value="true">On (Preview Mode)</option>
                        </select>
                    </div>
                    <small class="setting-description">When enabled, shows a simple line preview while drawing, then applies the actual brush when you release. Helps reduce lag on large canvases.</small>
                </div>
                <div class="setting-item">
                    <div>
                        <label for="imageImportModeToggle">Image Import Mode:</label>
                        <select id="imageImportModeToggle">
                            <option value="fit-to-view">Fit-to-View (Scale to zoom)</option>
                            <option value="pixel-perfect">Pixel-Perfect (1:1 mapping)</option>
                        </select>
                    </div>
                    <small class="setting-description">Pixel-Perfect preserves original image resolution for clean pixel art imports. Fit-to-View scales images to match your current zoom level.</small>
                </div>
            </div>

            <!-- Keybind Settings -->
            <div class="settings-section">
                <h3>Keybinds</h3>
                <div class="keybind-list" id="keybindList">
                    <!-- Keybinds will be populated by JavaScript -->
                </div>
                <div class="keybind-actions">
                    <button id="revertKeybinds" class="revert-btn">Revert to Default</button>
                </div>
            </div>

            <!-- Save Button -->
            <div class="settings-buttons">
                <button id="saveUserSettings" class="save-btn">Save All Settings</button>
            </div>
        </div>
    </div>

    <div id="sizeModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
            <h3>Select New Canvas Size</h3>

            <div class="modal-preview-area">
                <div class="preview-container">
                    <h4>Current (<span id="currentSizeLabel"></span>)</h4>
                    <canvas id="currentCanvasPreview"></canvas>
                </div>
                <div class="preview-container">
                    <h4>New (<span id="newSizeLabel"></span>)</h4>
                    <canvas id="newCanvasPreview"></canvas>
                </div>
            </div>
            <div id="sizeOptionsContainer" class="modal-options">
                </div>
            <div class="modal-actions">
                <button type="button" id="cancelSizeChangeBtn">Cancel</button>
                <button type="button" id="confirmSizeChangeBtn">OK</button>
            </div>
        </div>
    </div>

    <!-- Canvas Size Warning Tooltip -->
    <div id="canvasSizeWarningTooltip" class="canvas-warning-tooltip" style="display: none;">
        <div class="warning-tooltip-content">
            <div class="warning-header">
                <span class="warning-icon">⚠️</span>
                <span class="warning-title">Large Canvas Warning</span>
                <button id="closeWarningTooltip" class="warning-close">&times;</button>
            </div>
            <p>Canvas size <strong id="warningCanvasSize"></strong> may cause lag. Enable Performance Mode for smoother drawing?</p>
            <div class="warning-tooltip-actions">
                <button id="enablePerformanceModeBtn" class="warning-btn-small primary">Enable</button>
                <button id="continueNormalModeBtn" class="warning-btn-small secondary">Continue</button>
                <button id="dontShowAgainBtn" class="warning-btn-small tertiary">Don't show again</button>
            </div>
        </div>
        <div class="warning-tooltip-arrow"></div>
    </div>

    <!-- Image Import Warning Modal -->
    <div id="imageImportWarningModal" class="modal-overlay" style="display: none;">
        <div class="modal-content image-import-warning-modal">
            <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
            <div class="warning-modal-content">
                <div class="warning-modal-header">
                    <span class="warning-icon">🖼️</span>
                    <h2>Image Import Quality Notice</h2>
                </div>
                <div class="warning-modal-body">
                    <p>You're importing an image using <strong>Fit-to-View mode</strong>, which scales the image to fit your current zoom level.</p>
                    <p>For pixel art, this can reduce quality and destroy original details.</p>
                    <div class="import-mode-comparison">
                        <div class="import-mode-option">
                            <h4>🔍 Current: Fit-to-View</h4>
                            <p>• Scales image to current zoom</p>
                            <p>• May blur or distort pixels</p>
                            <p>• Good for photos/artwork</p>
                        </div>
                        <div class="import-mode-option recommended">
                            <h4>✨ Recommended: Pixel-Perfect</h4>
                            <p>• Preserves original resolution</p>
                            <p>• 1:1 pixel mapping</p>
                            <p>• Perfect for pixel art</p>
                        </div>
                    </div>
                    <div class="import-tip">
                        <h4>💡 Pro Tip for Best Quality:</h4>
                        <p>For cleanest imports, use images with <strong>1:1 pixel ratio</strong> (no upscaling). If your image looks blocky or has repeated pixels, try exporting it at original size instead of scaled up versions.</p>
                    </div>
                </div>
                <div class="warning-modal-actions">
                    <button id="enablePixelPerfectBtn" class="warning-btn primary">Enable Pixel-Perfect & Import</button>
                    <button id="continueWithFitToViewBtn" class="warning-btn secondary">Continue with Fit-to-View</button>
                    <button id="dontShowImageWarningBtn" class="warning-btn tertiary">Don't show again</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Drawing Status Indicator -->
    <div id="drawingStatusIndicator" class="drawing-status-indicator" style="display: none;">
        <div class="status-content">
            <div class="progress-circle-container">
                <svg class="progress-circle" width="24" height="24" viewBox="0 0 24 24">
                    <circle class="progress-circle-bg" cx="12" cy="12" r="10" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
                    <circle class="progress-circle-fill" cx="12" cy="12" r="10" fill="none" stroke="white" stroke-width="2"
                            stroke-linecap="round" stroke-dasharray="62.83" stroke-dashoffset="62.83"
                            transform="rotate(-90 12 12)"/>
                </svg>
                <span class="progress-percentage" id="progressPercentage">0%</span>
            </div>
            <span id="statusText">Drawing real line from preview...</span>
            <button id="cancelDrawingBtn" class="cancel-drawing-btn" title="Cancel drawing">&times;</button>
        </div>
    </div>

    <!-- Image Import Status Indicator -->
    <div id="imageImportStatusIndicator" class="drawing-status-indicator" style="display: none;">
        <div class="status-content">
            <div class="progress-circle-container">
                <svg class="progress-circle" width="24" height="24" viewBox="0 0 24 24">
                    <circle class="progress-circle-bg" cx="12" cy="12" r="10" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
                    <circle class="progress-circle-fill" cx="12" cy="12" r="10" fill="none" stroke="white" stroke-width="2"
                            stroke-linecap="round" stroke-dasharray="62.83" stroke-dashoffset="62.83"
                            transform="rotate(-90 12 12)"/>
                </svg>
                <span class="progress-percentage" id="imageImportProgressPercentage">0%</span>
            </div>
            <span id="imageImportStatusText">Importing image...</span>
            <button id="cancelImageImportBtn" class="cancel-drawing-btn" title="Cancel import">&times;</button>
        </div>
    </div>

    <!-- Cloud Save Modal -->
    <div id="cloudSaveModal" class="modal-overlay" style="display: none;">
        <div class="modal-content cloud-save-modal">
            <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
            <h2>Save Artwork</h2>

            <!-- Preview Section -->
            <div class="save-preview-section">
                <h3>Preview</h3>
                <div class="save-preview-container">
                    <canvas id="savePreviewCanvas" class="save-preview-canvas"></canvas>
                    <div class="save-preview-info">
                        <p id="savePreviewDimensions">Dimensions: 0x0</p>
                        <p id="savePreviewType">Type: Canvas</p>
                    </div>
                </div>
            </div>

            <!-- Name Input -->
            <div class="save-name-section">
                <label for="saveNameInput">Artwork Name:</label>
                <input type="text" id="saveNameInput" class="save-name-input" placeholder="Enter artwork name">
                <small class="save-name-hint">Names will be auto-numbered if duplicates exist (e.g., "artwork (1)")</small>
            </div>

            <!-- Save Options -->
            <div class="save-options-section">
                <h3>Save Options</h3>
                <div class="save-option-buttons">
                    <button id="saveAsImageBtn" class="save-option-btn primary">
                        <span class="save-btn-icon">🖼️</span>
                        <span class="save-btn-text">Save as Image to Cloud</span>
                        <small>Save as PNG image (up to 100 images)</small>
                    </button>
                    <button id="saveCanvasBtn" class="save-option-btn primary" style="display: none;">
                        <span class="save-btn-icon">🎨</span>
                        <span class="save-btn-text">Save Canvas to Cloud</span>
                        <small>Save full editable canvas (up to 20 canvases)</small>
                    </button>
                    <button id="saveToDeviceBtn" class="save-option-btn secondary">
                        <span class="save-btn-icon">💾</span>
                        <span class="save-btn-text">Save to Device</span>
                        <small>Download as PNG file</small>
                    </button>
                </div>
            </div>

            <!-- Cloud Storage Status -->
            <div class="cloud-storage-status">
                <div class="storage-info">
                    <span id="imageStorageCount">Images: 0/100</span>
                    <span id="canvasStorageCount">Canvases: 0/20</span>
                </div>
            </div>
        </div>
    </div>

    <script src="error-handler.js"></script>
    <script src="security-utils.js"></script>
    <script src="canvas.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('Service Worker registered successfully:', registration.scope);
                    })
                    .catch((error) => {
                        console.log('Service Worker registration failed:', error);
                    });
            });
        }
    </script>

</body>
</html>