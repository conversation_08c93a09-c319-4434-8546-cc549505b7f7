<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Privacy Policy | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3586346747453362"
       crossorigin="anonymous"></script>
  <style>
    .privacy-section {
      margin: 25px 0;
    }

    .privacy-section h2 {
      color: #2c5aa0;
      margin-bottom: 15px;
      font-size: 1.4em;
      border-bottom: 2px solid #e0e0e0;
      padding-bottom: 8px;
    }

    .privacy-section h3 {
      color: #2c5aa0;
      margin: 20px 0 10px 0;
      font-size: 1.1em;
    }

    .privacy-section p {
      margin: 12px 0;
      line-height: 1.6;
    }

    .privacy-section ul {
      margin: 12px 0;
      padding-left: 25px;
    }

    .privacy-section li {
      margin: 8px 0;
      line-height: 1.5;
    }

    .effective-date {
      background-color: #f0f8ff;
      padding: 15px;
      border-left: 4px solid #2c5aa0;
      margin: 20px 0;
      border-radius: 4px;
    }

    .highlight-box {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }

    .contact-info {
      background-color: #e8f5e8;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }

    /* Dark theme styles */
    body.dark-theme .privacy-section h2 {
      color: #6db3f2;
      border-bottom-color: #555;
    }

    body.dark-theme .privacy-section h3 {
      color: #6db3f2;
    }

    body.dark-theme .effective-date {
      background-color: #1a2332;
      border-left-color: #6db3f2;
    }

    body.dark-theme .highlight-box {
      background-color: #332a1a;
      border-color: #b8860b;
      color: #e0e0e0;
    }

    body.dark-theme .contact-info {
      background-color: #1a2e1a;
      color: #e0e0e0;
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="title.webp" alt="PixelArtNexus" class="logo">
        <h1>Privacy Policy</h1>
      </div>

      <div class="effective-date">
        <strong>Last updated:</strong> January 2025
      </div>

      <div class="highlight-box">
        <p><strong>Your Privacy Matters:</strong> PixelArtNexus is committed to protecting your privacy and being transparent about how we collect, use, and protect your information. This Privacy Policy explains our data practices in clear, understandable terms.</p>
      </div>

      <div class="privacy-section">
        <h2>1. Information We Collect</h2>
        
        <h3>Account Information</h3>
        <p>When you create an account with PixelArtNexus, we collect:</p>
        <ul>
          <li><strong>Email address</strong> - Used for account identification, authentication, and communication</li>
          <li><strong>Authentication data</strong> - Provided by Auth0 and Google when you sign in</li>
          <li><strong>Username and profile information</strong> - Generated automatically or customized by you</li>
          <li><strong>Email verification status</strong> - To ensure account security</li>
        </ul>

        <h3>User-Generated Content</h3>
        <ul>
          <li><strong>Pixel art creations</strong> - All artwork you create using our tools</li>
          <li><strong>Canvas data</strong> - Layer information, tool settings, and project files</li>
          <li><strong>Profile images</strong> - Custom profile pictures you upload</li>
          <li><strong>Saved projects</strong> - Up to 20 canvases and 100 images stored in your cloud account</li>
        </ul>

        <h3>Usage and Preference Data</h3>
        <ul>
          <li><strong>Application settings</strong> - Theme preferences, keybinds, tool configurations</li>
          <li><strong>Performance settings</strong> - Brush performance mode, import preferences</li>
          <li><strong>Usage analytics</strong> - How you interact with our platform for improvement purposes</li>
          <li><strong>Local storage data</strong> - Canvas state and preferences stored in your browser</li>
        </ul>

        <h3>Technical Information</h3>
        <ul>
          <li><strong>Device and browser information</strong> - For compatibility and performance optimization</li>
          <li><strong>IP address and location data</strong> - For security and analytics purposes</li>
          <li><strong>Cookies and tracking data</strong> - For authentication, preferences, and advertising</li>
        </ul>
      </div>

      <div class="privacy-section">
        <h2>2. How We Use Your Information</h2>
        
        <h3>Core Service Functionality</h3>
        <ul>
          <li>Providing and maintaining your PixelArtNexus account</li>
          <li>Syncing your settings and artwork across devices</li>
          <li>Enabling authentication and account security</li>
          <li>Storing and retrieving your creative projects</li>
        </ul>

        <h3>Platform Improvement</h3>
        <ul>
          <li>Analyzing usage patterns to improve our tools and features</li>
          <li>Optimizing performance and user experience</li>
          <li>Developing new features based on user needs</li>
          <li>Troubleshooting technical issues</li>
        </ul>

        <h3>Communication</h3>
        <ul>
          <li>Sending important account and service updates</li>
          <li>Responding to your support requests and feedback</li>
          <li>Notifying you of policy changes or new features</li>
        </ul>

        <h3>Legal and Safety</h3>
        <ul>
          <li>Enforcing our Terms of Service and Community Guidelines</li>
          <li>Preventing fraud, abuse, and unauthorized access</li>
          <li>Complying with legal obligations and requests</li>
        </ul>
      </div>

      <div class="privacy-section">
        <h2>3. Information Sharing and Third Parties</h2>
        
        <h3>Authentication Services</h3>
        <p><strong>Auth0:</strong> We use Auth0 for secure authentication. Auth0 processes your login credentials and provides us with your email and basic profile information. Auth0's privacy practices are governed by their own privacy policy.</p>
        
        <p><strong>Google Authentication:</strong> When you sign in with Google, Google shares your email and basic profile information with us through Auth0. This is governed by Google's privacy policy.</p>

        <h3>Data Storage</h3>
        <p><strong>Xano Backend:</strong> Your user settings, artwork, and account information are stored securely on Xano's cloud infrastructure. All data transmission is encrypted using HTTPS.</p>

        <h3>Advertising</h3>
        <p><strong>Google AdSense:</strong> We use Google AdSense to display advertisements. Google may collect information about your visits to our site and other sites to provide targeted ads. You can opt out of personalized advertising by visiting Google's Ad Settings.</p>

        <h3>We Do Not Sell Your Data</h3>
        <p>We do not sell, rent, or trade your personal information to third parties for their marketing purposes.</p>
      </div>

      <div class="privacy-section">
        <h2>4. Data Security</h2>
        <ul>
          <li><strong>Encryption:</strong> All data transmission uses HTTPS encryption</li>
          <li><strong>Access Control:</strong> User settings are tied to Auth0 user IDs, ensuring users can only access their own data</li>
          <li><strong>Secure Storage:</strong> Data is stored on secure cloud infrastructure with appropriate safeguards</li>
          <li><strong>Regular Updates:</strong> We regularly update our security practices and systems</li>
        </ul>
        
        <p>While we implement strong security measures, no system is 100% secure. We encourage you to use strong passwords and keep your account information confidential.</p>
      </div>

      <div class="privacy-section">
        <h2>5. Your Rights and Choices</h2>
        
        <h3>Account Control</h3>
        <ul>
          <li><strong>Access:</strong> You can view and edit your account information and settings at any time</li>
          <li><strong>Deletion:</strong> You can delete your artwork, settings, and account data</li>
          <li><strong>Export:</strong> You can download your artwork and project files</li>
          <li><strong>Settings:</strong> You can modify your preferences, theme, and privacy settings</li>
        </ul>

        <h3>Communication Preferences</h3>
        <ul>
          <li>You can opt out of non-essential communications</li>
          <li>Essential service communications (security alerts, policy changes) cannot be disabled</li>
        </ul>

        <h3>Advertising Choices</h3>
        <ul>
          <li>You can opt out of personalized advertising through Google Ad Settings</li>
          <li>You can use ad blockers, though this may affect site functionality</li>
        </ul>
      </div>

      <div class="privacy-section">
        <h2>6. Data Retention</h2>
        <p>We retain your information for as long as your account is active or as needed to provide services. Specifically:</p>
        <ul>
          <li><strong>Account data:</strong> Retained until you delete your account</li>
          <li><strong>Artwork and projects:</strong> Retained until you delete them or your account</li>
          <li><strong>Usage analytics:</strong> Aggregated data may be retained indefinitely for improvement purposes</li>
          <li><strong>Legal requirements:</strong> Some data may be retained longer if required by law</li>
        </ul>
      </div>

      <div class="privacy-section">
        <h2>7. Children's Privacy</h2>
        <p>PixelArtNexus is intended for users aged 12 and older. We do not knowingly collect personal information from children under 12. If you believe we have collected information from a child under 12, please contact us immediately.</p>
        
        <p>Users between 12-18 should have parental permission before using our services.</p>
      </div>

      <div class="privacy-section">
        <h2>8. International Users</h2>
        <p>PixelArtNexus is operated from the United States. If you are accessing our services from outside the US, please be aware that your information may be transferred to, stored, and processed in the United States where our servers are located.</p>
      </div>

      <div class="privacy-section">
        <h2>9. Changes to This Privacy Policy</h2>
        <p>We may update this Privacy Policy from time to time. When we make changes, we will:</p>
        <ul>
          <li>Update the "Last updated" date at the top of this policy</li>
          <li>Notify users of significant changes through our platform</li>
          <li>For material changes, provide additional notice as required by law</li>
        </ul>
        
        <p>Your continued use of PixelArtNexus after changes become effective constitutes acceptance of the updated policy.</p>
      </div>

      <div class="privacy-section">
        <h2>10. Contact Us</h2>
        <div class="contact-info">
          <p>If you have questions about this Privacy Policy or our data practices, please contact us:</p>
          <ul>
            <li><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li><strong>Subject Line:</strong> "Privacy Policy Question"</li>
          </ul>
          
          <p>We will respond to privacy-related inquiries within 30 days.</p>
        </div>
      </div>

      <!-- Footer -->
      <div class="bottom-toolbar">
        <div class="footer-section core-info">
          <a href="/about" class="footer-link">About</a>
          <a href="/contact" class="footer-link">Contact</a>
          <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
          <a href="/help" class="footer-link">Help / FAQ</a>
        </div>
        <div class="footer-section legal-policy">
          <a href="/terms" class="footer-link">Terms of Service</a>
          <a href="/privacy" class="footer-link">Privacy Policy</a>
        </div>
        <div class="footer-section community-social">
          <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
          <a href="/social" class="footer-link">Social Media</a>
          <a href="/blog" class="footer-link">Blog / Updates</a>
        </div>
        <div class="footer-section copyright">
          <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
        </div>
      </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Login button (shown when not authenticated) -->
        <button id="authButton" class="auth-button">Login</button>

        <!-- Settings button (shown when not authenticated) -->
        <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

        <!-- Profile dropdown (shown when authenticated and verified) -->
        <div id="profileDropdown" class="profile-dropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div class="setting-item">
          <div>
            <label for="themeSelect">Theme:</label>
            <select id="themeSelect">
              <option value="light">Light Mode</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>
      </div>

      <!-- User Profile Settings (shown only for authenticated users) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div class="setting-item">
          <div>
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter username" maxlength="50">
          </div>
        </div>
        <div class="setting-item">
          <div>
            <label for="profileImageInput">Profile Picture:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
            <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
          </div>
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <script src="shared-layout.js"></script>
</body>
</html>
