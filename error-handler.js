// Error Handling and Logging Utility for PixelArt Nexus

class ErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.maxErrors = 50;
        this.setupGlobalErrorHandling();
    }

    setupGlobalErrorHandling() {
        // Handle uncaught JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                stack: event.reason ? event.reason.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // Handle resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError({
                    type: 'Resource Loading Error',
                    message: `Failed to load: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    timestamp: new Date().toISOString()
                });
            }
        }, true);
    }

    logError(errorInfo) {
        // Add to error queue
        this.errorQueue.push(errorInfo);
        
        // Keep only the most recent errors
        if (this.errorQueue.length > this.maxErrors) {
            this.errorQueue.shift();
        }

        // Log to console in development
        if (this.isDevelopment()) {
            console.error('Error logged:', errorInfo);
        }

        // Send to analytics/monitoring service if available
        this.sendToMonitoring(errorInfo);
    }

    logUserAction(action, details = {}) {
        const actionLog = {
            type: 'User Action',
            action: action,
            details: details,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        if (this.isDevelopment()) {
            console.log('User action:', actionLog);
        }
    }

    logPerformance(metric, value, details = {}) {
        const perfLog = {
            type: 'Performance',
            metric: metric,
            value: value,
            details: details,
            timestamp: new Date().toISOString()
        };

        if (this.isDevelopment()) {
            console.log('Performance metric:', perfLog);
        }
    }

    sendToMonitoring(errorInfo) {
        // Only send critical errors to avoid spam
        if (this.isCriticalError(errorInfo)) {
            // Here you could send to services like Sentry, LogRocket, etc.
            // For now, we'll just store locally
            try {
                const errors = JSON.parse(localStorage.getItem('pixelart-errors') || '[]');
                errors.push(errorInfo);
                
                // Keep only last 10 errors in localStorage
                if (errors.length > 10) {
                    errors.splice(0, errors.length - 10);
                }
                
                localStorage.setItem('pixelart-errors', JSON.stringify(errors));
            } catch (e) {
                // Ignore localStorage errors
            }
        }
    }

    isCriticalError(errorInfo) {
        const criticalPatterns = [
            /security/i,
            /auth/i,
            /network/i,
            /canvas/i,
            /save/i,
            /load/i
        ];

        return criticalPatterns.some(pattern => 
            pattern.test(errorInfo.message) || 
            pattern.test(errorInfo.type)
        );
    }

    isDevelopment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.hostname.includes('dev');
    }

    getErrorReport() {
        return {
            errors: this.errorQueue,
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString(),
            localStorage: this.getLocalStorageInfo()
        };
    }

    getLocalStorageInfo() {
        try {
            const keys = Object.keys(localStorage);
            return {
                keyCount: keys.length,
                totalSize: JSON.stringify(localStorage).length,
                pixelartKeys: keys.filter(key => key.startsWith('pixelart-'))
            };
        } catch (e) {
            return { error: 'Cannot access localStorage' };
        }
    }

    clearErrors() {
        this.errorQueue = [];
        try {
            localStorage.removeItem('pixelart-errors');
        } catch (e) {
            // Ignore
        }
    }
}

// Create global error handler instance
const globalErrorHandler = new ErrorHandler();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
}

// Make available globally for browser usage
if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
    window.globalErrorHandler = globalErrorHandler;
}