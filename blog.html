<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Blog - PixelArt Nexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
</head>
<body>
  <div id="fullWidthWrapper">
    <!-- Left Banner -->
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArt Nexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <!-- Main Content -->
    <div id="mainContainer">
      <div class="content-wrapper">
        <h1>PixelArt Nexus Blog</h1>
        <p class="intro-text">Development updates, pixel art tutorials, and behind-the-scenes insights</p>

        <!-- Blog Posts Container -->
        <div class="blog-posts-container">
          
          <!-- Blog Post 1 (Most Recent) -->
          <div class="blog-post">
            <h2 class="blog-title">How I Started Off</h2>
            <p class="blog-date">June 10, 2025</p>
            <div class="blog-content">
              <p>I started with a blank 64x64 canvas with a measly toolbar and just let my mind wander...</p>
              <p>Eventually, I landed on this advanced UI you see now. Here's what I learned.</p>
              <p>The process began with sketching out a basic layout in my mind. I wanted something that felt both modern and nostalgic, capturing the essence of classic pixel art while being functional for today's users.</p>
              <p>One of the biggest challenges was making sure the interface scaled properly across different screen sizes while maintaining it's aesthetic. I ended up creating multiple versions of each UI element at different resolutions.</p>
              <p>The color picker took a very long time to get right, and it still isn't perfect. I went through a couple iterations before picking the version you see today on the canvas page. That darn off-color eyedropper tool preset is still giving me trouble.</p>
              <p>I'm still learning as I go, and I'm always open to feedback and suggestions. If you have any ideas for future blog posts, feel free to reach out!</p>
            </div>
          </div>

          <!-- Blog Post 2 -->
          <div class="blog-post">
            <h2 class="blog-title">Welcome to PixelArtNexus! (Or as I like to call it, PAN)</h2>
            <p class="blog-date">Jun 10, 2025</p>
            <div class="blog-content">
              <p>Welcome to the very first blog post on PixelArtNexus! I'm excited to share this journey with you.</p>
              <p>This project started as a simple idea: create a pixel art editor that's both powerful and accessible to everyone. After weeks of development, we're finally ready to share it with the world.</p>
              <p>What makes PixelArt Nexus special? Here are a few key features:</p>
              <ul>
                <li>Intuitive brush tools with customizable sizes and shapes</li>
                <li>Layer support for complex artwork</li>
                <li>Real-time collaboration features (coming soon!)</li>
                <li>Cloud storage for your creations</li>
                <li>Export options for various formats and sizes</li>
              </ul>
              <p>But this is just the beginning. I have so many exciting features planned, including animation tools, community galleries, and much more!</p>
              <p>Thank you for being part of this journey. Your feedback and support mean everything to me as I continue to build and improve PAN.</p>
            </div>
          </div>

          <!-- Add new blog posts above this comment -->
          <!-- When adding new posts, copy the blog-post div structure and place it at the top -->
          
        </div>

        <!-- End Message -->
        <div class="blog-end-message">
          <p>That's all the blog posts so far, stay tuned for more updates and insights!</p>
          <p>Have suggestions for future blog topics? <a href="/contact.html">Let me know!</a></p>
        </div>

        <!-- Back to Home Button -->
        <div class="back-to-home">
          <a href="/" class="back-button">← Back to Home</a>
        </div>

        <!-- Footer -->
        <div class="bottom-toolbar">
          <div class="footer-section core-info">
            <a href="/about" class="footer-link">About</a>
            <a href="/contact" class="footer-link">Contact</a>
            <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
            <a href="/help" class="footer-link">Help / FAQ</a>
          </div>
          <div class="footer-section legal-policy">
            <a href="/terms" class="footer-link">Terms of Service</a>
            <a href="/privacy" class="footer-link">Privacy Policy</a>
          </div>
          <div class="footer-section community-social">
            <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
            <a href="/social" class="footer-link">Social Media</a>
            <a href="/blog" class="footer-link">Blog / Updates</a>
          </div>
          <div class="footer-section copyright">
            <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Banner -->
    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Login button (shown when not authenticated) -->
        <button id="authButton" class="auth-button">Login</button>

        <!-- Settings button (shown when not authenticated) -->
        <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

        <!-- Profile dropdown (shown when authenticated and verified) -->
        <div id="profileDropdown" class="profile-dropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div class="setting-item">
          <div>
            <label for="themeSelect">Theme:</label>
            <select id="themeSelect">
              <option value="light">Light Mode</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>
      </div>

      <!-- User Profile Settings (shown only for authenticated users) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div class="setting-item">
          <div>
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter username" maxlength="50">
          </div>
        </div>
        <div class="setting-item">
          <div>
            <label for="profileImageInput">Profile Picture:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
            <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
          </div>
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <script src="shared-layout.js"></script>
</body>
</html>
