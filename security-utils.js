// Security Utilities for PixelArt Nexus
// Comprehensive input validation and DoS prevention

// Security Configuration
const SECURITY_CONFIG = {
    // Input validation limits
    USERNAME_MAX_LENGTH: 50,
    LAYER_NAME_MAX_LENGTH: 50,
    CANVAS_NAME_MAX_LENGTH: 50,
    FILENAME_MAX_LENGTH: 100,
    
    // Resource limits
    MAX_CANVAS_SIZE: 1024,
    MAX_LAYERS: 20,
    MAX_CANVASES: 3,
    MAX_UNDO_STEPS: 100,
    MAX_STORAGE_SIZE_KB: 5000,
    MAX_FILE_SIZE_MB: 10,
    
    // API limits
    API_RATE_LIMIT_REQUESTS: 10,
    API_RATE_LIMIT_WINDOW_MS: 1000,
    API_REQUEST_TIMEOUT_MS: 10000,
    
    // Brush/tool limits
    MIN_BRUSH_SIZE: 1,
    MAX_BRUSH_SIZE: 100,
    MIN_OPACITY: 0,
    MAX_OPACITY: 100,
    MIN_ZOOM: 0.1,
    MAX_ZOOM: 50,
    
    // Color validation
    HEX_COLOR_PATTERN: /^#([0-9A-F]{3}){1,2}$/i,
    RGB_COLOR_PATTERN: /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/i,
    RGBA_COLOR_PATTERN: /^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(0|1|0?\.\d+)\s*\)$/i
};

// Input Validation Utility
class InputValidator {
    // Username validation
    static validateUsername(username) {
        if (!username || typeof username !== 'string') {
            return { valid: false, error: 'Username is required' };
        }
        
        if (username.length > SECURITY_CONFIG.USERNAME_MAX_LENGTH) {
            return { valid: false, error: `Username cannot exceed ${SECURITY_CONFIG.USERNAME_MAX_LENGTH} characters` };
        }
        
        // Allow letters, numbers, spaces, underscores, hyphens, and periods
        const usernamePattern = /^[a-zA-Z0-9 _\-\.]+$/;
        if (!usernamePattern.test(username)) {
            return { valid: false, error: 'Username can only contain letters, numbers, spaces, underscores, hyphens, and periods' };
        }
        
        // Prevent excessive whitespace
        if (username.trim() !== username || /\s{2,}/.test(username)) {
            return { valid: false, error: 'Username cannot have leading/trailing spaces or consecutive spaces' };
        }
        
        return { valid: true };
    }
    
    // Layer/Canvas name validation
    static validateLayerName(name) {
        if (!name || typeof name !== 'string') {
            return { valid: false, error: 'Name is required' };
        }
        
        if (name.length > SECURITY_CONFIG.LAYER_NAME_MAX_LENGTH) {
            return { valid: false, error: `Name cannot exceed ${SECURITY_CONFIG.LAYER_NAME_MAX_LENGTH} characters` };
        }
        
        // Prevent HTML/script injection
        if (/<[^>]*>/g.test(name)) {
            return { valid: false, error: 'Name cannot contain HTML tags' };
        }
        
        return { valid: true };
    }
    
    // Filename validation
    static validateFilename(filename) {
        if (!filename || typeof filename !== 'string') {
            return { valid: false, error: 'Filename is required' };
        }
        
        if (filename.length > SECURITY_CONFIG.FILENAME_MAX_LENGTH) {
            return { valid: false, error: `Filename cannot exceed ${SECURITY_CONFIG.FILENAME_MAX_LENGTH} characters` };
        }
        
        // Prevent path traversal and invalid characters
        const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
        if (invalidChars.test(filename)) {
            return { valid: false, error: 'Filename contains invalid characters' };
        }
        
        // Prevent reserved names (Windows)
        const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
        if (reservedNames.test(filename)) {
            return { valid: false, error: 'Filename uses a reserved name' };
        }
        
        return { valid: true };
    }
    
    // Color validation
    static validateHexColor(color) {
        if (!color || typeof color !== 'string') {
            return { valid: false, error: 'Color is required' };
        }
        
        if (!SECURITY_CONFIG.HEX_COLOR_PATTERN.test(color)) {
            return { valid: false, error: 'Invalid hex color format' };
        }
        
        return { valid: true };
    }
    
    // Numeric range validation
    static validateNumericRange(value, min, max, fieldName = 'Value') {
        const num = Number(value);
        
        if (isNaN(num)) {
            return { valid: false, error: `${fieldName} must be a number` };
        }
        
        if (num < min || num > max) {
            return { valid: false, error: `${fieldName} must be between ${min} and ${max}` };
        }
        
        return { valid: true, value: num };
    }
    
    // Canvas size validation
    static validateCanvasSize(width, height) {
        const widthValidation = this.validateNumericRange(width, 1, SECURITY_CONFIG.MAX_CANVAS_SIZE, 'Canvas width');
        if (!widthValidation.valid) return widthValidation;
        
        const heightValidation = this.validateNumericRange(height, 1, SECURITY_CONFIG.MAX_CANVAS_SIZE, 'Canvas height');
        if (!heightValidation.valid) return heightValidation;
        
        // Check total pixel count to prevent memory issues
        const totalPixels = widthValidation.value * heightValidation.value;
        const maxPixels = SECURITY_CONFIG.MAX_CANVAS_SIZE * SECURITY_CONFIG.MAX_CANVAS_SIZE;
        
        if (totalPixels > maxPixels) {
            return { valid: false, error: `Canvas size too large. Maximum total pixels: ${maxPixels}` };
        }
        
        return { valid: true, width: widthValidation.value, height: heightValidation.value };
    }
    
    // File upload validation
    static validateFileUpload(file) {
        if (!file) {
            return { valid: false, error: 'No file selected' };
        }
        
        // Check file size
        const maxSizeBytes = SECURITY_CONFIG.MAX_FILE_SIZE_MB * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            return { valid: false, error: `File size cannot exceed ${SECURITY_CONFIG.MAX_FILE_SIZE_MB}MB` };
        }
        
        // Check file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            return { valid: false, error: 'Invalid file type. Only PNG, JPEG, GIF, and WebP are allowed' };
        }
        
        // Validate filename
        const filenameValidation = this.validateFilename(file.name);
        if (!filenameValidation.valid) return filenameValidation;
        
        return { valid: true };
    }
}

// HTML Sanitization Utility
class HTMLSanitizer {
    static sanitizeText(text) {
        if (!text || typeof text !== 'string') return '';
        
        const element = document.createElement('div');
        element.textContent = text;
        return element.innerHTML;
    }
    
    static sanitizeAttribute(value) {
        if (!value || typeof value !== 'string') return '';
        
        return value
            .replace(/[<>"'&]/g, (match) => {
                const entities = {
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#x27;',
                    '&': '&amp;'
                };
                return entities[match];
            });
    }
}

// Rate Limiting Utility
class RateLimiter {
    constructor(maxRequests = SECURITY_CONFIG.API_RATE_LIMIT_REQUESTS, timeWindow = SECURITY_CONFIG.API_RATE_LIMIT_WINDOW_MS) {
        this.maxRequests = maxRequests;
        this.timeWindow = timeWindow;
        this.requestTimes = [];
    }
    
    canMakeRequest() {
        const now = Date.now();
        
        // Remove old requests outside the time window
        this.requestTimes = this.requestTimes.filter(time => now - time < this.timeWindow);
        
        if (this.requestTimes.length < this.maxRequests) {
            this.requestTimes.push(now);
            return true;
        }
        
        return false;
    }
    
    getTimeUntilNextRequest() {
        if (this.requestTimes.length === 0) return 0;
        
        const oldestRequest = Math.min(...this.requestTimes);
        const timeUntilExpiry = this.timeWindow - (Date.now() - oldestRequest);
        
        return Math.max(0, timeUntilExpiry);
    }
}

// Resource Management Utility
class ResourceManager {
    static validateLayerCount(currentCount) {
        if (currentCount >= SECURITY_CONFIG.MAX_LAYERS) {
            return { valid: false, error: `Maximum ${SECURITY_CONFIG.MAX_LAYERS} layers allowed` };
        }
        return { valid: true };
    }
    
    static validateCanvasCount(currentCount) {
        if (currentCount >= SECURITY_CONFIG.MAX_CANVASES) {
            return { valid: false, error: `Maximum ${SECURITY_CONFIG.MAX_CANVASES} canvases allowed` };
        }
        return { valid: true };
    }
    
    static validateUndoHistorySize(historyArray) {
        if (historyArray.length > SECURITY_CONFIG.MAX_UNDO_STEPS) {
            // Trim history to prevent memory issues
            historyArray.splice(0, historyArray.length - SECURITY_CONFIG.MAX_UNDO_STEPS);
        }
        return historyArray;
    }
    
    static validateStorageSize(data) {
        const serialized = JSON.stringify(data);
        const sizeKB = Math.round(serialized.length / 1024);
        
        if (sizeKB > SECURITY_CONFIG.MAX_STORAGE_SIZE_KB) {
            return { 
                valid: false, 
                error: `Data size (${sizeKB}KB) exceeds maximum allowed (${SECURITY_CONFIG.MAX_STORAGE_SIZE_KB}KB)`,
                actualSize: sizeKB
            };
        }
        
        return { valid: true, size: sizeKB };
    }
}

// Secure API Request Utility
class SecureAPIClient {
    constructor() {
        this.rateLimiter = new RateLimiter();
    }
    
    async makeRequest(url, options = {}) {
        // Check rate limit
        if (!this.rateLimiter.canMakeRequest()) {
            const waitTime = this.rateLimiter.getTimeUntilNextRequest();
            throw new Error(`Rate limit exceeded. Please wait ${Math.ceil(waitTime / 1000)} seconds before trying again.`);
        }
        
        // Set up request timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), SECURITY_CONFIG.API_REQUEST_TIMEOUT_MS);
        
        // Default secure headers
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            signal: controller.signal
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, finalOptions);
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('Request timed out. Please try again.');
            }
            
            throw error;
        }
    }
    
    async makeJSONRequest(url, options = {}) {
        const response = await this.makeRequest(url, options);
        return await response.json();
    }
}

// Secure Storage Utility
class SecureStorage {
    static async saveToStorage(key, data) {
        // Validate storage size
        const sizeValidation = ResourceManager.validateStorageSize(data);
        if (!sizeValidation.valid) {
            throw new Error(sizeValidation.error);
        }
        
        const serialized = JSON.stringify(data);
        
        try {
            localStorage.setItem(key, serialized);
            return { success: true, size: sizeValidation.size };
        } catch (error) {
            console.error('localStorage error:', error);
            
            // Fallback to IndexedDB
            try {
                await this.saveToIndexedDB(key, data);
                return { success: true, size: sizeValidation.size, fallback: 'indexeddb' };
            } catch (dbError) {
                console.error('IndexedDB error:', dbError);
                throw new Error('Failed to save data. Storage may be full or unavailable.');
            }
        }
    }
    
    static async saveToIndexedDB(key, data) {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('PixelArtNexus', 1);
            
            request.onerror = () => reject(request.error);
            
            request.onsuccess = () => {
                const db = request.result;
                const transaction = db.transaction(['storage'], 'readwrite');
                const store = transaction.objectStore('storage');
                
                const putRequest = store.put({ key, data });
                putRequest.onsuccess = () => resolve();
                putRequest.onerror = () => reject(putRequest.error);
            };
            
            request.onupgradeneeded = () => {
                const db = request.result;
                if (!db.objectStoreNames.contains('storage')) {
                    db.createObjectStore('storage', { keyPath: 'key' });
                }
            };
        });
    }
    
    static getFromStorage(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return null;
        }
    }
}

// Global security instances
const globalRateLimiter = new RateLimiter();
const secureAPIClient = new SecureAPIClient();

// Export utilities for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SECURITY_CONFIG,
        InputValidator,
        HTMLSanitizer,
        RateLimiter,
        ResourceManager,
        SecureAPIClient,
        SecureStorage,
        globalRateLimiter,
        secureAPIClient
    };
}

// Make available globally for browser usage
if (typeof window !== 'undefined') {
    window.SecurityUtils = {
        SECURITY_CONFIG,
        InputValidator,
        HTMLSanitizer,
        RateLimiter,
        ResourceManager,
        SecureAPIClient,
        SecureStorage,
        globalRateLimiter,
        secureAPIClient
    };
}