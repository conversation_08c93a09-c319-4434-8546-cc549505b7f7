<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>About | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3586346747453362"
       crossorigin="anonymous"></script>
  <style>
    .info-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      margin: 30px 0;
    }

    .goals-card {
      grid-column: 1 / -1;
      max-width: 400px;
      margin: 20px auto 0 auto;
    }

    .info-card {
      background-color: #f9f9f9;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #2c5aa0;
    }

    .info-card h3 {
      margin-top: 0;
      color: #2c5aa0;
    }

    .feature-list {
      list-style-type: none;
      padding: 0;
    }

    .feature-list li {
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
    }

    .feature-list li:before {
      content: "✓ ";
      color: #2c5aa0;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="title.webp" alt="PixelArtNexus" class="logo">
        <h1>About PixelArtNexus</h1>
      </div>
    
    <div class="info-grid">
      <div class="info-card">
        <h3>🎨 Purpose</h3>
        <p>PixelArtNexus is a comprehensive web-based pixel art editor designed to provide artists, game developers, and creative enthusiasts with professional-grade tools for creating stunning pixel art directly in their browser.</p>
      </div>
      
      <div class="info-card">
        <h3>👨‍💻 Creator</h3>
        <p>Developed with passion by a dedicated creator, me :),  focused on making pixel art creation accessible to everyone, from beginners to professional artists.</p>
      </div>
      
      <div class="info-card">
        <h3>📅 Created</h3>
        <p>Project initiated: May 2025<br>
        Continuously updated and improved based on user feedback and emerging needs in the pixel art community.</p>
      </div>
      
      <div class="info-card goals-card">
        <h3>🎯 Goals</h3>
        <p>To democratize pixel art creation by providing free, powerful, and intuitive tools that rival desktop applications while being accessible from any device with a web browser.</p>
      </div>
    </div>
    
    <h2>Key Features</h2>
    <ul class="feature-list">
      <li>Multi-layer support with opacity controls</li>
      <li>Multiple canvas management</li>
      <li>Comprehensive drawing tools (draw, erase, fill, line, rectangle, circle)</li>
      <li>Advanced selection and copy/paste functionality</li>
      <li>Customizable brush sizes and opacity</li>
      <li>Zoom and pan capabilities</li>
      <li>Grid overlay options</li>
      <li>Undo/redo functionality</li>
      <li>Dark/light theme support</li>
      <li>Customizable keyboard shortcuts</li>
      <li>User authentication and settings persistence</li>
      <li>High-quality PNG export</li>
    </ul>
    
    <h2>Our Mission</h2>
    <p>We believe that creativity should not be limited by software costs or device capabilities. PixelArtNexus aims to:</p>
    <ul>
      <li><strong>Empower Artists:</strong> Provide professional-grade tools without the barrier of expensive software</li>
      <li><strong>Foster Community:</strong> Create a platform where pixel artists can create, share, and learn</li>
      <li><strong>Innovate Continuously:</strong> Regularly add new features based on user needs and technological advances</li>
      <li><strong>Maintain Accessibility:</strong> Ensure the platform works across all devices and skill levels</li>
    </ul>
    
    <h2>Technology</h2>
    <p>Built with modern web technologies including HTML5 Canvas, JavaScript, and CSS3, PixelArtNexus leverages the power of the web platform to deliver a seamless, responsive experience that works on desktop and mobile devices alike.</p>

    <a href="/" class="back-link">← Back to Home</a>

    <!-- Footer -->
    <div class="bottom-toolbar">
      <div class="footer-section core-info">
        <a href="/about" class="footer-link">About</a>
        <a href="/contact" class="footer-link">Contact</a>
        <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
        <a href="/help" class="footer-link">Help / FAQ</a>
      </div>
      <div class="footer-section legal-policy">
        <a href="/terms" class="footer-link">Terms of Service</a>
        <a href="/privacy" class="footer-link">Privacy Policy</a>
      </div>
      <div class="footer-section community-social">
        <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
        <a href="/social" class="footer-link">Social Media</a>
        <a href="/blog" class="footer-link">Blog / Updates</a>
      </div>
      <div class="footer-section copyright">
        <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
      </div>
    </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Login button (shown when not authenticated) -->
        <button id="authButton" class="auth-button">Login</button>

        <!-- Settings button (shown when not authenticated) -->
        <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

        <!-- Profile dropdown (shown when authenticated and verified) -->
        <div id="profileDropdown" class="profile-dropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div class="setting-item">
          <div>
            <label for="themeSelect">Theme:</label>
            <select id="themeSelect">
              <option value="light">Light Mode</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>
      </div>

      <!-- User Profile Settings (shown only for authenticated users) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div class="setting-item">
          <div>
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter username" maxlength="50">
          </div>
        </div>
        <div class="setting-item">
          <div>
            <label for="profileImageInput">Profile Picture:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
            <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
          </div>
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <script src="shared-layout.js"></script>
</body>
</html>
