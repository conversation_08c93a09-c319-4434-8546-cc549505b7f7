<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Username Availability Test.</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        input[type="text"] {
            width: 300px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        input:invalid {
            border-color: #ff6b6b;
            background-color: #ffe6e6;
        }
        input:valid {
            border-color: #51cf66;
            background-color: #e6ffe6;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .available {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .taken {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .error {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .loading {
            background-color: #cce7ff;
            color: #004085;
            border: 1px solid #99d6ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Username Availability Test</h1>
        <p>This page tests the username availability checking with your Xano backend.</p>
        
        <div class="test-section">
            <h2>Real-time Username Check</h2>
            <p>Type a username below. It will check availability after 1 second of no typing:</p>
            <input type="text" id="usernameTest" placeholder="Enter username to test" maxlength="50">
            <div id="realtimeResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>Manual API Test</h2>
            <p>Test the API directly:</p>
            <input type="text" id="manualUsername" placeholder="Username to check manually">
            <button onclick="testUsernameAPI()">Check Availability</button>
            <div id="manualResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>Expected Xano Response Format</h2>
            <p><strong>Available username:</strong> <code>[]</code></p>
            <p><strong>Taken username:</strong> <code>[{"username": "existinguser"}]</code></p>
        </div>
        
        <div class="test-section">
            <h2>Test Results Log</h2>
            <div id="logContainer" style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                <div>Waiting for tests...</div>
            </div>
        </div>
    </div>

    <script>
        // Xano Configuration (same as your app)
        const XANO_CONFIG = {
            baseURL: 'https://x8ki-letl-twmt.n7.xano.io/api:YVvnFfHl',
            endpoints: {
                checkUsernameAvailability: '/check-username-ability'
            }
        };

        // Logging function
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#6c757d';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Username availability check function (same as your app)
        async function checkUsernameAvailability(username) {
            try {
                log(`🔍 Checking username availability: "${username}"`);
                
                const url = `${XANO_CONFIG.baseURL}${XANO_CONFIG.endpoints.checkUsernameAvailability}?username=${encodeURIComponent(username)}`;
                log(`📡 API URL: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                log(`📥 Raw API response: ${JSON.stringify(result)}`);
                
                // Parse Xano response format:
                // [] = username available
                // [{username: "name"}] = username taken
                const isAvailable = Array.isArray(result) && result.length === 0;
                
                const finalResult = {
                    available: isAvailable,
                    message: isAvailable ? 'Username is available' : 'Username already taken'
                };
                
                log(`✅ Parsed result: ${JSON.stringify(finalResult)}`, 'success');
                return finalResult;
            } catch (error) {
                log(`❌ Error checking username: ${error.message}`, 'error');
                return {
                    available: false,
                    message: `Error: ${error.message}`,
                    error: true
                };
            }
        }

        // Real-time username checking
        let usernameCheckTimeout = null;
        const usernameInput = document.getElementById('usernameTest');
        const realtimeResult = document.getElementById('realtimeResult');

        usernameInput.addEventListener('input', (e) => {
            const username = e.target.value.trim();
            
            // Clear previous timeout
            if (usernameCheckTimeout) {
                clearTimeout(usernameCheckTimeout);
            }
            
            // Hide result while typing
            realtimeResult.style.display = 'none';
            
            // Basic validation
            if (username.length > 50) {
                e.target.setCustomValidity('Username cannot exceed 50 characters');
                return;
            } else {
                e.target.setCustomValidity('');
            }
            
            // Check availability after delay
            if (username) {
                realtimeResult.textContent = 'Checking...';
                realtimeResult.className = 'result loading';
                realtimeResult.style.display = 'block';
                
                usernameCheckTimeout = setTimeout(async () => {
                    const result = await checkUsernameAvailability(username);
                    
                    if (result.error) {
                        realtimeResult.textContent = result.message;
                        realtimeResult.className = 'result error';
                        e.target.setCustomValidity(result.message);
                    } else if (result.available) {
                        realtimeResult.textContent = '✅ ' + result.message;
                        realtimeResult.className = 'result available';
                        e.target.setCustomValidity('');
                    } else {
                        realtimeResult.textContent = '❌ ' + result.message;
                        realtimeResult.className = 'result taken';
                        e.target.setCustomValidity(result.message);
                    }
                }, 1000);
            }
        });

        // Manual API test
        async function testUsernameAPI() {
            const manualUsername = document.getElementById('manualUsername').value.trim();
            const manualResult = document.getElementById('manualResult');
            
            if (!manualUsername) {
                alert('Please enter a username to test');
                return;
            }
            
            manualResult.textContent = 'Checking...';
            manualResult.className = 'result loading';
            manualResult.style.display = 'block';
            
            const result = await checkUsernameAvailability(manualUsername);
            
            if (result.error) {
                manualResult.textContent = result.message;
                manualResult.className = 'result error';
            } else if (result.available) {
                manualResult.textContent = '✅ ' + result.message;
                manualResult.className = 'result available';
            } else {
                manualResult.textContent = '❌ ' + result.message;
                manualResult.className = 'result taken';
            }
        }

        // Initialize
        log('🚀 Username availability test page loaded');
        log(`🔧 Using Xano endpoint: ${XANO_CONFIG.baseURL}${XANO_CONFIG.endpoints.checkUsernameAvailability}`);
    </script>
</body>
</html>