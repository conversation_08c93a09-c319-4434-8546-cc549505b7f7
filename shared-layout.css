/* Shared Layout CSS for About, Contact, Help pages */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  color: #333;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Three-column layout */
#fullWidthWrapper {
  display: flex;
  flex-direction: row;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
}

.banner-div {
  flex: 1;
  background-color: transparent;
  border: none;
  min-width: 200px;
  box-sizing: border-box;
}

#leftBanner {
  border-left: none;
  display: flex;
  flex-direction: column;
  margin-right: 5px;
  position: relative;
}

#rightBanner {
  border-left: none;
  display: flex;
  flex-direction: column;
  margin-left: 5px;
  position: relative;
}

/* Main content area */
#mainContainer {
  flex: 0 0 auto;
  width: 900px;
  max-width: 900px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-radius: 8px;
  margin: 40px 0;
  padding: 20px;
  position: relative;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Left toolbar with logo */
.left-toolbar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 52px;
  flex-shrink: 0;
  opacity: 1;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.left-toolbar:hover {
  opacity: 0.8;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.title-image {
  max-width: 180px;
  height: auto;
  max-height: 40px;
  object-fit: contain;
  transition: filter 0.3s ease;
}

/* Right toolbar with auth buttons */
.right-toolbar {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 52px;
  flex-shrink: 0;
  opacity: 1;
  gap: 6px;
}

/* Banner images */
.banner-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 5px;
  margin-bottom: 5px;
  overflow: hidden;
  border-radius: 3px;
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 3px;
  transition: filter 0.3s ease;
}

/* Banner ad container styling */
.banner-ad-container {
  position: absolute;
  top: 57px; /* Position below toolbar */
  left: 5px;
  right: 5px;
  bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; /* Fully transparent */
  overflow: hidden;
  border-radius: 3px;
  z-index: 10; /* Above banner image */
  pointer-events: none; /* Allow clicks to pass through to banner image */
}

.banner-ad-container .adsbygoogle {
  width: 100%;
  height: 100%;
  border-radius: 3px;
  pointer-events: auto; /* Re-enable clicks for the ad itself */
}

/* Auth buttons styling */
.auth-button, .settings-button {
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-radius: 3px;
  background-color: #f5f5f5;
  color: #333;
  cursor: pointer;
  font-size: 0.85em;
  min-width: 50px;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.auth-button:hover, .settings-button:hover {
  background-color: #e0e0e0;
  border-color: #999;
}

.auth-button:active, .settings-button:active {
  background-color: #d0d0d0;
}

/* Profile dropdown styling */
.profile-dropdown {
  position: relative;
  display: inline-block;
}

.profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid #ccc;
  transition: border-color 0.2s ease;
}

.profile-avatar:hover {
  border-color: #999;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 1000;
  min-width: 150px;
  margin-top: 2px;
}

.profile-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
}

.profile-menu-item:last-child {
  border-bottom: none;
}

.profile-menu-item:hover {
  background-color: #f5f5f5;
}

/* Header styling */
.header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.logo {
  max-width: 300px;
  height: auto;
  margin-bottom: 20px;
}

h1 {
  color: #2c5aa0;
  margin: 0;
  font-size: 2.5em;
}

h2 {
  color: #2c5aa0;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
  margin-top: 30px;
}

/* Back link styling */
.back-link {
  display: inline-block;
  margin-top: 30px;
  padding: 10px 20px;
  background-color: #2c5aa0;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.back-link:hover {
  background-color: #1e3a70;
}

/* Settings Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.modal-content {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close-btn:hover {
  color: #333;
}

.settings-section {
  margin: 20px 0;
}

.settings-section h3 {
  margin: 0 0 15px 0;
  color: #2c5aa0;
  font-size: 1.1em;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

.setting-item label {
  font-weight: bold;
  margin-right: 10px;
}

.setting-item select,
.setting-item input[type="text"] {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  min-width: 150px;
}

.setting-item input[type="file"] {
  padding: 3px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  width: 100%;
}

.setting-item > div {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.modal-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.modal-button.primary {
  background-color: #2c5aa0;
  color: white;
}

.modal-button.primary:hover {
  background-color: #1e3f73;
}

.modal-button.secondary {
  background-color: #f5f5f5;
  color: #333;
}

.modal-button.secondary:hover {
  background-color: #e0e0e0;
}

/* Dark theme modal styles */
body.dark-theme .modal-content {
  background-color: #2a2a2a;
  color: #e0e0e0;
}

body.dark-theme .modal-close-btn {
  color: #ccc;
}

body.dark-theme .modal-close-btn:hover {
  color: #fff;
}

body.dark-theme .settings-section h3 {
  color: #6db3f2;
}

body.dark-theme .setting-item select,
body.dark-theme .setting-item input[type="text"],
body.dark-theme .setting-item input[type="file"] {
  background-color: #3a3a3a;
  color: #e0e0e0;
  border-color: #555;
}

body.dark-theme .modal-button.secondary {
  background-color: #3a3a3a;
  color: #e0e0e0;
}

body.dark-theme .modal-button.secondary:hover {
  background-color: #4a4a4a;
}

/* Notification system */
.notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #4CAF50;
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-size: 14px;
}

.notification.show {
  opacity: 1;
}

.notification.error {
  background-color: #f44336;
}

/* Social Media Page Styles */
.social-links-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 60px;
}

.social-link-card {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.social-link-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.social-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
  flex-shrink: 0;
}

.social-icon svg {
  width: 32px;
  height: 32px;
  color: white;
}

.social-icon.youtube {
  background: linear-gradient(135deg, #ff0000, #cc0000);
}

.social-icon.twitter {
  background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.social-icon.instagram {
  background: linear-gradient(135deg, #e4405f, #833ab4, #fcb045);
}

.social-icon.tiktok {
  background: linear-gradient(135deg, #000000, #ff0050);
}

.social-icon.discord {
  background: linear-gradient(135deg, #5865f2, #4752c4);
}

.social-info {
  flex: 1;
}

.social-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.4em;
  font-weight: 600;
}

.social-info p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.social-button {
  display: inline-block;
  padding: 10px 20px;
  background: #2c5aa0;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.social-button:hover {
  background: #1e3f73;
}

/* Support Section */
.support-section {
  text-align: center;
  padding: 40px 0;
  border-top: 2px solid #eee;
}

.support-section h2 {
  color: #2c5aa0;
  margin-bottom: 20px;
  font-size: 2em;
}

.support-text {
  font-size: 1.1em;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.support-card {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff5f5f, #ff9500);
  border-radius: 16px;
  padding: 32px;
  max-width: 500px;
  margin: 0 auto;
  box-shadow: 0 8px 24px rgba(255, 95, 95, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.support-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(255, 95, 95, 0.4);
}

.kofi-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
  flex-shrink: 0;
}

.kofi-icon svg {
  width: 32px;
  height: 32px;
  color: white;
}

.support-info {
  text-align: left;
  flex: 1;
}

.support-info h3 {
  margin: 0 0 8px 0;
  color: white;
  font-size: 1.4em;
  font-weight: 600;
}

.support-info p {
  margin: 0 0 16px 0;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.kofi-button {
  display: inline-block;
  padding: 12px 24px;
  background: white;
  color: #ff5f5f;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.kofi-button:hover {
  background: #f8f8f8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.kofi-text {
  font-size: 1.1em;
}

/* Responsive design */
@media (max-width: 1400px) {
  .banner-div {
    min-width: 150px;
  }
  
  #mainContainer {
    width: 800px;
    max-width: 800px;
  }
}

@media (max-width: 1200px) {
  .banner-div {
    min-width: 100px;
  }
  
  #mainContainer {
    width: 700px;
    max-width: 700px;
  }
  
  .auth-button, .settings-button {
    padding: 4px 6px;
    font-size: 0.75em;
    min-width: 40px;
  }
}

@media (max-width: 1000px) {
  #fullWidthWrapper {
    flex-direction: column;
  }
  
  .banner-div {
    min-width: auto;
    height: auto;
  }
  
  #mainContainer {
    width: 100%;
    max-width: 100%;
    margin: 20px;
    border-radius: 0;
  }
  
  .left-toolbar, .right-toolbar {
    height: auto;
    padding: 10px;
  }
  
  .banner-image-container,
  .banner-ad-container {
    display: none;
  }
}

/* Dark theme styles */
body.dark-theme {
  background-color: #1a1a1a;
  color: #e0e0e0;
}

body.dark-theme .banner-div {
  background-color: #1a1a1a;
}

body.dark-theme #mainContainer {
  background-color: #2d2d2d;
  box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  color: #e0e0e0;
}

body.dark-theme .title-image,
body.dark-theme .banner-image {
  filter: invert(1) hue-rotate(180deg);
}

/* Dark theme for main page logos (center logos on about/contact/help pages) */
body.dark-theme .header .logo,
body.dark-theme img.logo {
  filter: invert(1) hue-rotate(180deg) !important;
}

body.dark-theme .auth-button,
body.dark-theme .settings-button {
  background-color: #4d4d4d;
  border-color: #666;
  color: #e0e0e0;
}

body.dark-theme .auth-button:hover,
body.dark-theme .settings-button:hover {
  background-color: #5d5d5d;
  border-color: #777;
}

body.dark-theme .auth-button:active,
body.dark-theme .settings-button:active {
  background-color: #3d3d3d;
}

body.dark-theme .profile-avatar {
  border-color: #666;
}

body.dark-theme .profile-avatar:hover {
  border-color: #777;
}

body.dark-theme .profile-menu {
  background-color: #3d3d3d;
  border-color: #666;
  box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

body.dark-theme .profile-menu-item {
  border-bottom-color: #555;
  color: #e0e0e0;
}

body.dark-theme .profile-menu-item:hover {
  background-color: #4d4d4d;
}

body.dark-theme .header {
  border-bottom-color: #555;
}

body.dark-theme h1,
body.dark-theme h2 {
  color: #6db3f2;
}

body.dark-theme h2 {
  border-bottom-color: #555;
}

body.dark-theme .back-link {
  background-color: #6db3f2;
  color: #1a1a1a;
}

body.dark-theme .back-link:hover {
  background-color: #5a9fd9;
}

/* Dark theme for page-specific elements */
body.dark-theme .info-card,
body.dark-theme .contact-info,
body.dark-theme .faq-item,
body.dark-theme .tool-card {
  background-color: #3d3d3d;
  border-left-color: #6db3f2;
  color: #e0e0e0;
}

body.dark-theme .info-card h3,
body.dark-theme .tool-card h4,
body.dark-theme .faq-question {
  color: #6db3f2;
}

body.dark-theme .email {
  background-color: #3d3d3d;
  border-color: #6db3f2;
  color: #e0e0e0;
}

body.dark-theme .email a {
  color: #6db3f2;
}

body.dark-theme .disclaimer {
  background-color: #4a4a2a;
  border-color: #666633;
  color: #e0e0e0;
}

body.dark-theme .disclaimer h3 {
  color: #cccc99;
}

body.dark-theme .shortcut-table th {
  background-color: #6db3f2;
  color: #1a1a1a;
}

body.dark-theme .shortcut-table td,
body.dark-theme .shortcut-table th {
  border-color: #555;
}

body.dark-theme .shortcut-table tr:nth-child(even) {
  background-color: #3d3d3d;
}

body.dark-theme .kbd {
  background-color: #4d4d4d;
  border-color: #666;
  color: #e0e0e0;
}

body.dark-theme .feature-list li,
body.dark-theme .contact-reasons li {
  border-bottom-color: #555;
}

body.dark-theme .feature-list li:before {
  color: #6db3f2;
}

body.dark-theme a {
  color: #6db3f2;
}

body.dark-theme a:hover {
  color: #5a9fd9;
}

/* Footer styling */
.bottom-toolbar {
  margin: 40px auto 0 auto;
  padding: 8px 16px;
  background-color: #e8e8e8;
  border: 1px solid #ccc;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 0.8em;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.footer-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-section.core-info {
  flex: 2;
}

.footer-section.legal-policy {
  flex: 1;
}

.footer-section.community-social {
  flex: 2;
}

.footer-section.copyright {
  flex: 1;
  justify-content: flex-end;
}

.footer-link {
  color: #555;
  text-decoration: none;
  font-size: 0.75em;
  padding: 2px 6px;
  border-radius: 2px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.footer-link:hover {
  color: #2c5aa0;
  background-color: #f0f0f0;
  text-decoration: underline;
}

.copyright-text {
  font-size: 0.75em;
  color: #777;
  white-space: nowrap;
}

/* Dark theme footer styles */
body.dark-theme .bottom-toolbar {
  background-color: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
}

body.dark-theme .footer-link {
  color: #b0b0b0;
}

body.dark-theme .footer-link:hover {
  color: #6db3f2;
  background-color: #333;
}

body.dark-theme .copyright-text {
  color: #888;
}

/* Responsive footer design */
@media (max-width: 600px) {
  .bottom-toolbar {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .footer-section {
    flex-direction: column;
    gap: 4px;
  }

  .footer-section.copyright {
    justify-content: center;
  }
}

/* Blog Page Specific Styles */
.content-wrapper {
  max-width: 900px;
  margin: 0 auto;
  padding: 40px 20px;
}

.content-wrapper h1 {
  text-align: center;
  color: #2c5aa0;
  margin-bottom: 20px;
  font-size: 2.5em;
}

.intro-text {
  text-align: center;
  font-size: 1.2em;
  color: #666;
  margin-bottom: 50px;
  line-height: 1.6;
}

/* Blog Posts Container */
.blog-posts-container {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-bottom: 60px;
}

/* Individual Blog Post */
.blog-post {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid #f0f0f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.blog-post:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.blog-title {
  color: #2c5aa0;
  font-size: 1.8em;
  font-weight: 600;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.blog-date {
  color: #888;
  font-size: 0.95em;
  margin: 0 0 24px 0;
  font-style: italic;
  border-bottom: 2px solid #eee;
  padding-bottom: 16px;
}

.blog-content {
  line-height: 1.7;
  color: #333;
}

.blog-content p {
  margin: 0 0 16px 0;
  font-size: 1.05em;
}

.blog-content p:last-child {
  margin-bottom: 0;
}

.blog-content ul {
  margin: 16px 0;
  padding-left: 24px;
}

.blog-content li {
  margin: 8px 0;
  font-size: 1.05em;
}

.blog-content a {
  color: #2c5aa0;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-bottom-color 0.2s ease;
}

.blog-content a:hover {
  border-bottom-color: #2c5aa0;
}

/* Blog End Message */
.blog-end-message {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  border: 2px solid #dee2e6;
}

.blog-end-message p {
  margin: 0 0 12px 0;
  font-size: 1.1em;
  color: #666;
}

.blog-end-message p:last-child {
  margin-bottom: 0;
}

.blog-end-message a {
  color: #2c5aa0;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: border-bottom-color 0.2s ease;
}

.blog-end-message a:hover {
  border-bottom-color: #2c5aa0;
}

/* Back to Home Button */
.back-to-home {
  text-align: center;
  margin: 40px 0 20px 0;
}

.back-button {
  display: inline-block;
  padding: 12px 24px;
  background-color: #2c5aa0;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: #1e3f73;
}

/* Dark Theme Styles for Blog */
body.dark-theme .content-wrapper h1 {
  color: #6db3f2;
}

body.dark-theme .intro-text {
  color: #ccc;
}

body.dark-theme .blog-post {
  background: #2a2a2a;
  border-color: #444;
}

body.dark-theme .blog-title {
  color: #6db3f2;
}

body.dark-theme .blog-date {
  color: #aaa;
  border-bottom-color: #444;
}

body.dark-theme .blog-content {
  color: #e0e0e0;
}

body.dark-theme .blog-content a {
  color: #6db3f2;
}

body.dark-theme .blog-content a:hover {
  border-bottom-color: #6db3f2;
}

body.dark-theme .blog-end-message {
  background: linear-gradient(135deg, #3a3a3a, #2a2a2a);
  border-color: #444;
}

body.dark-theme .blog-end-message p {
  color: #ccc;
}

body.dark-theme .blog-end-message a {
  color: #6db3f2;
}

body.dark-theme .blog-end-message a:hover {
  border-bottom-color: #6db3f2;
}

body.dark-theme .back-button {
  background-color: #6db3f2;
  color: #1a1a1a;
}

body.dark-theme .back-button:hover {
  background-color: #5a9fd9;
}

/* Social Media Page Specific Styles */
.social-links-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 60px;
}

.social-link-card {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 2px solid transparent;
}

.social-link-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.social-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
  flex-shrink: 0;
}

.social-icon svg {
  width: 32px;
  height: 32px;
  color: white;
}

.social-icon.youtube {
  background: linear-gradient(135deg, #ff0000, #cc0000);
}

.social-icon.twitter {
  background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.social-icon.tiktok {
  background: linear-gradient(135deg, #000000, #ff0050);
}

.social-icon.discord {
  background: linear-gradient(135deg, #5865f2, #4752c4);
}

.social-info {
  flex: 1;
}

.social-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.4em;
  font-weight: 600;
}

.social-info p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.social-button {
  display: inline-block;
  padding: 10px 20px;
  background: #2c5aa0;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.social-button:hover {
  background: #1e3f73;
}

/* Dark Theme Styles for Social Media */
body.dark-theme .social-link-card {
  background: #2a2a2a;
  border-color: #444;
}

body.dark-theme .social-info h3 {
  color: #e0e0e0;
}

body.dark-theme .social-info p {
  color: #ccc;
}

/* Mobile Responsive for Blog and Social */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 20px 15px;
  }

  .content-wrapper h1 {
    font-size: 2em;
  }

  .blog-post {
    padding: 24px 20px;
  }

  .blog-title {
    font-size: 1.5em;
  }

  .blog-content p,
  .blog-content li {
    font-size: 1em;
  }

  .blog-end-message {
    padding: 24px 16px;
  }

  .social-link-card {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }

  .social-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .support-card {
    flex-direction: column;
    text-align: center;
    padding: 24px;
  }

  .kofi-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .support-info {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 15px 10px;
  }

  .blog-post {
    padding: 20px 16px;
  }

  .blog-title {
    font-size: 1.3em;
  }

  .intro-text {
    font-size: 1.1em;
  }
}
