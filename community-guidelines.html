<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Community Guidelines | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3586346747453362"
       crossorigin="anonymous"></script>
  <style>
    .guidelines-section {
      margin: 25px 0;
    }

    .guidelines-section h2 {
      color: #2c5aa0;
      margin-bottom: 15px;
      font-size: 1.4em;
      border-bottom: 2px solid #e0e0e0;
      padding-bottom: 8px;
    }

    .guidelines-section h3 {
      color: #2c5aa0;
      margin: 20px 0 10px 0;
      font-size: 1.1em;
    }

    .guidelines-section p {
      margin: 12px 0;
      line-height: 1.6;
    }

    .guidelines-section ul {
      margin: 12px 0;
      padding-left: 25px;
    }

    .guidelines-section li {
      margin: 8px 0;
      line-height: 1.5;
    }

    .effective-date {
      background-color: #f0f8ff;
      padding: 15px;
      border-left: 4px solid #2c5aa0;
      margin: 20px 0;
      border-radius: 4px;
    }

    .welcome-box {
      background-color: #e8f5e8;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      border-left: 4px solid #28a745;
    }

    .warning-box {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }

    .prohibited-content {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }

    .enforcement-levels {
      background-color: #fff3cd;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
      border-left: 4px solid #ffc107;
    }

    .contact-info {
      background-color: #e8f5e8;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }
    
    /* Zero-tolerance notice styling */
    .zero-tolerance-notice {
      background-color: #f44336;
      border: 2px solid #d32f2f;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
      position: relative;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .zero-tolerance-notice:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(244, 67, 54, 0.5);
    }
    
    .zero-tolerance-notice h3 {
      color: white !important;
      margin-top: 0;
      font-size: 1.4em !important;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid rgba(255, 255, 255, 0.3);
      padding-bottom: 10px;
    }
    
    .zero-tolerance-notice p, 
    .zero-tolerance-notice ul, 
    .zero-tolerance-notice li {
      color: white;
      font-weight: 500;
    }
    
    .zero-tolerance-notice p strong {
      font-weight: 700;
      font-size: 1.1em;
    }
    
    .zero-tolerance-notice ul {
      background-color: rgba(0, 0, 0, 0.2);
      padding: 15px 15px 15px 35px;
      border-radius: 6px;
    }
    
    .zero-tolerance-notice li {
      margin: 10px 0;
    }

    /* Dark theme styles */
    body.dark-theme .guidelines-section h2 {
      color: #6db3f2;
      border-bottom-color: #555;
    }

    body.dark-theme .guidelines-section h3 {
      color: #6db3f2;
    }

    body.dark-theme .effective-date {
      background-color: #1a2332;
      border-left-color: #6db3f2;
    }

    body.dark-theme .welcome-box {
      background-color: #1a2e1a;
      border-left-color: #28a745;
      color: #e0e0e0;
    }

    body.dark-theme .warning-box {
      background-color: #332a1a;
      border-color: #b8860b;
      color: #e0e0e0;
    }

    body.dark-theme .prohibited-content {
      background-color: #2d1a1a;
      border-color: #8b0000;
      color: #e0e0e0;
    }

    body.dark-theme .enforcement-levels {
      background-color: #332a1a;
      border-left-color: #ffc107;
      color: #e0e0e0;
    }

    body.dark-theme .contact-info {
      background-color: #1a2e1a;
      color: #e0e0e0;
    }
    
    body.dark-theme .zero-tolerance-notice {
      background-color: #b71c1c;
      border-color: #7f0000;
    }
    
    body.dark-theme .zero-tolerance-notice ul {
      background-color: rgba(0, 0, 0, 0.4);
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="title.webp" alt="PixelArtNexus" class="logo">
        <h1>Community Guidelines</h1>
      </div>

      <div class="effective-date">
        <strong>Effective Date:</strong> January 2025<br>
        <strong>Last Updated:</strong> January 2025
      </div>

      <div class="welcome-box">
        <p><strong>Welcome to the PixelArtNexus Community!</strong></p>
        <p>Our community is built on creativity, respect, and shared passion for pixel art. These guidelines help ensure everyone can enjoy a safe, inspiring, and welcoming environment to create, share, and discover amazing pixel art.</p>
      </div>

      <div class="guidelines-section">
        <h2>1. Community Overview</h2>
        <p>PixelArtNexus offers a vibrant social platform where artists can:</p>
        <ul>
          <li><strong>Share artwork</strong> in public galleries and personal account pages</li>
          <li><strong>Create and manage</strong> custom galleries for themed collections</li>
          <li><strong>Connect with others</strong> through following, friending, and blocking features</li>
          <li><strong>Engage with content</strong> through likes, favorites, and comments</li>
          <li><strong>Stay updated</strong> with customizable notifications</li>
          <li><strong>Discover inspiration</strong> from artists worldwide</li>
        </ul>
        
        <p>By participating in our community, you agree to follow these guidelines and our <a href="/terms" class="footer-link">Terms of Service</a>.</p>
      </div>

      <div class="guidelines-section">
        <h2>2. Account Requirements</h2>
        
        <h3>Verified Account Benefits</h3>
        <p>To participate in community features, you need a <strong>verified account</strong>:</p>
        <ul>
          <li>Email verification through Auth0 authentication</li>
          <li>Access to post in public galleries</li>
          <li>Ability to create custom galleries</li>
          <li>Full social interaction features</li>
          <li>Notification preferences control</li>
        </ul>

        <h3>Account Responsibility</h3>
        <ul>
          <li>Maintain accurate profile information</li>
          <li>Use appropriate usernames and profile images</li>
          <li>Keep your account secure and report unauthorized access</li>
          <li>One account per person - multiple accounts are not permitted</li>
        </ul>
      </div>

      <div class="guidelines-section">
        <h2>3. Content Sharing Guidelines</h2>
        
        <h3>Gallery Posting</h3>
        <p><strong>Public Galleries:</strong></p>
        <ul>
          <li>Curated galleries managed by PixelArtNexus staff</li>
          <li>Submit your best work that fits the gallery theme</li>
          <li>All submissions are subject to moderation</li>
        </ul>
        
        <p><strong>User-Created Galleries:</strong></p>
        <ul>
          <li>Create themed collections for specific topics or styles</li>
          <li>Set clear descriptions and posting guidelines for your gallery</li>
          <li>Moderate submissions to maintain gallery quality</li>
        </ul>
        
        <p><strong>Personal Account Posts:</strong></p>
        <ul>
          <li>Share your artwork directly to your profile</li>
          <li>Followers receive notifications when you post</li>
          <li>All content must comply with community standards</li>
        </ul>

        <h3>Content Quality Standards</h3>
        <ul>
          <li>Share original artwork or properly credited collaborations</li>
          <li>Provide meaningful titles and descriptions</li>
          <li>Use appropriate tags to help others discover your work</li>
          <li>Ensure images are clear and properly formatted</li>
        </ul>
      </div>

      <div class="guidelines-section">
        <h2>4. Social Interactions</h2>
        
        <h3>Following and Friending</h3>
        <ul>
          <li><strong>Following:</strong> Stay updated with artists you admire</li>
          <li><strong>Friending:</strong> Build closer connections with mutual consent</li>
          <li><strong>Blocking:</strong> Control who can interact with your content</li>
          <li>Respect others' privacy settings and boundaries</li>
        </ul>

        <h3>Comments and Engagement</h3>
        <ul>
          <li><strong>Constructive feedback:</strong> Offer helpful, respectful critiques</li>
          <li><strong>Positive engagement:</strong> Use likes and favorites to show appreciation</li>
          <li><strong>Meaningful comments:</strong> Add value to the conversation</li>
          <li><strong>Comment likes:</strong> Support thoughtful community discussions</li>
        </ul>

        <h3>Notification Settings</h3>
        <p>Customize your experience through notification preferences:</p>
        <ul>
          <li>Likes and favorites on your content</li>
          <li>Comments on your posts</li>
          <li>New posts from followed artists</li>
          <li>Friend requests and social interactions</li>
          <li>Gallery submissions and updates</li>
        </ul>
      </div>

      <div class="prohibited-content">
        <h2>5. Prohibited Content</h2>
        <p><strong>The following content is strictly forbidden:</strong></p>
        
        <h3>Adult and Inappropriate Content</h3>
        <ul>
          <li><strong>NSFW content:</strong> No nudity, sexual content, or suggestive material</li>
          <li><strong>Gore and violence:</strong> No graphic violence, blood, or disturbing imagery</li>
          <li><strong>Provocative content:</strong> No content designed to shock or offend</li>
        </ul>

        <h3>Harmful Behavior</h3>
        <ul>
          <li><strong>Hate speech:</strong> No discrimination based on race, religion, gender, sexuality, or other protected characteristics</li>
          <li><strong>Bullying and harassment:</strong> No targeting, intimidating, or repeatedly bothering other users</li>
          <li><strong>Threats:</strong> No threatening language or implied violence</li>
          <li><strong>Grooming or predatory behavior:</strong> No content or behavior targeting minors in an inappropriate manner</li>
        </ul>

        <h3>Other Violations</h3>
        <ul>
          <li><strong>Spam:</strong> No repetitive, irrelevant, or promotional content</li>
          <li><strong>Copyright infringement:</strong> No unauthorized use of others' work</li>
          <li><strong>Impersonation:</strong> No pretending to be someone else</li>
          <li><strong>Misinformation:</strong> No deliberately false or misleading information</li>
        </ul>
        
        <div class="zero-tolerance-notice">
          <h3>Zero Tolerance for Grooming and Predatory Behavior</h3>
          <p>PixelArtNexus maintains a <strong>zero-tolerance policy</strong> regarding grooming, pedophilia, and any predatory behavior targeting minors. This is not just a rule—it's our unwavering commitment to safety.</p>
          <p>Any content, communication, or behavior associated with grooming or pedophilia will be:</p>
          <ul>
            <li>Immediately removed from our platform</li>
            <li>Thoroughly investigated using all available resources</li>
            <li>Reported to appropriate law enforcement agencies</li>
            <li>Subject to permanent account termination without warning</li>
          </ul>
          <p>The safety of our community, especially younger users, is our highest priority. We take this responsibility personally and will use every tool at our disposal to protect our users and bring offenders to justice.</p>
        </div>
      </div>

      <div class="enforcement-levels">
        <h2>6. Enforcement Actions</h2>
        <p>Violations result in escalating consequences based on severity and frequency:</p>
        
        <h3>Warning</h3>
        <ul>
          <li>First-time minor violations</li>
          <li>Content removal with explanation</li>
          <li>Educational guidance on community standards</li>
        </ul>

        <h3>Temporary Ban</h3>
        <ul>
          <li>Repeated violations or moderate offenses</li>
          <li>Duration: 1-30 days depending on severity</li>
          <li>Loss of posting and interaction privileges</li>
        </ul>

        <h3>Permanent Ban</h3>
        <ul>
          <li>Severe violations or repeated temporary bans</li>
          <li>Complete loss of account access</li>
          <li>All content and social connections removed</li>
        </ul>

        <h3>Immediate Permanent Ban</h3>
        <ul>
          <li>Any form of grooming or predatory behavior targeting minors</li>
          <li>Sharing or soliciting content that sexualizes minors</li>
          <li>Attempting to establish inappropriate relationships with minors</li>
          <li>No warnings will be issued for these violations</li>
          <li>Law enforcement may be contacted immediately</li>
        </ul>

        <h3>Ban Evasion</h3>
        <p><strong>Creating new accounts to bypass warnings, temporary bans, or permanent bans is strictly prohibited and will result in immediate permanent suspension of all associated accounts.</strong></p>
      </div>

      <div class="guidelines-section">
        <h2>7. Respectful Engagement</h2>
        <p>We expect all community members to:</p>
        <ul>
          <li><strong>Treat others with kindness:</strong> Remember there's a real person behind every account</li>
          <li><strong>Give constructive feedback:</strong> Help others improve while being respectful</li>
          <li><strong>Celebrate diversity:</strong> Welcome artists of all skill levels and backgrounds</li>
          <li><strong>Support creativity:</strong> Encourage experimentation and artistic growth</li>
          <li><strong>Resolve conflicts peacefully:</strong> Use blocking features rather than engaging in arguments</li>
          <li><strong>Report violations:</strong> Help maintain community standards by reporting inappropriate content</li>
        </ul>
      </div>

      <div class="guidelines-section">
        <h2>8. Reporting and Appeals</h2>
        
        <h3>Reporting Violations</h3>
        <ul>
          <li>Use the report button on any content or comment</li>
          <li>Provide specific details about the violation</li>
          <li>Reports are reviewed by our moderation team</li>
          <li>False reporting may result in penalties</li>
        </ul>

        <h3>Appeals Process</h3>
        <ul>
          <li>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></li>
          <li>Include your username and details of the action you're appealing</li>
          <li>Appeals are reviewed within 7 business days</li>
          <li>Decisions are final after the appeals process</li>
        </ul>
      </div>

      <div class="guidelines-section">
        <h2>9. Privacy and Safety</h2>
        <ul>
          <li><strong>Protect personal information:</strong> Don't share private details in public</li>
          <li><strong>Use privacy settings:</strong> Control who can see and interact with your content</li>
          <li><strong>Report safety concerns:</strong> Alert us to any threats or dangerous behavior</li>
          <li><strong>Block problematic users:</strong> Take control of your community experience</li>
        </ul>
      </div>

      <div class="guidelines-section">
        <h2>10. Community Growth</h2>
        <p>Help us build an amazing community by:</p>
        <ul>
          <li><strong>Welcoming newcomers:</strong> Help new artists feel at home</li>
          <li><strong>Sharing knowledge:</strong> Teach techniques and share resources</li>
          <li><strong>Participating actively:</strong> Engage with content and join discussions</li>
          <li><strong>Providing feedback:</strong> Help us improve the platform</li>
          <li><strong>Leading by example:</strong> Model the behavior you want to see</li>
        </ul>
      </div>

      <div class="contact-info">
        <h2>11. Contact and Support</h2>
        <p>Questions about these Community Guidelines? Need help with community features?</p>
        <ul>
          <li><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
          <li><strong>Subject Line:</strong> "Community Guidelines Question"</li>
          <li><strong>Response Time:</strong> Within 48 hours for community-related inquiries</li>
        </ul>
      </div>

      <div class="guidelines-section">
        <p><em>Thank you for helping make PixelArtNexus a welcoming, creative, and inspiring community for pixel artists everywhere. Together, we're building something amazing!</em></p>
      </div>

      <!-- Footer -->
      <div class="bottom-toolbar">
        <div class="footer-section core-info">
          <a href="/about" class="footer-link">About</a>
          <a href="/contact" class="footer-link">Contact</a>
          <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
          <a href="/help" class="footer-link">Help / FAQ</a>
        </div>
        <div class="footer-section legal-policy">
          <a href="/terms" class="footer-link">Terms of Service</a>
          <a href="/privacy" class="footer-link">Privacy Policy</a>
        </div>
        <div class="footer-section community-social">
          <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
          <a href="/social" class="footer-link">Social Media</a>
          <a href="/blog" class="footer-link">Blog / Updates</a>
        </div>
        <div class="footer-section copyright">
          <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
        </div>
      </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Login button (shown when not authenticated) -->
        <button id="authButton" class="auth-button">Login</button>

        <!-- Settings button (shown when not authenticated) -->
        <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

        <!-- Profile dropdown (shown when authenticated and verified) -->
        <div id="profileDropdown" class="profile-dropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div class="setting-item">
          <div>
            <label for="themeSelect">Theme:</label>
            <select id="themeSelect">
              <option value="light">Light Mode</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>
      </div>

      <!-- User Profile Settings (shown only for authenticated users) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div class="setting-item">
          <div>
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter username" maxlength="50">
          </div>
        </div>
        <div class="setting-item">
          <div>
            <label for="profileImageInput">Profile Picture:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
            <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
          </div>
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <script src="shared-layout.js"></script>
</body>
</html>
