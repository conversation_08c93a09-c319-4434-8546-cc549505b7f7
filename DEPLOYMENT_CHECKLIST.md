# Deployment Checklist for Xano Integration

Follow this checklist to deploy your PixelArtNexus with Xano user settings sync.

## ✅ Pre-Deployment Setup

### 1. Complete Xano Setup
- [ ] Created Xano account and workspace
- [ ] Created `user_settings` table with all required fields
- [ ] Set up all three API endpoints (GET, POST, PATCH)
- [ ] Configured CORS for your domains
- [ ] Tested API endpoints in Xano's API explorer

### 2. Configure Environment Variables
- [ ] Set up environment variables in Netlify (AUTH0_DOMAIN, AUTH0_CLIENT_ID, XANO_BASE_URL)
- [ ] Create a local `.env` file from `.env.example` for development
- [ ] Run `npm run generate-config` to test environment variable injection
- [ ] Tested locally with `test-xano.html` (optional but recommended)

### 3. Test Locally
- [ ] Run `npm run dev` to start local server
- [ ] Test Auth0 login/logout
- [ ] Test settings changes (theme and keybinds)
- [ ] Check browser console for any errors
- [ ] Verify sync status indicator works

## 🚀 Deployment Steps

### 1. Build for Production
```bash
npm run build:prod
```

### 2. Update Xano CORS (if needed)
Add your Netlify domain to CORS origins:
- `https://your-site-name.netlify.app`
- `https://pixelartnexus.com` (if using custom domain)

### 3. Deploy to Netlify
- Push changes to your GitHub repository
- Netlify will automatically deploy from the `dist` folder

### 4. Update Auth0 Settings
Add your production URL to Auth0:
- Allowed Callback URLs: `https://your-site-name.netlify.app`
- Allowed Logout URLs: `https://your-site-name.netlify.app`
- Allowed Web Origins: `https://your-site-name.netlify.app`

## 🧪 Post-Deployment Testing

### Test the Complete Flow:

1. **First Device Test:**
   - [ ] Visit your deployed site
   - [ ] Log in with Auth0
   - [ ] Change theme to dark mode
   - [ ] Modify some keybinds
   - [ ] Verify sync status shows "Settings synced to cloud"
   - [ ] Log out

2. **Second Device Test:**
   - [ ] Visit site on different device/browser
   - [ ] Log in with same Auth0 account
   - [ ] Verify dark theme is applied automatically
   - [ ] Verify custom keybinds are loaded
   - [ ] Verify sync status shows "Settings loaded from cloud"

3. **Offline Test:**
   - [ ] Disconnect internet
   - [ ] Change settings
   - [ ] Verify sync status shows error/offline state
   - [ ] Reconnect internet
   - [ ] Verify settings sync when connection restored

## 🔧 Configuration Reference

### Your Xano URL Format:
```
https://[workspace-id].xano.io/api:[api-group]
```

Example:
```
https://x8ki-letl-twmt.xano.io/api:v1
```

### Required Xano Table Structure:
```sql
user_settings:
- id (Integer, Primary Key, Auto Increment)
- user_id (Text, Required, Unique Index)
- theme (Text, Required, Default: 'light')
- keybinds (JSON, Required)
- created_at (DateTime, Auto-fill on Create)
- updated_at (DateTime, Auto-fill on Create & Update)
```

### Required API Endpoints:
- `GET /user_settings?user_id={user_id}`
- `POST /user_settings`
- `PATCH /user_settings/{id}`

## 🐛 Troubleshooting

### Common Issues:

1. **CORS Errors:**
   - Check Xano CORS settings include your domain
   - Verify protocol (http vs https) matches

2. **404 API Errors:**
   - Verify Xano URL is correct
   - Check API endpoint paths match exactly

3. **Settings Not Syncing:**
   - Check browser console for errors
   - Verify Auth0 user.sub is being passed correctly
   - Test API endpoints directly with `test-xano.html`

4. **Sync Status Always Shows Error:**
   - Check network tab in browser dev tools
   - Verify Xano workspace is active
   - Check API response format

### Debug Steps:

1. **Check Browser Console:**
   ```javascript
   // Look for these log messages:
   "Loading user settings from Xano..."
   "Successfully applied settings from Xano"
   "Syncing local settings to cloud..."
   ```

2. **Test API Directly:**
   - Use the included `test-xano.html` file
   - Test each endpoint individually
   - Verify response format matches expectations

3. **Check Xano Logs:**
   - Go to Xano workspace > Logs
   - Look for API call records
   - Check for any error responses

## 📊 Success Metrics

Your deployment is successful when:
- [ ] Users can log in and out with Auth0
- [ ] Settings changes are saved locally immediately
- [ ] Settings sync to Xano (check sync status indicator)
- [ ] Settings persist across devices and sessions
- [ ] Offline mode gracefully degrades to localStorage
- [ ] No console errors related to Xano integration

## 🔄 Future Enhancements

Once basic sync is working, you could add:
- [ ] Conflict resolution for simultaneous edits
- [ ] Settings export/import functionality
- [ ] User preference for auto-sync vs manual sync
- [ ] Settings history/versioning
- [ ] Bulk settings operations

## 📞 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Review Xano documentation
3. Test with the included `test-xano.html` file
4. Check browser console and network tab
5. Verify Xano logs for API calls
