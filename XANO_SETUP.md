# Xano Setup Guide for PixelArtNexus User Settings

This guide will help you set up Xano to store user settings (theme and keybinds) for authenticated users.

## 1. Create Xano Account and Workspace

1. Go to [Xano.com](https://xano.com) and create an account
2. Create a new workspace for your PixelArtNexus project
3. Note your workspace URL (e.g., `https://your-workspace.xano.io`)

## 2. Create Database Table

Create a new table called `user_settings` with the following fields:

| Field Name | Type | Settings |
|------------|------|----------|
| `id` | Integer | Primary Key, Auto Increment |
| `user_id` | Text | Required, Indexed |
| `theme` | Text | Required, Default: 'light' |
| `keybinds` | JSON | Required |
| `created_at` | DateTime | Auto-fill on Create |
| `updated_at` | DateTime | Auto-fill on Create & Update |

### Field Details:

- **user_id**: Will store the Auth0 user ID (user.sub)
- **theme**: Either 'light' or 'dark'
- **keybinds**: JSON object containing the user's custom keybinds
- **created_at/updated_at**: Automatic timestamps

## 3. Create API Endpoints

Create the following API endpoints in your Xano workspace:

### A. Get User Settings
- **Method**: GET
- **Path**: `/user_settings`
- **Query Parameters**: `user_id` (text, required)
- **Function**: 
  ```
  Query user_settings table where user_id = {user_id}
  Order by created_at DESC
  Return results
  ```

### B. Create User Settings
- **Method**: POST
- **Path**: `/user_settings`
- **Body Parameters**:
  - `user_id` (text, required)
  - `theme` (text, required)
  - `keybinds` (JSON, required)
- **Function**:
  ```
  Insert into user_settings table with provided data
  Return created record
  ```

### C. Update User Settings
- **Method**: PATCH
- **Path**: `/user_settings/{id}`
- **Path Parameters**: `id` (integer, required)
- **Body Parameters**:
  - `theme` (text, optional)
  - `keybinds` (JSON, optional)
- **Function**:
  ```
  Update user_settings record where id = {id}
  Set updated_at to current timestamp
  Return updated record
  ```

## 4. Configure CORS

In your Xano workspace settings:
1. Go to Settings > API
2. Add your domain to CORS allowed origins:
   - For development: `http://localhost:8080`, `http://localhost:8082`
   - For production: `https://pixelartnexus.com`

## 5. Update Your Code

In your `index.js` file, update the `XANO_CONFIG` object:

```javascript
const XANO_CONFIG = {
    baseURL: 'https://your-workspace.xano.io/api:your-api-group', // Replace with your actual URL
    endpoints: {
        getUserSettings: '/user_settings',
        saveUserSettings: '/user_settings',
        updateUserSettings: '/user_settings'
    }
};
```

Replace:
- `your-workspace` with your actual Xano workspace name
- `your-api-group` with your API group name (usually 'v1' or similar)

## 6. Test the Integration

1. Deploy your updated code
2. Log in with Auth0
3. Change your theme or keybinds in settings
4. Check your Xano database to see if the settings are saved
5. Log out and log back in to verify settings are restored

## 7. Optional: Add Data Validation

In Xano, you can add validation rules:

### Theme Validation
- Add validation to ensure theme is either 'light' or 'dark'

### Keybinds Validation
- Validate that keybinds is a valid JSON object
- Optionally validate specific keybind structure

## 8. Monitoring and Logs

- Use Xano's built-in logging to monitor API calls
- Check for any errors in the browser console
- Monitor the sync status indicator in your settings modal

## Troubleshooting

### Common Issues:

1. **CORS Errors**: Make sure your domain is added to CORS settings
2. **404 Errors**: Verify your API endpoint URLs are correct
3. **Authentication Issues**: Ensure user.sub is being passed correctly
4. **JSON Parsing**: Make sure keybinds are properly stringified/parsed

### Debug Steps:

1. Check browser console for error messages
2. Verify network requests in browser dev tools
3. Check Xano logs for API call details
4. Test API endpoints directly in Xano's API explorer

## Security Notes

- User settings are tied to Auth0 user IDs, ensuring users can only access their own settings
- No sensitive data is stored (only theme preferences and keybinds)
- All API calls are made over HTTPS
- Consider adding rate limiting in Xano if needed

## Next Steps

Once this is working, you could extend the system to store:
- Canvas preferences
- Tool settings
- Recent projects metadata
- User preferences for UI layout

The current implementation provides a solid foundation for any additional user data storage needs.
