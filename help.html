<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Help & FAQ | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3586346747453362"
       crossorigin="anonymous"></script>
  <style>
    h3 {
      color: #2c5aa0;
      margin-top: 25px;
    }

    .faq-item {
      background-color: #f9f9f9;
      padding: 20px;
      border-radius: 8px;
      margin: 15px 0;
      border-left: 4px solid #2c5aa0;
    }

    .faq-question {
      font-weight: bold;
      color: #2c5aa0;
      margin-bottom: 10px;
      font-size: 1.1em;
    }

    .tool-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }

    .tool-card {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
      border-left: 3px solid #2c5aa0;
    }

    .tool-card h4 {
      margin-top: 0;
      color: #2c5aa0;
    }

    .shortcut-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }

    .shortcut-table th,
    .shortcut-table td {
      border: 1px solid #ddd;
      padding: 12px;
      text-align: left;
    }

    .shortcut-table th {
      background-color: #2c5aa0;
      color: white;
    }

    .shortcut-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .kbd {
      background-color: #e0e0e0;
      border: 1px solid #ccc;
      border-radius: 3px;
      padding: 2px 6px;
      font-family: monospace;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="title.webp" alt="PixelArtNexus" class="logo">
        <h1>Help & FAQ</h1>
      </div>
    
    <h2>🎨 Getting Started</h2>
    <p>Welcome to PixelArtNexus! This guide will help you get started with creating amazing pixel art using our web-based editor.</p>
    
    <h2>🛠️ Tools Overview</h2>
    <div class="tool-grid">
      <div class="tool-card">
        <h4>🖌️ Draw Tool (D)</h4>
        <p>Basic drawing tool for creating pixel art. Click and drag to draw with the selected color and brush size.</p>
      </div>
      <div class="tool-card">
        <h4>🧽 Erase Tool (E)</h4>
        <p>Remove pixels from your canvas. Works like the draw tool but removes content instead.</p>
      </div>
      <div class="tool-card">
        <h4>🎨 Fill Tool (F)</h4>
        <p>Fill connected areas with the selected color. Great for filling large regions quickly.</p>
      </div>
      <div class="tool-card">
        <h4>👁️ Eyedropper (T)</h4>
        <p>Pick colors from existing pixels on your canvas to use as your drawing color.</p>
      </div>
      <div class="tool-card">
        <h4>✋ Pan Tool (P)</h4>
        <p>Move around your canvas when zoomed in. Also accessible by right-clicking and dragging.</p>
      </div>
      <div class="tool-card">
        <h4>📦 Select Tool (S)</h4>
        <p>Select rectangular areas to copy, cut, paste, or move around your canvas.</p>
      </div>
    </div>
    
    <h2>⌨️ Keyboard Shortcuts</h2>
    <table class="shortcut-table">
      <thead>
        <tr>
          <th>Action</th>
          <th>Default Shortcut</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Draw Tool</td>
          <td><span class="kbd">D</span></td>
          <td>Switch to drawing tool</td>
        </tr>
        <tr>
          <td>Pan Tool</td>
          <td><span class="kbd">P</span></td>
          <td>Switch to pan tool</td>
        </tr>
        <tr>
          <td>Eyedropper</td>
          <td><span class="kbd">T</span></td>
          <td>Switch to eyedropper tool</td>
        </tr>
        <tr>
          <td>Select Tool</td>
          <td><span class="kbd">S</span></td>
          <td>Switch to selection tool</td>
        </tr>
        <tr>
          <td>Brush Size +</td>
          <td><span class="kbd">=</span></td>
          <td>Increase brush size</td>
        </tr>
        <tr>
          <td>Brush Size -</td>
          <td><span class="kbd">-</span></td>
          <td>Decrease brush size</td>
        </tr>
        <tr>
          <td>Undo</td>
          <td><span class="kbd">Ctrl+Z</span></td>
          <td>Undo last action</td>
        </tr>
        <tr>
          <td>Redo</td>
          <td><span class="kbd">Ctrl+Y</span></td>
          <td>Redo last undone action</td>
        </tr>
      </tbody>
    </table>
    <p><em>Note: You can customize these shortcuts in the Settings menu!</em></p>
    
    <h2>❓ Frequently Asked Questions</h2>
    
    <div class="faq-item">
      <div class="faq-question">Q: How do I save my artwork?</div>
      <div class="faq-answer">A: Click the "Save" button in the toolbar to download your pixel art as a PNG file. You can save individual selections or the entire canvas.</div>
    </div>
    
    <div class="faq-item">
      <div class="faq-question">Q: Can I work with multiple layers?</div>
      <div class="faq-answer">A: Yes! Use the Layers panel to add, delete, and manage multiple layers. You can adjust opacity and visibility for each layer independently.</div>
    </div>
    
    <div class="faq-item">
      <div class="faq-question">Q: How do I change the canvas size?</div>
      <div class="faq-answer">A: Click the "Change Size" button in the toolbar to open the canvas size selector. Choose from preset sizes or create a custom size.</div>
    </div>
    
    <div class="faq-item">
      <div class="faq-question">Q: Can I import images?</div>
      <div class="faq-answer">A: Yes! Drag and drop image files directly onto the canvas to import them. The image will be automatically resized to fit your canvas.</div>
    </div>
    
    <div class="faq-item">
      <div class="faq-question">Q: How do I use the selection tool?</div>
      <div class="faq-answer">A: Select the Selection Tool (S), then click and drag to create a selection rectangle. You can then copy (Ctrl+C), cut, paste (Ctrl+V), or move the selected area.</div>
    </div>
    
    <div class="faq-item">
      <div class="faq-question">Q: Does my work save automatically?</div>
      <div class="faq-answer">A: Yes! Your work is automatically saved to your browser's local storage. However, we recommend regularly downloading your work as PNG files for backup.</div>
    </div>
    
    <div class="faq-item">
      <div class="faq-question">Q: Can I use this on mobile devices?</div>
      <div class="faq-answer">A: Yes! PixelArtNexus is designed to work on both desktop and mobile devices, with touch-friendly controls for mobile users.</div>
    </div>
    
    <h2>🎯 Tips for Better Pixel Art</h2>
    <ul>
      <li><strong>Start Small:</strong> Begin with smaller canvas sizes (16x16 or 32x32) to learn the basics</li>
      <li><strong>Use Limited Colors:</strong> Restrict your palette to create more cohesive artwork</li>
      <li><strong>Zoom In:</strong> Work at high zoom levels for precise pixel placement</li>
      <li><strong>Use Layers:</strong> Separate different elements (background, character, effects) into different layers</li>
      <li><strong>Save Often:</strong> Download your work regularly to avoid losing progress</li>
    </ul>
    
    <h2>🆘 Need More Help?</h2>
    <p>If you can't find the answer to your question here, please don't hesitate to <a href="/contact" style="color: #2c5aa0;">contact us</a> or submit feedback through our <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" style="color: #2c5aa0;">feedback form</a>.</p>

    <a href="/" class="back-link">← Back to Home</a>

    <!-- Footer -->
    <div class="bottom-toolbar">
      <div class="footer-section core-info">
        <a href="/about" class="footer-link">About</a>
        <a href="/contact" class="footer-link">Contact</a>
        <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
        <a href="/help" class="footer-link">Help / FAQ</a>
      </div>
      <div class="footer-section legal-policy">
        <a href="/terms" class="footer-link">Terms of Service</a>
        <a href="/privacy" class="footer-link">Privacy Policy</a>
      </div>
      <div class="footer-section community-social">
        <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
        <a href="/social" class="footer-link">Social Media</a>
        <a href="/blog" class="footer-link">Blog / Updates</a>
      </div>
      <div class="footer-section copyright">
        <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
      </div>
    </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Login button (shown when not authenticated) -->
        <button id="authButton" class="auth-button">Login</button>

        <!-- Settings button (shown when not authenticated) -->
        <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

        <!-- Profile dropdown (shown when authenticated and verified) -->
        <div id="profileDropdown" class="profile-dropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div class="setting-item">
          <div>
            <label for="themeSelect">Theme:</label>
            <select id="themeSelect">
              <option value="light">Light Mode</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>
      </div>

      <!-- User Profile Settings (shown only for authenticated users) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div class="setting-item">
          <div>
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter username" maxlength="50">
          </div>
        </div>
        <div class="setting-item">
          <div>
            <label for="profileImageInput">Profile Picture:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
            <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
          </div>
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <script src="shared-layout.js"></script>
</body>
</html>
