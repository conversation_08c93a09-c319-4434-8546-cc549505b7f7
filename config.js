// Configuration file that loads environment variables during build
// and provides them to the application

// Default values for local development (REPLACE THESE WITH PLACEHOLDERS IN PRODUCTION CODE)
const defaultConfig = {
  auth0: {
    domain: 'dev-example.us.auth0.com',
    clientId: 'your-client-id-placeholder',
    audience: 'https://dev-example.us.auth0.com/api/v2/',
  },
  xano: {
    baseURL: 'https://example.xano.io/api:example',
  }
};

// Get environment variables with fallbacks to default values
const config = {
  auth0: {
    domain: process.env.AUTH0_DOMAIN || defaultConfig.auth0.domain,
    clientId: process.env.AUTH0_CLIENT_ID || defaultConfig.auth0.clientId,
    audience: process.env.AUTH0_AUDIENCE || `https://${process.env.AUTH0_DOMAIN || defaultConfig.auth0.domain}/api/v2/`,
  },
  xano: {
    baseURL: process.env.XANO_BASE_URL || defaultConfig.xano.baseURL,
  }
};

// Export the configuration
module.exports = config;