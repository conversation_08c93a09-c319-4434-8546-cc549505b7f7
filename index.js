// Main Landing Page JavaScript

// Get configuration from runtime-config.js (injected during build)
const APP_CONFIG = window.APP_CONFIG || {
  auth0: {
    domain: 'dev-example.us.auth0.com',
    clientId: 'your-client-id-placeholder',
  },
  xano: {
    baseURL: 'https://example.xano.io/api:example',
  }
};

// Auth0 configuration
const auth0Config = {
  domain: APP_CONFIG.auth0.domain,
  clientId: APP_CONFIG.auth0.clientId,
  redirectUri: window.location.pathname === '/' ? window.location.origin : window.location.origin + window.location.pathname,
  audience: `https://${APP_CONFIG.auth0.domain}/api/v2/`,
  scope: 'openid profile email'
};

let auth0Client;
let currentUser = null;
let currentUserData = null; // Store complete user data from Xano

// Xano Configuration
const XANO_CONFIG = {
  baseURL: APP_CONFIG.xano.baseURL,
  endpoints: {
    getUserSettings: '/user_settings',
    saveUserSettings: '/user_settings',
    updateUserSettings: '/user_settings',
    checkUsernameAvailability: '/check-username-ability'
  }
};

// Xano Service with security improvements
class XanoService {
  static async makeRequest(url, options = {}) {
    // Use secure API client if available
    if (typeof SecurityUtils !== 'undefined' && SecurityUtils.secureAPIClient) {
      try {
        return await SecurityUtils.secureAPIClient.makeJSONRequest(`${XANO_CONFIG.baseURL}${url}`, options);
      } catch (error) {
        console.error('Secure API request failed:', error);
        throw error;
      }
    }
    
    // Fallback to original implementation with basic security
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const finalOptions = { ...defaultOptions, ...options };
    const response = await fetch(`${XANO_CONFIG.baseURL}${url}`, finalOptions);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  static async getUserSettings(userEmail) {
    try {
      const response = await this.makeRequest(`${XANO_CONFIG.endpoints.getUserSettings}?email=${encodeURIComponent(userEmail)}`);
      return response || null;
    } catch (error) {
      console.error('Failed to get user settings from Xano:', error);
      return null;
    }
  }

  static async saveUserSettings(userId, settings, userEmail = null, emailVerified = false, username = null, profileImage = null) {
    try {
      // First, check if a record already exists for this email
      const existingRecord = await this.getUserSettings(userEmail || userId);

      const requestBody = {
        user_id: userId, // Keep for reference, but email is primary
        email: userEmail || userId, // Use email as primary identifier
        username: username,
        profile_image: profileImage,
        theme: settings.theme,
        keybinds: typeof settings.keybinds === 'object' ? JSON.stringify(settings.keybinds) : settings.keybinds,
        email_verified: emailVerified,
        brush_performance_mode: settings.brushPerformanceMode || false,
        show_canvas_size_warning: settings.showCanvasSizeWarning !== undefined ? settings.showCanvasSizeWarning : true,
        image_import_mode: settings.imageImportMode || 'fit-to-view',
        show_image_import_warning: settings.showImageImportWarning !== undefined ? settings.showImageImportWarning : true
      };

      // If we found an existing record, preserve data that shouldn't be overwritten
      if (existingRecord) {
        const existingData = existingRecord.user_settings || existingRecord;
        console.log('🔗 Found existing record for email, linking accounts:', {
          existingUserId: existingData.user_id,
          newUserId: userId,
          email: userEmail
        });

        // Preserve existing username and profile image if not provided
        if (!username && existingData.username) {
          requestBody.username = existingData.username;
        }
        if (!profileImage && existingData.profile_image) {
          requestBody.profile_image = existingData.profile_image;
        }

        // Update the user_id to the current login method
        requestBody.user_id = userId;
      }

      console.log('🔍 XanoService.saveUserSettings - Request body:', {
        ...requestBody,
        profile_image: requestBody.profile_image ? `[Base64 data: ${requestBody.profile_image.length} chars]` : null,
        keybinds: typeof requestBody.keybinds === 'object' ? '[Object]' : requestBody.keybinds
      });

      const response = await this.makeRequest(XANO_CONFIG.endpoints.saveUserSettings, {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      console.log('🔍 XanoService.saveUserSettings - Response:', {
        ...response,
        profile_image: response.profile_image ? `[Base64 data: ${response.profile_image.length} chars]` : null
      });

      return response;
    } catch (error) {
      console.error('Failed to save user settings to Xano:', error);
      throw error;
    }
  }

  static async checkUsernameAvailability(username) {
    try {
      console.log('🔍 Checking username availability:', username);
      
      const url = `${XANO_CONFIG.endpoints.checkUsernameAvailability}?username=${encodeURIComponent(username)}`;
      
      const response = await this.makeRequest(url);
      console.log('✅ Username availability check result:', response);
      
      // Parse Xano response format:
      // [] = username available
      // [{username: "name"}] = username taken
      const isAvailable = Array.isArray(response) && response.length === 0;
      
      return {
        available: isAvailable,
        message: isAvailable ? 'Username is available' : 'Username already taken'
      };
    } catch (error) {
      console.error('Failed to check username availability:', error);
      // Fall back to checking existing users if API fails
      return await this.checkUsernameAvailabilityFallback(username);
    }
  }

  static async checkUsernameAvailabilityFallback(username) {
    try {
      console.log('🔄 Using fallback method to check username availability');
      
      // Try to get user settings by username (this is a fallback if the dedicated endpoint doesn't exist)
      const response = await this.makeRequest(`${XANO_CONFIG.endpoints.getUserSettings}?username=${encodeURIComponent(username)}`);
      
      if (response && response.username) {
        return {
          available: false,
          message: 'Username already taken'
        };
      } else {
        return {
          available: true,
          message: 'Username is available'
        };
      }
    } catch (error) {
      console.warn('Fallback username availability check also failed:', error);
      return {
        available: true,
        message: 'Unable to verify username availability',
        warning: true
      };
    }
  }
}

// Initialize Auth0
async function initAuth0() {
  try {
    console.log('Initializing Auth0 on main page...');
    console.log('Auth0 config:', auth0Config);

    auth0Client = await auth0.createAuth0Client(auth0Config);
    console.log('Auth0 client created successfully');

    // Handle redirect callback FIRST
    const query = window.location.search;
    console.log('Current URL query:', query);

    if (query.includes('code=') && query.includes('state=')) {
      console.log('Handling Auth0 redirect callback on main page...');
      try {
        await auth0Client.handleRedirectCallback();
        console.log('Auth0 callback handled successfully');

        currentUser = await auth0Client.getUser();
        console.log('User authenticated:', currentUser);

        await loadUserSettingsFromXano(); // Load user data from Xano
        updateAuthUI(true);

        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);

        showNotification('Successfully logged in!', 'success');
      } catch (error) {
        console.error('Auth callback error on main page:', error);
        showNotification('Login failed. Please try again.', 'error');

        // Clean up URL even on error
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    } else {
      // Check if user is already authenticated
      const isAuthenticated = await auth0Client.isAuthenticated();
      console.log('User already authenticated:', isAuthenticated);

      if (isAuthenticated) {
        currentUser = await auth0Client.getUser();
        await loadUserSettingsFromXano(); // Load user data from Xano
        updateAuthUI(true);
      } else {
        updateAuthUI(false);
      }
    }
  } catch (error) {
    console.error('Auth0 initialization error on main page:', error);
    showNotification('Authentication system error. Please refresh the page.', 'error');
  }
}

// Load user settings from Xano
async function loadUserSettingsFromXano() {
  if (!currentUser) {
    console.log('User not authenticated, skipping Xano settings load');
    return;
  }

  try {
    console.log('Loading user settings from Xano...');

    // Use email as primary identifier to link accounts across auth methods
    const userEmail = currentUser.email || currentUser.sub;
    console.log('Looking up settings by email:', userEmail);
    const xanoSettings = await XanoService.getUserSettings(userEmail);

    if (xanoSettings) {
      // Handle nested response structure from Xano
      const settingsData = xanoSettings.user_settings || xanoSettings;
      currentUserData = settingsData; // Store complete user data
      userSettingsId = settingsData.id;

      console.log('Loaded settings from Xano:', settingsData);
      console.log('🔍 Loaded username:', settingsData.username);
      console.log('🔍 Loaded profile image:', !!settingsData.profile_image);

      // Initialize canvas variables from Xano data
      if (settingsData.keybinds) {
        try {
          currentKeybinds = typeof settingsData.keybinds === 'string' ?
            JSON.parse(settingsData.keybinds) : settingsData.keybinds;
        } catch (e) {
          console.warn('Failed to parse keybinds from Xano:', e);
          currentKeybinds = {};
        }
      }

      brushPerformanceMode = settingsData.brush_performance_mode || false;
      showCanvasSizeWarning = settingsData.show_canvas_size_warning !== false;
      imageImportMode = settingsData.image_import_mode || 'fit-to-view';
      showImageImportWarning = settingsData.show_image_import_warning !== false;

      // Apply theme from Xano if available
      if (settingsData.theme) {
        applyTheme(settingsData.theme);
      }
    } else {
      console.log('No settings found in Xano for user');
    }
  } catch (error) {
    console.error('Failed to load settings from Xano:', error);
  }
}

// Update authentication UI
function updateAuthUI(isAuthenticated) {
  const authButton = document.getElementById('authButton');
  const settingsButton = document.getElementById('settingsButton');
  const profileDropdown = document.getElementById('profileDropdown');

  if (isAuthenticated && currentUser) {
    authButton.style.display = 'none';

    // Check if user is verified
    const isVerified = currentUser.email_verified;

    if (isVerified) {
      settingsButton.style.display = 'none';
      profileDropdown.style.display = 'block';
      updateProfileImage();
    } else {
      settingsButton.style.display = 'block';
      profileDropdown.style.display = 'none';
    }
  } else {
    // User not logged in - show both login and settings buttons
    authButton.style.display = 'block';
    settingsButton.style.display = 'block';
    profileDropdown.style.display = 'none';
  }
}

// Update profile image
function updateProfileImage() {
  const profileImage = document.getElementById('profileImage');
  if (profileImage) {
    if (currentUserData && currentUserData.profile_image) {
      console.log('🔍 Setting profile image from Xano currentUserData');
      profileImage.src = currentUserData.profile_image;
    } else {
      console.log('🔍 Setting default blank profile image');
      // Set default blank profile image (same as other pages)
      profileImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFMEUwRTAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K';
    }
  }
}

// Theme management
function initTheme() {
  const savedTheme = localStorage.getItem('pixelart-theme') || 'light';
  applyTheme(savedTheme);
}

function applyTheme(theme) {
  if (theme === 'dark') {
    document.body.classList.add('dark-theme');
  } else {
    document.body.classList.remove('dark-theme');
  }
  localStorage.setItem('pixelart-theme', theme);
}

// Notification system
function showNotification(message, type = 'success') {
  // Remove existing notifications
  const existingNotifications = document.querySelectorAll('.notification');
  existingNotifications.forEach(notification => notification.remove());
  
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  
  document.body.appendChild(notification);
  
  // Show notification
  setTimeout(() => {
    notification.classList.add('show');
  }, 100);
  
  // Hide notification after 3 seconds
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Settings modal state
let settingsChanged = false;
let originalSettings = {};

// Canvas settings variables (needed for Xano compatibility)
let userSettingsId = null;
let currentKeybinds = {};
let showCanvasSizeWarning = true;
let imageImportMode = 'fit-to-view';
let showImageImportWarning = true;
let brushPerformanceMode = false;

// Auth variables for compatibility with shared layout
let isAuthenticated = false;
let user = null;

// Settings Modal Functions
function openSettingsModal() {
  const modal = document.getElementById('settingsModal');
  if (modal) {
    // Store original settings
    originalSettings = {
      theme: localStorage.getItem('pixelart-theme') || 'light',
      username: currentUserData ? currentUserData.username : '',
      profileImage: currentUserData ? currentUserData.profile_image : null
    };

    // Load current settings into modal
    loadSettingsIntoModal();

    // Reset change tracking
    settingsChanged = false;

    modal.style.display = 'flex';
    console.log('⚙️ Settings modal opened');
  }
}

function loadSettingsIntoModal() {
  // Load theme
  const themeSelect = document.getElementById('themeSelect');
  if (themeSelect) {
    themeSelect.value = originalSettings.theme;
  }

  // Show/hide user profile section based on authentication
  const userProfileSection = document.getElementById('userProfileSection');
  if (userProfileSection) {
    if (currentUser && currentUser.email_verified) {
      userProfileSection.style.display = 'block';

      const usernameInput = document.getElementById('usernameInput');
      const profileImagePreview = document.getElementById('profileImagePreview');

      if (usernameInput) {
        usernameInput.value = originalSettings.username || '';
      }

      if (profileImagePreview && originalSettings.profileImage) {
        profileImagePreview.src = originalSettings.profileImage;
        profileImagePreview.style.display = 'block';
      } else if (profileImagePreview) {
        profileImagePreview.style.display = 'none';
      }
    } else {
      userProfileSection.style.display = 'none';
    }
  }
}

async function closeSettingsModal(force = false) {
  if (!force && settingsChanged) {
    // Show confirmation dialog
    const shouldSave = confirm('You have unsaved changes. Would you like to save your settings before closing?');
    if (shouldSave) {
      await saveSettings();
      return;
    }

    // User chose not to save, revert changes
    revertSettings();
  }

  const modal = document.getElementById('settingsModal');
  if (modal) {
    modal.style.display = 'none';
    console.log('⚙️ Settings modal closed');
  }
}

function revertSettings() {
  // Revert theme
  applyTheme(originalSettings.theme);
  localStorage.setItem('pixelart-theme', originalSettings.theme);

  console.log('🔄 Settings reverted to original values');
}

async function saveSettings() {
  try {
    const themeSelect = document.getElementById('themeSelect');
    const selectedTheme = themeSelect ? themeSelect.value : 'light';

    // Apply theme immediately
    applyTheme(selectedTheme);
    localStorage.setItem('pixelart-theme', selectedTheme);

    // Save to database if user is authenticated and verified
    if (currentUser && currentUser.email_verified) {
      await saveSettingsToXano(selectedTheme);
    }

    // Reset change tracking
    settingsChanged = false;
    originalSettings.theme = selectedTheme;

    console.log('💾 Settings saved - theme:', selectedTheme);
    showNotification('Settings saved successfully!');

    // Close modal
    const modal = document.getElementById('settingsModal');
    if (modal) {
      modal.style.display = 'none';
    }

  } catch (error) {
    console.error('Failed to save settings:', error);
    
    // Check if this is a username availability error or user cancellation
    if (error.message && error.message.includes('Username change cancelled by user')) {
      showNotification('Settings save cancelled.', 'info');
    } else if (error.message && (error.message.includes('Username already taken') || error.message.includes('already taken'))) {
      showNotification('Username already taken. Please choose a different username.', true);
    } else if (error.message && error.message.toLowerCase().includes('username')) {
      showNotification(error.message, true);
    } else {
      showNotification('Failed to save settings. Please try again.', true);
    }
  }
}

async function saveSettingsToXano(theme) {
  if (!currentUser) {
    console.log('User not authenticated, skipping Xano save');
    return;
  }

  // Check if user email is verified
  const emailVerified = currentUser.email_verified || false;
  if (!emailVerified) {
    console.log('User email not verified, skipping cloud sync');
    return;
  }

  try {
    // Get current values from modal
    const usernameInput = document.getElementById('usernameInput');
    const profileImageInput = document.getElementById('profileImageInput');

    let username = currentUserData ? currentUserData.username : null;
    let profileImageData = currentUserData ? currentUserData.profile_image : null;

    // Update username if changed with validation
    if (usernameInput && usernameInput.value.trim() !== originalSettings.username) {
      const newUsername = usernameInput.value.trim();
      
      // Validate username using security utils if available
      if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
        const validation = SecurityUtils.InputValidator.validateUsername(newUsername);
        if (!validation.valid) {
          throw new Error(validation.error);
        }
      } else {
        // Fallback validation
        if (newUsername.length > 50) {
          throw new Error('Username cannot exceed 50 characters');
        }
        if (!/^[a-zA-Z0-9 _\-\.]+$/.test(newUsername)) {
          throw new Error('Username can only contain letters, numbers, spaces, underscores, hyphens, and periods');
        }
      }

      // Check username availability
      try {
        const availabilityCheck = await XanoService.checkUsernameAvailability(newUsername);
        if (!availabilityCheck.available) {
          throw new Error(availabilityCheck.message);
        }
        
        if (availabilityCheck.warning) {
          const proceed = confirm(availabilityCheck.message + '\n\nDo you want to proceed anyway?');
          if (!proceed) {
            throw new Error('Username change cancelled by user');
          }
        }
      } catch (error) {
        console.error('Username availability check failed:', error);
        throw error;
      }
      
      username = newUsername;
    }

    // Update profile image if changed
    if (profileImageInput && profileImageInput.files && profileImageInput.files[0]) {
      profileImageData = await convertImageToBase64(profileImageInput.files[0]);
    }

    // Prepare settings data - include all user preferences (matching canvas implementation)
    const settings = {
      theme,
      keybinds: currentKeybinds,
      brushPerformanceMode: brushPerformanceMode,
      showCanvasSizeWarning: showCanvasSizeWarning,
      imageImportMode: imageImportMode,
      showImageImportWarning: showImageImportWarning
    };

    const userEmail = currentUser.email || currentUser.sub;

    // Preserve existing username and profile image from currentUserData
    const existingUsername = username || (currentUserData ? currentUserData.username : null);
    const existingProfileImage = profileImageData || (currentUserData ? currentUserData.profile_image : null);

    console.log('Full user object when saving:', currentUser); // Debug: Full user object
    console.log('Saving with email:', userEmail); // Debug: Check what email we're sending
    console.log('User sub (Auth0 ID):', currentUser.sub); // Debug: Check Auth0 ID
    console.log('Email verified status:', emailVerified); // Debug: Check verification status
    console.log('Preserving existing username:', existingUsername); // Debug: Check preserved username
    console.log('Preserving existing profile image:', !!existingProfileImage); // Debug: Check preserved image

    const response = await XanoService.saveUserSettings(
      currentUser.sub,
      settings,
      userEmail,
      emailVerified,
      existingUsername, // Preserve existing username
      existingProfileImage // Preserve existing profile image
    );

    // Handle nested response structure from Xano
    const responseData = response.user_settings || response;
    userSettingsId = responseData.id;
    currentUserData = responseData; // Update currentUserData with the response

    console.log('✅ saveSettingsToXano - Updated currentUserData:', {
      id: currentUserData.id,
      username: currentUserData.username,
      hasProfileImage: !!currentUserData.profile_image,
      allKeys: Object.keys(currentUserData)
    });

    // Update original settings
    originalSettings.username = existingUsername;
    originalSettings.profileImage = existingProfileImage;

    // Update profile dropdown if visible
    updateProfileImage();

    console.log('Saved settings to Xano');

  } catch (error) {
    console.error('Failed to save settings to Xano:', error);
    // Don't throw error - let local storage still work
    throw error;
  }
}

// Helper functions
function convertImageToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

function onThemeChange() {
  const themeSelect = document.getElementById('themeSelect');
  if (themeSelect) {
    const selectedTheme = themeSelect.value;

    // Apply theme immediately
    applyTheme(selectedTheme);

    // Mark as changed
    settingsChanged = true;

    console.log('🎨 Theme changed to:', selectedTheme);
  }
}

let usernameCheckTimeout = null;

function onUsernameChange(event) {
  settingsChanged = true;
  console.log('👤 Username changed');
  
  const usernameInput = event.target;
  const username = usernameInput.value.trim();
  
  // Clear previous timeout
  if (usernameCheckTimeout) {
    clearTimeout(usernameCheckTimeout);
  }
  
  // Basic validation first
  if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
    const validation = SecurityUtils.InputValidator.validateUsername(username);
    if (!validation.valid) {
      usernameInput.setCustomValidity(validation.error);
      return;
    }
  } else {
    // Fallback validation if security utils not available
    if (username.length > 50) {
      usernameInput.setCustomValidity('Username cannot exceed 50 characters');
      return;
    }
    if (username && !/^[a-zA-Z0-9 _\-\.]+$/.test(username)) {
      usernameInput.setCustomValidity('Username can only contain letters, numbers, spaces, underscores, hyphens, and periods');
      return;
    }
  }
  
  // Clear validation if basic checks pass
  usernameInput.setCustomValidity('');
  
  // Check availability after a delay (debouncing)
  if (username && username !== (originalSettings ? originalSettings.username : '')) {
    usernameCheckTimeout = setTimeout(async () => {
      try {
        const availabilityCheck = await XanoService.checkUsernameAvailability(username);
        if (!availabilityCheck.available) {
          usernameInput.setCustomValidity(availabilityCheck.message);
        } else if (availabilityCheck.warning) {
          usernameInput.setCustomValidity(availabilityCheck.message);
        } else {
          usernameInput.setCustomValidity('');
        }
      } catch (error) {
        console.warn('Username availability check failed:', error);
        // Don't set validation error for network issues
      }
    }, 1000); // 1 second delay
  }
}

function onProfileImageChange() {
  const input = document.getElementById('profileImageInput');
  const preview = document.getElementById('profileImagePreview');

  if (input && input.files && input.files[0]) {
    const file = input.files[0];

    // Validate file type
    if (!file.type.match(/^image\/(png|jpg|jpeg)$/)) {
      showNotification('Please select a PNG or JPG image file.', true);
      input.value = '';
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      showNotification('Image file must be smaller than 2MB.', true);
      input.value = '';
      return;
    }

    // Show preview
    const reader = new FileReader();
    reader.onload = function(e) {
      if (preview) {
        preview.src = e.target.result;
        preview.style.display = 'block';
      }
    };
    reader.readAsDataURL(file);

    settingsChanged = true;
    console.log('🖼️ Profile image changed');
  }
}

// Coming soon modal functionality
function showComingSoonModal(featureName, description) {
  const modal = document.getElementById('comingSoonModal');
  const title = document.getElementById('comingSoonTitle');
  const desc = document.getElementById('comingSoonDescription');
  
  title.textContent = `${featureName} Coming Soon!`;
  desc.textContent = description;
  
  modal.style.display = 'flex';
}

function hideComingSoonModal() {
  const modal = document.getElementById('comingSoonModal');
  modal.style.display = 'none';
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
  // Initialize Auth0 and theme
  initAuth0();
  initTheme();
  
  // Auth button click - redirect to login page
  document.getElementById('authButton').addEventListener('click', function() {
    console.log('Login button clicked on main page - redirecting to /login');
    window.location.href = '/login';
  });
  
  // Settings button click - open settings modal
  document.getElementById('settingsButton').addEventListener('click', function() {
    openSettingsModal();
  });
  
  // Profile dropdown functionality
  const profileAvatar = document.getElementById('profileAvatar');
  const profileMenu = document.getElementById('profileMenu');
  
  if (profileAvatar) {
    profileAvatar.addEventListener('click', function(e) {
      e.stopPropagation();
      profileMenu.style.display = profileMenu.style.display === 'none' ? 'block' : 'none';
    });
  }
  
  // Close profile menu when clicking outside
  document.addEventListener('click', function() {
    if (profileMenu) {
      profileMenu.style.display = 'none';
    }
  });
  
  // Profile menu items
  document.getElementById('userSettingsMenuItem')?.addEventListener('click', function() {
    openSettingsModal();
  });
  
  document.getElementById('savedArtMenuItem')?.addEventListener('click', function() {
    window.location.href = '/canvas';
  });
  
  document.getElementById('logoutMenuItem')?.addEventListener('click', async function() {
    try {
      await auth0Client.logout({
        logoutParams: {
          returnTo: window.location.origin
        }
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  });
  
  // Settings modal event listeners
  const settingsModal = document.getElementById('settingsModal');
  if (settingsModal) {
    // Close button
    const closeBtn = settingsModal.querySelector('.modal-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => closeSettingsModal());
    }

    // Save button
    const saveBtn = document.getElementById('saveSettingsBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', saveSettings);
    }

    // Cancel button
    const cancelBtn = document.getElementById('cancelSettingsBtn');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => closeSettingsModal());
    }

    // Close modal when clicking outside
    settingsModal.addEventListener('click', function(e) {
      if (e.target === settingsModal) {
        closeSettingsModal();
      }
    });

    // Theme change listener
    const themeSelect = document.getElementById('themeSelect');
    if (themeSelect) {
      themeSelect.addEventListener('change', onThemeChange);
    }

    // Username change listener with validation
    const usernameInput = document.getElementById('usernameInput');
    if (usernameInput) {
      usernameInput.addEventListener('input', (e) => {
        // Use security validation if available
        if (typeof SecurityUtils !== 'undefined' && SecurityUtils.InputValidator) {
          const validation = SecurityUtils.InputValidator.validateUsername(e.target.value);
          if (!validation.valid) {
            e.target.setCustomValidity(validation.error);
          } else {
            e.target.setCustomValidity('');
          }
        }
        onUsernameChange(e);
      });
    }

    // Profile image change listener
    const profileImageInput = document.getElementById('profileImageInput');
    if (profileImageInput) {
      profileImageInput.addEventListener('change', onProfileImageChange);
    }
  }

  // Coming soon buttons
  document.getElementById('animationStudioBtn').addEventListener('click', function() {
    showComingSoonModal(
      'Animation Studio',
      'Create amazing pixel art animations with timeline controls, onion skinning, and export options.'
    );
  });
  
  document.getElementById('galleryBtn').addEventListener('click', function() {
    showComingSoonModal(
      'Community Gallery',
      'Discover, share, and get inspired by pixel art creations from artists around the world.'
    );
  });
  
  // Modal close functionality
  document.querySelector('#comingSoonModal .modal-close-btn').addEventListener('click', hideComingSoonModal);
  
  document.getElementById('comingSoonModal').addEventListener('click', function(e) {
    if (e.target === this) {
      hideComingSoonModal();
    }
  });
  
  // Escape key to close modals
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      hideComingSoonModal();
      closeSettingsModal();
    }
  });

  // Add click handler for logo to navigate to home (refresh current page since this is the home page)
  const leftToolbar = document.querySelector('.left-toolbar');
  if (leftToolbar) {
    leftToolbar.addEventListener('click', function() {
      window.location.href = '/';
    });
  }
});

// Handle browser back/forward navigation
window.addEventListener('popstate', function() {
  // Clean up any open modals
  hideComingSoonModal();
});
