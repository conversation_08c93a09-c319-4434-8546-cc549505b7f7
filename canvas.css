body {
      font-family: sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f0f0f0;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      overflow: auto; /* Allow scrolling when content exceeds viewport */
      height: 100vh; /* Full viewport height */

      /* Prevent zoom-based layout changes */
      zoom: 1; /* Reset any inherited zoom */
      -webkit-text-size-adjust: none; /* Prevent text size adjustment on mobile */
      -moz-text-size-adjust: none;
      -ms-text-size-adjust: none;
      text-size-adjust: none;
    }

    #fullWidthWrapper {
      display: flex;
      flex-direction: row;
      width: 100vw;
      height: 100vh; /* Full viewport height */
      box-sizing: border-box;
    }

    .banner-div {
      flex: 1; /* Take remaining space on each side */
      background-color: transparent; /* Clear background like main container */
      border: none; /* Remove border for clean appearance */
      min-width: 200px; /* Minimum width for banner areas */
      box-sizing: border-box;
    }

    #leftBanner {
      border-left: none; /* Remove left border since mainContainer will have its own border */
      display: flex;
      flex-direction: column;
      margin-right: 5px; /* Add spacing between main container and right banner */
      position: relative;
    }

    #rightBanner {
      border-left: none; /* Remove left border since mainContainer will have its own border */
      display: flex;
      flex-direction: column;
      margin-left: 5px; /* Add spacing between main container and right banner */
      position: relative;
    }

    /* Banner image container styling */
    .banner-image-container {
      flex: 1; /* Fill remaining space below toolbar */
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 5px; /* Add spacing from edges and toolbars */
      margin-bottom: 5px;
      box-sizing: border-box;
      overflow: hidden; /* Prevent image overflow */
      border-radius: 3px; /* Slight rounding to match interface */
      position: relative; /* For proper image positioning */
    }

    .banner-image {
      width: 100%; /* Zoom out to 90% of container */
      height: 100%; /* Zoom out to 90% of container */
      object-fit: cover; /* Cover the area while maintaining aspect ratio */
      object-position: center; /* Center the image */
      border-radius: 3px; /* Slight rounding to match interface */
      display: block; /* Ensure proper display */
    }

    /* Banner ad container styling */
    .banner-ad-container {
      position: absolute;
      top: 57px; /* Position below toolbar */
      left: 5px;
      right: 5px;
      bottom: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent; /* Fully transparent */
      overflow: hidden; /* Prevent ad overflow */
      border-radius: 3px; /* Slight rounding to match interface */
      z-index: 10; /* Above banner image */
      pointer-events: none; /* Allow clicks to pass through to banner image */
    }

    .banner-ad-container .adsbygoogle {
      width: 100%;
      height: 100%;
      border-radius: 3px;
      pointer-events: auto; /* Re-enable clicks for the ad itself */
    }

    .left-toolbar {
      margin: 0; /* No margins to align with main toolbar */
      padding: 8px; /* Match main toolbar padding */
      background-color: #e8e8e8; /* Match brush/layer options background */
      border: 1px solid #ccc; /* Match brush/layer options border */
      border-radius: 3px; /* Match brush/layer options border radius */
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      position: relative;
      box-sizing: border-box;
      width: 100%; /* Fill the width of rightBanner */
      height: 52px; /* Match main toolbar height exactly */
      flex-shrink: 0; /* Prevent shrinking */
      opacity: 1; /* Make visible even though parent has opacity 0 */
    }

    .right-toolbar {
      margin: 0; /* No margins to align with main toolbar */
      padding: 6px; /* Slightly reduced padding to give more space */
      background-color: #e8e8e8; /* Match brush/layer options background */
      border: 1px solid #ccc; /* Match brush/layer options border */
      border-radius: 3px; /* Match brush/layer options border radius */
      display: flex;
      flex-wrap: nowrap; /* Prevent wrapping to keep buttons on same line */
      align-items: center;
      justify-content: center; /* Center the buttons horizontally */
      position: relative;
      box-sizing: border-box;
      width: 100%; /* Fill the width of rightBanner */
      height: 52px; /* Match main toolbar height exactly */
      flex-shrink: 0; /* Prevent shrinking */
      opacity: 1; /* Make visible even though parent has opacity 0 */
      gap: 6px; /* Add gap between buttons instead of margin */
    }

    .bottom-toolbar {
      margin: 2px auto 0 auto; /* Reduced top margin */
      padding: 4px 16px; /* Reduced vertical padding */
      background-color: #e8e8e8; /* Match brush/layer options background */
      border: 1px solid #ccc; /* Match brush/layer options border */
      border-radius: 3px; /* Match brush/layer options border radius */
      display: flex;
      flex-wrap: nowrap; /* Prevent wrapping to keep height minimal */
      align-items: center;
      justify-content: space-between; /* Distribute sections evenly */
      position: relative;
      box-sizing: border-box;
      width: 1090px; /* Match main toolbar width */
      height: 40px; /* Reduced height to fit viewport */
      flex-shrink: 0; /* Prevent shrinking */
    }

    /* Right toolbar button styling - match shared-layout.css behavior */
    .auth-button, .settings-button {
      padding: 6px 12px;
      border: 1px solid #ccc;
      border-radius: 3px;
      background-color: #f5f5f5;
      color: #333;
      cursor: pointer;
      transition: background-color 0.2s ease, border-color 0.2s ease;
      font-size: 0.9em;
      text-decoration: none;
      display: inline-block;
      text-align: center;
      white-space: nowrap;
    }

    /* Profile dropdown styling */
    .profile-dropdown {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .profile-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #e0e0e0;
      border: 2px solid #999;
      cursor: pointer;
      overflow: hidden;
      transition: border-color 0.2s ease;
    }

    .profile-avatar:hover {
      border-color: #666;
    }

    .profile-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .profile-menu {
      position: absolute;
      top: 45px;
      right: 0;
      background-color: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      z-index: 1000;
      min-width: 150px;
    }

    .profile-menu-item {
      padding: 10px 15px;
      cursor: pointer;
      border-bottom: 1px solid #eee;
      transition: background-color 0.2s ease;
    }

    .profile-menu-item:last-child {
      border-bottom: none;
    }

    .profile-menu-item:hover {
      background-color: #f5f5f5;
    }

    .auth-button:hover, .settings-button:hover {
      background-color: #e0e0e0;
      border-color: #999;
    }

    .auth-button:active, .settings-button:active {
      background-color: #d0d0d0;
    }

    /* Login prompt styling */
    .login-prompt {
      margin-top: 20px;
      padding: 15px;
      background-color: #f0f8ff;
      border: 1px solid #b0d4f1;
      border-radius: 4px;
      text-align: center;
    }

    .login-prompt p {
      margin: 5px 0;
    }

    /* User Settings Modal Styling */
    .user-settings-modal {
      max-width: 600px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .username-input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 3px;
      font-size: 0.9em;
    }

    .profile-image-upload {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .profile-preview {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      border: 2px solid #ccc;
      object-fit: cover;
    }

    .file-input {
      display: none;
    }

    .upload-btn {
      padding: 6px 12px;
      background-color: #f5f5f5;
      border: 1px solid #999;
      border-radius: 3px;
      cursor: pointer;
      font-size: 0.85em;
      transition: background-color 0.2s ease;
    }

    .upload-btn:hover {
      background-color: #e0e0e0;
    }

    /* Login button specific styling */
    .auth-button.login {
      color: #2c5aa0;
      border-color: #2c5aa0;
    }

    .auth-button.login:hover {
      background-color: #e8f0ff;
    }

    /* Logout button specific styling */
    .auth-button.logout {
      color: #a02c2c;
      border-color: #a02c2c;
    }

    .auth-button.logout:hover {
      background-color: #ffe8e8;
    }

    /* Verify email button specific styling */
    .auth-button.verify {
      color: #a07c2c;
      border-color: #a07c2c;
      background-color: #fff8e8;
    }

    .auth-button.verify:hover {
      background-color: #fff0d0;
    }

    /* Email verification message styling */
    .email-verification-message {
      position: absolute;
      top: 60px;
      right: 0;
      background-color: #fff8e8;
      border: 1px solid #a07c2c;
      border-radius: 3px;
      padding: 12px;
      width: 280px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      z-index: 1000;
      font-size: 0.85em;
    }

    .email-verification-message p {
      margin: 0 0 8px 0;
      color: #333;
    }

    .email-verification-message strong {
      color: #a07c2c;
    }

    .verification-buttons {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }

    .resend-verification-btn, .logout-verification-btn {
      padding: 6px 10px;
      font-size: 0.8em;
      font-weight: 500;
      border: 1px solid #a07c2c;
      border-radius: 3px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      flex: 1;
    }

    .resend-verification-btn {
      background-color: #a07c2c;
      color: white;
    }

    .resend-verification-btn:hover {
      background-color: #8a6825;
    }

    .logout-verification-btn {
      background-color: #fff;
      color: #a07c2c;
    }

    .logout-verification-btn:hover {
      background-color: #f5f5f5;
    }



    /* Settings Modal Styling */
    .settings-modal {
      width: 500px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .settings-section {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ddd;
    }

    .settings-section:last-child {
      border-bottom: none;
    }

    .settings-section h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 1.1em;
    }

    .setting-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 12px;
    }

    .setting-item > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;
    }

    .setting-item label {
      font-weight: 500;
      color: #555;
    }

    .setting-description {
      font-size: 0.85em;
      color: #777;
      margin-top: 4px;
      line-height: 1.3;
    }

    .setting-item select {
      padding: 4px 8px;
      border: 1px solid #ccc;
      border-radius: 3px;
      background-color: white;
    }

    /* Keybind Settings */
    .keybind-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 8px;
      background-color: #fafafa;
    }

    .keybind-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 8px;
      margin-bottom: 4px;
      background-color: white;
      border: 1px solid #eee;
      border-radius: 2px;
      gap: 8px;
    }

    .keybind-item:last-child {
      margin-bottom: 0;
    }

    .keybind-label {
      font-weight: 500;
      color: #333;
      flex: 1;
    }

    .keybind-input {
      padding: 4px 8px;
      border: 1px solid #ccc;
      border-radius: 2px;
      width: 80px;
      text-align: center;
      font-family: monospace;
      background-color: white;
    }

    .keybind-input:focus {
      outline: none;
      border-color: #2c5aa0;
      background-color: #f0f8ff;
    }

    .keybind-input.duplicate {
      border-color: #d32f2f;
      background-color: #ffebee;
    }

    .restore-keybind-btn {
      padding: 4px 6px;
      border: 1px solid #ccc;
      border-radius: 2px;
      background-color: #f5f5f5;
      color: #666;
      cursor: pointer;
      font-size: 12px;
      min-width: 24px;
      transition: background-color 0.2s ease;
    }

    .restore-keybind-btn:hover {
      background-color: #e0e0e0;
      color: #333;
    }

    .restore-keybind-btn:active {
      background-color: #d0d0d0;
    }

    .keybind-actions {
      display: flex;
      gap: 12px;
      margin-top: 16px;
      justify-content: flex-end;
    }

    .revert-btn, .save-btn {
      padding: 8px 16px;
      border: 1px solid #ccc;
      border-radius: 3px;
      cursor: pointer;
      font-size: 0.9em;
      transition: background-color 0.2s ease;
      background-color: #f5f5f5;
      color: #333;
    }

    .revert-btn:hover, .save-btn:hover {
      background-color: #e0e0e0;
    }

    .revert-btn:active, .save-btn:active {
      background-color: #d0d0d0;
    }

    /* Sync Status Indicator */
    .sync-status {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 8px 12px;
      margin-bottom: 16px;
      border-radius: 4px;
      font-size: 0.9em;
      transition: all 0.3s ease;
    }

    .sync-status.synced {
      background-color: #e8f5e8;
      border: 1px solid #4caf50;
      color: #2e7d32;
    }

    .sync-status.syncing {
      background-color: #fff3e0;
      border: 1px solid #ff9800;
      color: #f57c00;
    }

    .sync-status.error {
      background-color: #ffebee;
      border: 1px solid #f44336;
      color: #c62828;
    }

    .sync-status.offline {
      background-color: #f5f5f5;
      border: 1px solid #9e9e9e;
      color: #616161;
    }

    #syncStatusIcon {
      font-weight: bold;
      font-size: 1.1em;
    }

    /* Email Verification Status Indicator */
    .verification-status {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 8px 12px;
      margin-bottom: 16px;
      border-radius: 4px;
      font-size: 0.9em;
      transition: all 0.3s ease;
    }

    .verification-status.verified {
      background-color: #e8f5e8;
      border: 1px solid #4caf50;
      color: #2e7d32;
    }

    .verification-status.unverified {
      background-color: #fff3e0;
      border: 1px solid #ff9800;
      color: #f57c00;
    }

    #verificationStatusIcon {
      font-weight: bold;
      font-size: 1.1em;
    }

    /* Dark Theme Styles */
    body.dark-theme {
      background-color: #1a1a1a;
      color: #e0e0e0;
    }

    body.dark-theme .banner-div {
      background-color: #1a1a1a;
    }

    body.dark-theme #mainContainer {
      background-color: #1a1a1a;
    }

    body.dark-theme #toolbar,
    body.dark-theme .left-toolbar,
    body.dark-theme .right-toolbar,
    body.dark-theme .bottom-toolbar {
      background-color: #2d2d2d;
      border-color: #444;
      color: #e0e0e0;
    }

    body.dark-theme #brushOptionsBar,
    body.dark-theme #layersPanelContainer {
      background-color: #2d2d2d;
      border-color: #444;
      color: #e0e0e0;
    }

    body.dark-theme #canvasTabsBar {
      background-color: #3d3d3d;
      border-color: #444;
    }

    body.dark-theme .canvas-tab {
      background-color: #2d2d2d;
      border-color: #444;
      color: #e0e0e0;
    }

    body.dark-theme .canvas-tab.active {
      background-color: #4d4d4d;
    }

    body.dark-theme .footer-link {
      color: #b0b0b0;
    }

    body.dark-theme .footer-link:hover {
      color: #6db3f2;
      background-color: #333;
    }

    body.dark-theme .copyright-text {
      color: #888;
    }

    body.dark-theme .modal-content {
      background-color: #2d2d2d;
      border-color: #444;
      color: #e0e0e0;
    }

    body.dark-theme .settings-section {
      border-bottom-color: #444;
    }

    body.dark-theme .settings-section h3 {
      color: #e0e0e0;
    }

    body.dark-theme .setting-item label {
      color: #b0b0b0;
    }

    body.dark-theme .setting-description {
      color: #888;
    }

    /* Canvas Size Warning Tooltip */
    .canvas-warning-tooltip {
      position: fixed;
      top: 120px;
      right: 20px;
      width: 280px;
      background: white;
      border: 2px solid #e67e22;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      font-size: 0.9em;
    }

    .warning-tooltip-content {
      padding: 12px;
    }

    .warning-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    .warning-icon {
      font-size: 1.1em;
      margin-right: 6px;
    }

    .warning-title {
      font-weight: 600;
      color: #e67e22;
      flex: 1;
    }

    .warning-close {
      background: none;
      border: none;
      font-size: 1.2em;
      cursor: pointer;
      color: #999;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .warning-close:hover {
      color: #666;
    }

    .canvas-warning-tooltip p {
      margin: 0 0 10px 0;
      line-height: 1.4;
      color: #555;
    }

    .warning-tooltip-actions {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
    }

    .warning-btn-small {
      padding: 6px 10px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.8em;
      font-weight: 500;
      transition: all 0.2s ease;
      flex: 1;
      min-width: 60px;
    }

    .warning-btn-small.primary {
      background-color: #27ae60;
      color: white;
    }

    .warning-btn-small.primary:hover {
      background-color: #219a52;
    }

    .warning-btn-small.secondary {
      background-color: #3498db;
      color: white;
    }

    .warning-btn-small.secondary:hover {
      background-color: #2980b9;
    }

    .warning-btn-small.tertiary {
      background-color: #95a5a6;
      color: white;
      flex: 2;
    }

    .warning-btn-small.tertiary:hover {
      background-color: #7f8c8d;
    }

    .warning-tooltip-arrow {
      position: absolute;
      top: -8px;
      right: 30px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid #e67e22;
    }

    /* Drawing Status Indicator */
    .drawing-status-indicator {
      position: fixed;
      top: 80px;
      right: 20px;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 0.85em;
      z-index: 1001;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }

    .status-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-spinner {
      width: 12px;
      height: 12px;
      border: 2px solid #ffffff40;
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .progress-circle-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
    }

    .progress-circle {
      position: absolute;
      top: 0;
      left: 0;
    }

    .progress-circle-fill {
      transition: stroke-dashoffset 0.3s ease;
    }

    .progress-percentage {
      position: absolute;
      font-size: 8px;
      font-weight: bold;
      color: white;
      text-align: center;
      line-height: 1;
      pointer-events: none;
    }

    .cancel-drawing-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      border-radius: 3px;
      padding: 2px 6px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      margin-left: 4px;
      transition: background-color 0.2s ease;
      line-height: 1;
    }

    .cancel-drawing-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }

    .cancel-drawing-btn:active {
      background: rgba(255, 255, 255, 0.4);
      transform: translateY(1px);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Dark theme for canvas warning tooltip */
    body.dark-theme .canvas-warning-tooltip {
      background: #2d2d2d;
      border-color: #f39c12;
      color: #e0e0e0;
    }

    body.dark-theme .warning-title {
      color: #f39c12;
    }

    body.dark-theme .canvas-warning-tooltip p {
      color: #b0b0b0;
    }

    body.dark-theme .warning-close {
      color: #b0b0b0;
    }

    body.dark-theme .warning-close:hover {
      color: #e0e0e0;
    }

    body.dark-theme .warning-tooltip-arrow {
      border-bottom-color: #f39c12;
    }

    /* Image Import Warning Modal */
    .image-import-warning-modal {
      max-width: 600px;
      width: 90%;
    }

    .warning-modal-content {
      text-align: left;
    }

    .warning-modal-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 2px solid #e0e0e0;
    }

    .warning-modal-header .warning-icon {
      font-size: 2em;
      margin-right: 15px;
    }

    .warning-modal-header h2 {
      margin: 0;
      color: #2c5aa0;
      font-size: 1.5em;
    }

    .warning-modal-body {
      margin-bottom: 25px;
    }

    .warning-modal-body p {
      margin: 0 0 15px 0;
      line-height: 1.6;
      color: #555;
    }

    .import-mode-comparison {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin: 20px 0;
    }

    .import-mode-option {
      padding: 20px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      background-color: #f9f9f9;
    }

    .import-mode-option.recommended {
      border-color: #27ae60;
      background-color: #e8f5e8;
    }

    .import-mode-option h4 {
      margin: 0 0 10px 0;
      color: #2c5aa0;
      font-size: 1.1em;
    }

    .import-mode-option.recommended h4 {
      color: #27ae60;
    }

    .import-mode-option p {
      margin: 5px 0;
      font-size: 0.9em;
      color: #666;
    }

    .import-tip {
      margin-top: 20px;
      padding: 15px;
      background-color: #e8f4fd;
      border: 1px solid #bee5eb;
      border-radius: 6px;
      border-left: 4px solid #17a2b8;
    }

    .import-tip h4 {
      margin: 0 0 8px 0;
      color: #0c5460;
      font-size: 1em;
    }

    .import-tip p {
      margin: 0;
      font-size: 0.9em;
      color: #0c5460;
      line-height: 1.4;
    }

    .warning-modal-actions {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .warning-btn {
      padding: 12px 20px;
      border: none;
      border-radius: 6px;
      font-size: 0.9em;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 140px;
    }

    .warning-btn.primary {
      background-color: #27ae60;
      color: white;
    }

    .warning-btn.primary:hover {
      background-color: #219a52;
    }

    .warning-btn.secondary {
      background-color: #3498db;
      color: white;
    }

    .warning-btn.secondary:hover {
      background-color: #2980b9;
    }

    .warning-btn.tertiary {
      background-color: #95a5a6;
      color: white;
    }

    .warning-btn.tertiary:hover {
      background-color: #7f8c8d;
    }

    /* Dark theme for image import warning modal */
    body.dark-theme .image-import-warning-modal {
      background-color: #2d2d2d;
      color: #e0e0e0;
    }

    body.dark-theme .warning-modal-header {
      border-bottom-color: #555;
    }

    body.dark-theme .warning-modal-header h2 {
      color: #6db3f2;
    }

    body.dark-theme .warning-modal-body p {
      color: #b0b0b0;
    }

    body.dark-theme .import-mode-option {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .import-mode-option.recommended {
      border-color: #27ae60;
      background-color: #1a3d1a;
    }

    body.dark-theme .import-mode-option h4 {
      color: #6db3f2;
    }

    body.dark-theme .import-mode-option.recommended h4 {
      color: #2ecc71;
    }

    body.dark-theme .import-mode-option p {
      color: #b0b0b0;
    }

    body.dark-theme .import-tip {
      background-color: #1a3d3d;
      border-color: #2d5a5a;
      border-left-color: #17a2b8;
    }

    body.dark-theme .import-tip h4 {
      color: #5bc0de;
    }

    body.dark-theme .import-tip p {
      color: #a0c4c7;
    }

    /* Responsive design for image import modal */
    @media (max-width: 600px) {
      .import-mode-comparison {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .warning-modal-actions {
        flex-direction: column;
      }

      .warning-btn {
        min-width: auto;
        width: 100%;
      }
    }

    body.dark-theme .setting-item select,
    body.dark-theme .keybind-input {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .keybind-list {
      background-color: #2d2d2d;
      border-color: #444;
    }

    body.dark-theme .keybind-item {
      background-color: #3d3d3d;
      border-color: #555;
    }

    body.dark-theme .keybind-label {
      color: #e0e0e0;
    }

    body.dark-theme .restore-keybind-btn {
      background-color: #4d4d4d;
      border-color: #666;
      color: #b0b0b0;
    }

    body.dark-theme .restore-keybind-btn:hover {
      background-color: #5d5d5d;
      color: #e0e0e0;
    }

    body.dark-theme .restore-keybind-btn:active {
      background-color: #3d3d3d;
    }

    /* Dark theme for keybind action buttons */
    body.dark-theme .revert-btn,
    body.dark-theme .save-btn {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .revert-btn:hover,
    body.dark-theme .save-btn:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme .revert-btn:active,
    body.dark-theme .save-btn:active {
      background-color: #2d2d2d;
    }

    /* Dark theme for sync status */
    body.dark-theme .sync-status.synced {
      background-color: #1b3d1b;
      border-color: #4caf50;
      color: #81c784;
    }

    body.dark-theme .sync-status.syncing {
      background-color: #3d2f1b;
      border-color: #ff9800;
      color: #ffb74d;
    }

    body.dark-theme .sync-status.error {
      background-color: #3d1b1b;
      border-color: #f44336;
      color: #e57373;
    }

    body.dark-theme .sync-status.offline {
      background-color: #2d2d2d;
      border-color: #666;
      color: #b0b0b0;
    }

    /* Dark theme for verification status */
    body.dark-theme .verification-status.verified {
      background-color: #1b3d1b;
      border-color: #4caf50;
      color: #81c784;
    }

    body.dark-theme .verification-status.unverified {
      background-color: #3d2f1b;
      border-color: #ff9800;
      color: #ffb74d;
    }

    /* Dark theme for email verification message */
    body.dark-theme .email-verification-message {
      background-color: #3d2f1b;
      border-color: #ff9800;
      color: #e0e0e0;
    }

    body.dark-theme .email-verification-message strong {
      color: #ffb74d;
    }

    body.dark-theme .resend-verification-btn {
      background-color: #ff9800;
      color: #1a1a1a;
    }

    body.dark-theme .resend-verification-btn:hover {
      background-color: #f57c00;
    }

    body.dark-theme .logout-verification-btn {
      background-color: #3d3d3d;
      border-color: #ff9800;
      color: #ffb74d;
    }

    body.dark-theme .logout-verification-btn:hover {
      background-color: #4d4d4d;
    }

    /* Dark theme for form elements */
    body.dark-theme input[type="range"] {
      background-color: transparent; /* Keep slider container transparent */
    }

    body.dark-theme input[type="range"]::-webkit-slider-track {
      background-color: #444 !important;
      border: 1px solid #555 !important;
    }

    body.dark-theme input[type="range"]::-webkit-slider-thumb {
      background-color: #e0e0e0 !important;
      border: 1px solid #999 !important;
    }

    body.dark-theme input[type="range"]::-moz-range-track {
      background-color: #444 !important;
      border: 1px solid #555 !important;
    }

    body.dark-theme input[type="range"]::-moz-range-thumb {
      background-color: #e0e0e0 !important;
      border: 1px solid #999 !important;
    }

    body.dark-theme select,
    body.dark-theme input[type="text"],
    body.dark-theme input[type="number"] {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme select:focus,
    body.dark-theme input[type="text"]:focus,
    body.dark-theme input[type="number"]:focus {
      border-color: #6db3f2;
      background-color: #4d4d4d;
    }

    body.dark-theme button {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme button:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme button:active {
      background-color: #2d2d2d;
    }

    /* Dark theme for toolbar buttons */
    body.dark-theme #toolbar button {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme #toolbar button:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme #toolbar button:active {
      background-color: #2d2d2d;
    }

    /* Dark theme for scrollbars */
    body.dark-theme ::-webkit-scrollbar {
      width: 12px;
      height: 12px;
    }

    body.dark-theme ::-webkit-scrollbar-track {
      background-color: #2d2d2d;
      border-radius: 6px;
    }

    body.dark-theme ::-webkit-scrollbar-thumb {
      background-color: #555;
      border-radius: 6px;
      border: 2px solid #2d2d2d;
    }

    body.dark-theme ::-webkit-scrollbar-thumb:hover {
      background-color: #666;
    }

    body.dark-theme ::-webkit-scrollbar-corner {
      background-color: #2d2d2d;
    }

    /* Dark theme for layer tabs and specific elements */
    body.dark-theme .layer-item {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .layer-item:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme .layer-item.active-layer {
      background-color: #5d5d5d;
      border-color: #6db3f2;
    }

    body.dark-theme .layer-visibility-btn,
    body.dark-theme .layer-delete-btn {
      background-color: #4d4d4d;
      border-color: #666;
      color: #e0e0e0;
    }

    body.dark-theme .layer-visibility-btn:hover,
    body.dark-theme .layer-delete-btn:hover {
      background-color: #5d5d5d;
    }

    /* Dark theme for canvas tabs */
    body.dark-theme .canvas-tab-close {
      background-color: #4d4d4d;
      color: #e0e0e0;
    }

    body.dark-theme .canvas-tab-close:hover {
      background-color: #d32f2f;
      color: white;
    }

    /* Dark theme for labels and text */
    body.dark-theme label {
      color: #e0e0e0;
    }

    body.dark-theme span {
      color: #e0e0e0;
    }

    /* Dark theme for image inversion */
    body.dark-theme .title-image,
    body.dark-theme .banner-image {
      filter: invert(1) hue-rotate(180deg);
    }

    /* Dark theme for specific toolbar elements */
    body.dark-theme #toolbar label,
    body.dark-theme #toolbar span {
      color: #e0e0e0;
    }

    body.dark-theme #brushOptionsBar label,
    body.dark-theme #brushOptionsBar span {
      color: #e0e0e0;
    }

    body.dark-theme #layersPanelContainer label,
    body.dark-theme #layersPanelContainer span {
      color: #e0e0e0;
    }

    /* Dark theme for modal elements */
    body.dark-theme .modal-close-btn {
      background-color: #4d4d4d;
      color: #e0e0e0;
      border-color: #666;
    }

    body.dark-theme .modal-close-btn:hover {
      background-color: #d32f2f;
      color: white;
    }

    /* Dark theme for specific button overrides */
    body.dark-theme .auth-button.login {
      background-color: #2c4a80;
      border-color: #4a6fa5;
      color: #e0e0e0;
    }

    body.dark-theme .auth-button.login:hover {
      background-color: #1e3a70;
    }

    body.dark-theme .auth-button.logout {
      background-color: #802c2c;
      border-color: #a04a4a;
      color: #e0e0e0;
    }

    body.dark-theme .auth-button.logout:hover {
      background-color: #701e1e;
    }

    body.dark-theme .auth-button.verify {
      background-color: #806c2c;
      border-color: #a0854a;
      color: #e0e0e0;
    }

    body.dark-theme .auth-button.verify:hover {
      background-color: #705e1e;
    }

    /* Dark theme for profile dropdown */
    body.dark-theme .profile-avatar {
      background-color: #3d3d3d;
      border-color: #666;
    }

    body.dark-theme .profile-avatar:hover {
      border-color: #888;
    }

    body.dark-theme .profile-menu {
      background-color: #2d2d2d;
      border-color: #444;
      color: #e0e0e0;
    }

    body.dark-theme .profile-menu-item {
      border-bottom-color: #444;
    }

    body.dark-theme .profile-menu-item:hover {
      background-color: #3d3d3d;
    }

    /* Dark theme for user settings modal */
    body.dark-theme .username-input {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .profile-preview {
      border-color: #555;
    }

    body.dark-theme .upload-btn {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .upload-btn:hover {
      background-color: #4d4d4d;
    }

    /* Dark theme for login prompt */
    body.dark-theme .login-prompt {
      background-color: #2d3748;
      border-color: #4a5568;
      color: #e2e8f0;
    }

    /* Responsive adjustments - match shared-layout.css behavior */
    @media (max-width: 1200px) {
      .auth-button, .settings-button {
        padding: 4px 6px;
        font-size: 0.75em;
        min-width: 40px;
      }

      .profile-avatar {
        width: 35px;
        height: 35px;
      }

      .right-toolbar {
        padding: 4px;
        gap: 4px;
      }
    }

    @media (max-width: 768px) {
      .profile-avatar {
        width: 30px;
        height: 30px;
      }
    }

    /* Dark theme for canvas area */
    body.dark-theme #canvasArea {
      background-color: #2d2d2d;
      border-color: #444;
    }

    body.dark-theme #canvasContainer {
      background-color: #1a1a1a;
    }

    /* Dark theme checkered background */
    body.dark-theme #canvasContainer.checkerboard {
      background-color: transparent;
      background-image:
        linear-gradient(45deg, #444 25%, transparent 25%),
        linear-gradient(-45deg, #444 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #444 75%),
        linear-gradient(-45deg, transparent 75%, #444 75%);
      background-size: 20px 20px;
      background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    }

    /* Dark theme for minimap */
    body.dark-theme #minimapCanvas {
      background-color: #1a1a1a;
      border-color: #555;
    }

    /* Dark theme for add new canvas button */
    body.dark-theme #addNewCanvasBtn {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme #addNewCanvasBtn:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme #addNewCanvasBtn:active {
      background-color: #2d2d2d;
    }

    body.dark-theme #addNewCanvasBtn:disabled {
      background-color: #2a2a2a;
      border-color: #444;
      color: #666;
    }

    /* Dark theme for zoom controls - keep container transparent */
    body.dark-theme #zoomControls {
      background-color: transparent;
      color: #e0e0e0;
    }

    /* Dark theme for option elements in selects */
    body.dark-theme option {
      background-color: #3d3d3d;
      color: #e0e0e0;
    }

    /* Dark theme for grid size modal elements */
    body.dark-theme #currentSizeDiv,
    body.dark-theme #previewSizeDiv {
      background-color: #2d2d2d;
      border-color: #444;
      color: #e0e0e0;
    }

    body.dark-theme .grid-size-option {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .grid-size-option:hover {
      background-color: #4d4d4d !important;
      border-color: #6db3f2 !important;
    }

    body.dark-theme .grid-size-option.selected {
      background-color: #5d5d5d;
      border-color: #6db3f2;
    }

    body.dark-theme #cancelSizeChangeBtn {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme #cancelSizeChangeBtn:hover {
      background-color: #4d4d4d;
    }

    /* Dark theme for preview containers */
    body.dark-theme .preview-container {
      background-color: #2d2d2d;
      border-color: #444;
    }

    /* Dark theme for modal titles */
    body.dark-theme .modal-content h2,
    body.dark-theme .modal-content h3 {
      color: #e0e0e0;
    }

    /* Dark theme for canvas preview elements */
    body.dark-theme #currentCanvasPreview,
    body.dark-theme #newCanvasPreview {
      background-color: #1a1a1a;
      border-color: #444;
    }

    /* Dark theme for remaining specific elements */

    /* Canvas edit and delete buttons */
    body.dark-theme .canvas-edit-btn,
    body.dark-theme .canvas-delete-btn,
    body.dark-theme .canvas-tab-controls button {
      background-color: #3d3d3d !important;
      border-color: #555 !important;
      color: #e0e0e0 !important;
    }

    body.dark-theme .canvas-edit-btn:hover,
    body.dark-theme .canvas-tab-controls button:hover {
      background-color: #4d4d4d !important;
    }

    body.dark-theme .canvas-delete-btn:hover {
      background-color: #d32f2f !important;
      color: white !important;
    }

    /* Layer visibility toggle */
    body.dark-theme .layer-visibility-toggle,
    body.dark-theme .visibility-btn,
    body.dark-theme .layer-visibility {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .layer-visibility-toggle:hover,
    body.dark-theme .visibility-btn:hover,
    body.dark-theme .layer-visibility:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme .layer-visibility-toggle.visible,
    body.dark-theme .visibility-btn.visible,
    body.dark-theme .layer-visibility.visible {
      background-color: #5d5d5d;
      border-color: #777;
    }

    /* Brush opacity container */
    body.dark-theme .brush-opacity-container,
    body.dark-theme #brushOpacityContainer {
      background-color: #2d2d2d;
      border-color: #444;
      color: #e0e0e0;
    }

    /* Mirror options container */
    body.dark-theme .mirror-options-container,
    body.dark-theme #mirrorOptionsContainer,
    body.dark-theme #hatchScaleContainer,
    body.dark-theme #sprayRadiusContainer,
    body.dark-theme #glowSizeContainer,
    body.dark-theme #radialMirrorSettings {
      background-color: #2d2d2d !important;
      border-color: #444 !important;
      color: #e0e0e0 !important;
    }

    /* Layer opacity container */
    body.dark-theme .layer-opacity-control,
    body.dark-theme .opacity-control-set {
      background-color: #2d2d2d;
      border-color: #444;
      color: #e0e0e0;
    }

    /* Undo and redo buttons */
    body.dark-theme .undo-btn,
    body.dark-theme .redo-btn,
    body.dark-theme #mobileUndoBtn,
    body.dark-theme #mobileRedoBtn {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .undo-btn:hover,
    body.dark-theme .redo-btn:hover,
    body.dark-theme #mobileUndoBtn:hover,
    body.dark-theme #mobileRedoBtn:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme .undo-btn:disabled,
    body.dark-theme .redo-btn:disabled,
    body.dark-theme #mobileUndoBtn:disabled,
    body.dark-theme #mobileRedoBtn:disabled {
      background-color: #2d2d2d;
      color: #666;
      border-color: #444;
    }

    /* Dark theme for selection action buttons */
    body.dark-theme .selection-action-btn {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .selection-action-btn:hover:not(:disabled) {
      background-color: #4d4d4d;
    }

    body.dark-theme .selection-action-btn:active:not(:disabled) {
      background-color: #2d2d2d;
    }

    body.dark-theme .selection-action-btn:disabled {
      background-color: #2d2d2d;
      color: #666;
      border-color: #444;
    }

    /* Dark theme for selection info divs */
    body.dark-theme .selection-info-div {
      background-color: #2d2d2d !important;
      border-color: #444 !important;
      color: #e0e0e0 !important;
    }

    /* Dark theme for tool info divs */
    body.dark-theme .tool-info-div {
      background-color: #2d2d2d !important;
      border-color: #444 !important;
      color: #e0e0e0 !important;
    }

    /* Dark theme for layer actions buttons */
    body.dark-theme #layerActions button {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme #layerActions button:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme #layerActions button:active {
      background-color: #2d2d2d;
    }

    /* Dark theme for layer controls buttons */
    body.dark-theme .layer-controls button {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .layer-controls button:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme .layer-controls button:active {
      background-color: #2d2d2d;
    }

    /* Dark theme for selection option buttons */
    body.dark-theme .selection-option-btn {
      background-color: #3d3d3d;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-theme .selection-option-btn:hover {
      background-color: #4d4d4d;
    }

    body.dark-theme .selection-option-btn:active {
      background-color: #2d2d2d;
    }

    /* Footer sections styling */
    .footer-section {
      display: flex;
      align-items: center;
      gap: 8px; /* Reduced space between links within a section */
    }

    .footer-section.core-info {
      flex: 2; /* Take more space for core info links */
    }

    .footer-section.legal-policy {
      flex: 1;
    }

    .footer-section.community-social {
      flex: 2; /* Take more space for community links */
    }

    .footer-section.copyright {
      flex: 1;
      justify-content: flex-end; /* Align copyright to the right */
    }

    /* Footer link styling */
    .footer-link {
      color: #555;
      text-decoration: none;
      font-size: 0.75em; /* Reduced font size */
      padding: 2px 6px; /* Reduced padding */
      border-radius: 2px;
      transition: all 0.2s ease;
      white-space: nowrap; /* Prevent link text from wrapping */
    }

    .footer-link:hover {
      color: #2c5aa0;
      background-color: #f0f0f0;
      text-decoration: underline;
    }

    /* Copyright text styling */
    .copyright-text {
      color: #666;
      font-size: 0.7em; /* Reduced font size */
      font-weight: normal;
      white-space: nowrap;
    }

    #mainContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 1090px; /* Updated width to fit viewport */
      height: 100vh; /* Full viewport height */
      box-sizing: border-box;
      position: relative;
      background-color: #f0f0f0; /* Match body background */

      /* Additional zoom stability */
      flex-shrink: 0; /* Prevent shrinking */
      flex-grow: 0; /* Prevent growing */
      transform-origin: top left; /* Ensure consistent zoom origin */
    }

    /* Ensure all main layout elements maintain fixed dimensions during zoom */
    #mainContainer, #editorContainer, #brushOptionsBar, #canvasArea, #canvasTabsBar, #rightPanels, #zoomControls, #layersPanelContainer {
      flex-shrink: 0 !important;
      flex-grow: 0 !important;
    }

    #toolbar {
      margin: 0 auto; /* Remove top/bottom margins */
      padding: 8px;
      background-color: #e8e8e8; /* Match brush/layer options background */
      border: 1px solid #ccc; /* Match brush/layer options border */
      border-radius: 3px; /* Match brush/layer options border radius */
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      position: relative;
      box-sizing: border-box;
      width: 1090px !important; /* Override JavaScript sizing */
      height: 52px !important; /* Match main toolbar height exactly */
      flex-shrink: 0; /* Prevent shrinking */

    }

    /* Title image styling - match shared-layout.css behavior */
    .title-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .title-image {
      max-width: 180px;
      height: auto;
      max-height: 40px;
      object-fit: contain; /* Maintain aspect ratio and center */
      transition: filter 0.3s ease;
    }
    /* In your <style> tags or CSS file */
    .layer-item {
        /* ... existing styles ... */
        transition: opacity 0.1s ease-out; /* Smooth transition for dragging style */
    }

    .layer-item.dragging {
        opacity: 0.5; /* Example: Make the original item semi-transparent */
        /* You could also add a border or box-shadow */
    }

    #layersList.dragging-active .drop-indicator {
        display: block; /* Show indicators when a drag is active */
        height: 2px; /* Thin line */
        background-color: #007bff; /* A base color for the line */
        margin: 2px 0; /* Space around the line */
        opacity: 0.3; /* Faint line */
        transition: opacity 0.15s ease-in-out, height 0.15s ease-in-out, background-color 0.15s ease-in-out;
        border-radius: 1px;
    }

    #layersList.dragging-active .drop-indicator.highlighted {
        height: 4px; /* Make the glowing line thicker */
        background-color: #ff8c00; /* Bright "glowing" color */
        opacity: 0.9; /* Much more visible */
    }

    .drop-indicator { /* Hidden by default */
        display: none;
    }
    #toolbar > label, #toolbar > button, #toolbar > input, #toolbar > select, #toolbar > div {
        margin-right: 6px;
        margin-bottom: 4px;
        vertical-align: middle;
        font-size: 0.9em;
        box-sizing: border-box;
    }
    /* ... existing styles ... */


    /* ... rest of your styles ... */
    /* ... existing styles ... */
    #minimapCanvas {
        position: absolute;
        bottom: 10px;
        left: 10px;
        border: 1px solid #333;
        background-color: #ffffff; /* Opaque background for the minimap itself */
        display: none; /* Initially hidden */
        z-index: 20; /* Ensure it's on top of other canvases in canvasContainer */
    }
    /* Ensure canvasContainer can contain absolutely positioned children correctly */
    #canvasContainer {
        position: relative; /* This should already be set from your existing CSS */
        /* ... other canvasContainer styles ... */
    }
    #toolbar input[type="number"], #toolbar input[type="text"] {
        padding: 3px;
    }
    #toolbar select {
        padding: 3px;
    }
    #toolbar select#tool {
        width: 165px;
    }
    #toolbar button {
        padding: 4px 8px;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        font-size: 0.85em;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    #toolbar button:hover {
        background-color: #e0e0e0;
    }

    #toolbar button:active {
        background-color: #d0d0d0;
    }

    canvas {
      image-rendering: pixelated;
      image-rendering: -moz-crisp-edges;
      image-rendering: crisp-edges;
      display: block;
      touch-action: none;
      -webkit-touch-callout: none;
      -webkit-tap-highlight-color: transparent;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }

    #editorContainer {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        margin: 5px 0 0 0; /* Small top margin to separate from toolbar */
        width: 1090px; /* Updated to accommodate canvas (220 + 600 + 250 + 20 margins) */
        height: 634px; /* Fixed height to match all panels */
        padding: 0; /* Remove padding to maximize space */
        box-sizing: border-box;
        justify-content: center; /* Center the content horizontally */
        position: relative;
    }

    #brushOptionsBar {
        display: flex;
        flex-direction: column;
        padding: 10px;
        background-color: #e8e8e8;
        border: 1px solid #ccc;
        border-radius: 3px;
        margin-right: 10px;
        box-sizing: border-box;
        width: 220px; /* Fixed width */
        height: 634px !important; /* Match canvas + tabs height (600px + 34px) */
        overflow-y: auto;
        flex-shrink: 0; /* Prevent shrinking */
    }


    #brushOptionsBar label:not(.opacity-control-set label) { /* Avoid double styling for opacity labels */
        display: block;
        margin-bottom: 5px;
        font-size: 0.85em;
    }
    #brushOptionsBar input[type="radio"] {
        margin-right: 4px;
        vertical-align: middle;
    }

    #commonBrushOptionsContainer,
    #hatchingBrushOptionsContainer,
    #selectOptionsContainer,
    #panOptionsContainer,
    #eyedropperOptionsContainer,
    #eraseOptionsContainer,
    #sprayBrushOptionsContainer,
    #glowBrushOptionsContainer { /* ADDED #glowBrushOptionsContainer HERE */
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        width: 100%;
        margin-bottom: 15px;
    }

    #commonBrushOptionsContainer h4,
    #hatchingBrushOptionsContainer h4,
    #sprayBrushOptionsContainer h4,
    #selectOptionsContainer h4,
    #panOptionsContainer h4,
    #eyedropperOptionsContainer h4,
    #eraseOptionsContainer h4,
    #fillOptionsContainer h4 {
        margin-top: 0;
        margin-bottom: 8px;
        font-size: 0.9em;
        text-align: center;
    }

    #hatchScaleContainer,
    #sprayRadiusContainer {
        margin-top: 8px;
        font-size: 0.8em;
        padding: 5px;
        border: 1px solid #ccc;
        border-radius: 3px;
        background-color: #f8f8f8;
        box-sizing: border-box; /* Added */
    }

    #hatchScaleContainer label,
    #sprayRadiusContainer label {
        display: block;
        margin-bottom: 3px;
    }

    #hatchScaleContainer input[type="range"],
    #sprayRadiusContainer input[type="range"] {
        width: 100%;
        box-sizing: border-box;
    }

    #fillOptionsContainer {
        display: none;
        flex-direction: column;
        box-sizing: border-box;
        width: 100%;
        /* margin-bottom: 15px; */ /* Handled by opacity-control-set or other children */
    }

    /* Opacity Control Set Styles (Updated for two-row layout) */
    .opacity-control-set {
        display: flex;
        flex-direction: column; /* Stack rows vertically */
        align-items: stretch;   /* Make children take full width */
        width: 100%;
        margin-bottom: 10px;
        padding: 5px;
        border: 1px solid #ccc;
        border-radius: 3px;
        background-color: #f8f8f8;
        box-sizing: border-box;
    }
    .opacity-input-line { /* For Row 1: Label + Number Input + % */
        display: flex;
        align-items: center;
        margin-bottom: 5px; /* Space between input line and slider */
        width: 100%;
    }
    .opacity-control-set label { /* Label styling */
        font-size: 0.85em;
        margin-right: auto; /* Pushes input+percent to the right */
        white-space: nowrap;
        flex-shrink: 0; /* Prevent label from shrinking */
    }
    .opacity-control-set .opacity-input-line input[type="number"] { /* Number input */
        width: 45px;
        padding: 2px 3px;
        text-align: right;
        font-size: 0.9em;
    }
    .opacity-control-set .opacity-input-line span { /* '%' symbol styling */
        margin-left: 2px; /* Small space after number input */
        margin-right: 0px; /* No space needed before slider which is on next line */
        font-size: 0.85em;
    }
    .opacity-control-set input[type="range"] { /* Range slider (Row 2) */
        width: 100%; /* Make slider take full width of its parent */
        box-sizing: border-box;
        min-width: 40px; /* Ensure it doesn't collapse too much */
    }
    /* Specific for layer opacity text input if needed */
    .layer-opacity-control .opacity-control-set input[type="number"] {
         font-size: 0.95em;
    }


    #canvasArea {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 600px; /* Reduced width to fit viewport */
        height: 600px; /* Fixed height to match canvas */
        margin-right: 10px;
        flex-shrink: 0; /* Prevent shrinking */
        box-sizing: border-box;
    }

    #canvasTabsBar {
        display: flex;
        align-items: stretch;
        background-color: #dadada;
        margin-bottom: 0px;
        overflow-x: auto;
        flex-wrap: nowrap;
        width: 600px; /* Fixed width to match canvas exactly */
        height: 34px; /* Fixed height instead of min-height */
        box-sizing: border-box;
        border: 1px solid #999;
        border-bottom: none;
        border-radius: 4px 4px 0 0;
        flex-shrink: 0; /* Prevent shrinking */
    }

    .canvas-tab {
        display: flex;
        align-items: center;
        padding: 6px 10px;
        background-color: #e9e9e9;
        border-right: 1px solid #bbb;
        cursor: pointer;
        font-size: 0.85em;
        white-space: nowrap;
        position: relative;
        flex-shrink: 0;
    }


    .canvas-tab.active-canvas-tab {
        background-color: #FFFFFF;
        border-bottom: 1px solid #FFFFFF;
        margin-bottom: -1px;
        z-index: 2;
    }
     .canvas-tab:not(.active-canvas-tab):hover {
        background-color: #f5f5f5;
    }

    .canvas-tab-name {
        flex-grow: 1;
        margin-right: 8px;
        max-width: 100px;
        min-width: 50px;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5;
    }
    .canvas-tab-name input {
        width: 100%;
        box-sizing: border-box;
        font: inherit;
        padding: 1px 2px;
        border: 1px solid #777;
        background-color: white;
        color: black;
        border-radius: 2px;
        height: 1.5em;
        line-height: normal;
    }

    .canvas-tab-controls {
        display: flex;
        align-items: center;
        margin-left: auto;
    }

    .canvas-tab-controls button {
        font-size: 0.9em;
        padding: 4px 8px;
        margin-left: 3px;
        line-height: 1;
        min-width: 24px;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    .canvas-tab-controls button:hover {
        background-color: #e0e0e0;
    }
    .canvas-tab-controls button:active {
        background-color: #d0d0d0;
    }
    .canvas-tab.active-canvas-tab .canvas-tab-controls button {
      background-color: #f0f0f0;
    }
    .canvas-tab.active-canvas-tab .canvas-tab-controls button:hover {
      background-color: #e0e0e0;
    }

    #addNewCanvasBtn {
        padding: 6px 12px;
        font-size: 0.9em;
        font-weight: bold;
        margin-left: auto;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        cursor: pointer;
        align-self: stretch;
        flex-shrink: 0;
        transition: background-color 0.2s ease;
    }
    #addNewCanvasBtn:hover {
        background-color: #e0e0e0;
    }
    #addNewCanvasBtn:active {
        background-color: #d0d0d0;
    }
    #addNewCanvasBtn:disabled {
        background-color: #e0e0e0;
        border-color: #ccc;
        color: #999;
        cursor: not-allowed;
    }

    #canvasContainer {
        position: relative;
        border: 1px solid #999;
        line-height: 0;
        background-color: #FFFFFF;
        box-sizing: border-box;
        z-index: 1;
        touch-action: none;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;

        /* Fixed dimensions that scale with browser zoom */
        width: 600px !important; /* Override JavaScript sizing - match canvasArea */
        height: 600px !important; /* Fixed height to match width for square canvas */
        flex-shrink: 0; /* Prevent shrinking from flex */
        overflow: hidden; /* Prevent canvas from overflowing */
        display: block; /* Use block instead of flex for simpler layout */
        position: relative; /* Ensure proper positioning context */
    }

    #canvasContainer.checkerboard {
      background-color: transparent;
      background-image:
        linear-gradient(45deg, #ccc 25%, transparent 25%),
        linear-gradient(-45deg, #ccc 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #ccc 75%),
        linear-gradient(-45deg, transparent 75%, #ccc 75%);
      background-size: 20px 20px;
      background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    }

    #pixelCanvas {
      background-color: transparent;
      width: 100% !important; /* Fill container width */
      height: 100% !important; /* Fill container height */
      display: block !important; /* Ensure block display */
      image-rendering: pixelated !important; /* Maintain pixel art rendering */
      image-rendering: -moz-crisp-edges !important;
      image-rendering: crisp-edges !important;
      object-fit: contain !important; /* Maintain aspect ratio while filling */
      object-position: center !important; /* Center the canvas */
    }

    #gridCanvas {
      position: absolute;
      top: 0 !important; /* Align to container top */
      left: 0 !important; /* Align to container left */
      pointer-events: none;
      background-color: transparent !important;
      width: 100% !important; /* Fill container width */
      height: 100% !important; /* Fill container height */
      display: block !important; /* Ensure block display */
      image-rendering: pixelated !important; /* Maintain pixel art rendering */
      image-rendering: -moz-crisp-edges !important;
      image-rendering: crisp-edges !important;
      object-fit: contain !important; /* Maintain aspect ratio while filling */
      object-position: center !important; /* Center the canvas */
    }

    #rightPanels {
        display: flex;
        flex-direction: row;
        height: 634px; /* Match canvas + tabs height (600px + 34px) */
        width: 250px; /* Reduced width (zoom controls + layers panel) */
        flex-shrink: 0; /* Prevent shrinking */
        box-sizing: border-box;
    }

    #zoomControls {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 5px; /* Reduced margin */
        height: 634px; /* Match canvas + tabs height */
        width: 30px; /* Reduced width */
        flex-shrink: 0; /* Prevent shrinking */
    }

    #zoomSlider {
        writing-mode: vertical-lr;
        width: 20px;
        padding: 0 5px;
        transform: rotate(180deg);
    }
    #zoomSliderContainer {
        display:flex;
        align-items: center;
        height: 634px; /* Match canvas + tabs height */
        flex-shrink: 0; /* Prevent shrinking */
    }

    #hexInput {
        text-transform: uppercase;
    }
    #color-picker {
        height: 26px;
        width: 35px;
        padding: 0;
        border: 1px solid #777;
        vertical-align: middle;
    }

    #layersPanelContainer {
        width: 215px; /* Reduced width to fit in smaller rightPanels */
        height: 634px; /* Match canvas + tabs height */
        background-color: #e8e8e8;
        border: 1px solid #ccc;
        border-radius: 3px;
        padding: 8px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        flex-shrink: 0; /* Prevent shrinking */
    }
    #layersPanelContainer h3 {
        margin-top: 0;
        margin-bottom: 8px;
        font-size: 0.95em;
        text-align: center;
    }
    #layersList {
        list-style-type: none;
        padding: 0;
        margin: 0;
        flex-grow: 1;
        overflow-y: auto;
    }
    .layer-item {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        padding: 4px;
        margin-bottom: 4px;
        background-color: #fdfdfd;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 0.85em;
        cursor: pointer;
    }
    .layer-details-row {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 3px;
    }
    .layer-item.active-layer {
        background-color: #cce5ff;
        border-color: #007bff;
    }
    .layer-name {
        flex-grow: 1;
        margin-left: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 1.2em;
    }
    .layer-name input {
        width: 100%;
        box-sizing: border-box;
        font: inherit;
        padding: 1px 2px;
        border: 1px solid #777;
        background-color: white;
        color: black;
        border-radius: 2px;
    }
    .layer-controls {
        display: flex;
        align-items: center;
    }
    .layer-controls button {
        font-size: 0.9em;
        padding: 2px 4px;
        margin-left: 2px;
        line-height: 1;
        min-width: 18px;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .layer-controls button:hover {
        background-color: #e0e0e0;
    }

    .layer-controls button:active {
        background-color: #d0d0d0;
    }
    .layer-visibility {
        min-width: 18px;
        height: 18px;
        border: 1px solid #999;
        background-color: #fff;
        text-align: center;
        line-height: 16px;
        cursor: pointer;
        font-size: 10px;
        margin-right: 4px;
    }
     .layer-visibility.visible::before { content: '👁️'; } /* Eye emoji */
    .layer-visibility.hidden::before { content: ' '; }

    .layer-opacity-control { /* Wrapper for label, input, slider for layer opacity */
        margin-top: 4px;
    }


    #layerActions button {
        width: 100%;
        margin-bottom: 3px;
        padding: 4px 8px;
        font-size: 0.8em;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    #layerActions button:hover {
        background-color: #e0e0e0;
    }

    #layerActions button:active {
        background-color: #d0d0d0;
    }
    /* --- Modal Styles --- */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000; /* Ensure it's on top */
        padding: 20px;
        box-sizing: border-box;
    }

    .modal-content {
        background-color: #fff;
        padding: 20px 25px;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        width: auto;
        /* min-width: 320px; /* Original: Example adjustment */
        /* max-width: 500px; /* Original: Example adjustment */
        min-width: 450px; /* ADJUSTED - ensure it's wide enough for previews + some options */
        max-width: 750px; /* ADJUSTED - Allow more width for options */
        position: relative;
        text-align: center;
    }

    .modal-close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        font-size: 16px;
        line-height: 1;
        cursor: pointer;
        padding: 4px 8px;
        color: #333;
        transition: background-color 0.2s ease;
    }
    .modal-close-btn:hover {
        background-color: #e0e0e0;
        color: #000;
    }
    .modal-close-btn:active {
        background-color: #d0d0d0;
    }

    .modal-content h3 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 1.2em;
        color: #333;
    }

    .modal-options {
        display: flex;
        /* flex-direction: column; */ /* Original: Stack options vertically */
        flex-direction: row;     /* CHANGED: Arrange options in a row */
        flex-wrap: wrap;         /* ADDED: Allow options to wrap to the next line */
        justify-content: center; /* ADDED: Center options in the row(s) */
        align-items: center;     /* Original: align-items: flex-start; - CHANGED for better centering in a row */
        margin-bottom: 25px;
        /* max-height: 200px; */ /* Original: If many options, make it scrollable - REMOVED */
        /* overflow-y: auto; */   /* Original - REMOVED */
        max-height: none;        /* ADDED: Allow content to determine height */
        overflow-y: visible;     /* ADDED: Ensure no scrollbar for options area */
    }

    .size-option {
        display: inline-flex; /* CHANGED: allow items in a row, align items inside label */
        align-items: center;  /* ADDED: vertically align radio and text within the label */
        /* margin: 8px 0; */    /* Original */
        margin: 5px;          /* CHANGED: Uniform margin for wrapped items */
        /* padding: 5px; */      /* Original */
        padding: 6px 10px;    /* CHANGED: Adjust padding for a slightly more compact look */
        cursor: pointer;
        /* width: 100%; */      /* Original */
        width: auto;          /* CHANGED: Allow option to size based on content */
        border-radius: 4px;
        border: 1px solid #eee; /* Optional: add a light border to each option */
        transition: background-color 0.2s;
    }
    /* Ensure hover and checked styles still look good */
    .size-option:hover, .size-option input[type="radio"]:focus + span {
        background-color: #f0f0f0;
        border-color: #ddd; /* Optional: slightly change border on hover */
    }
    .size-option input[type="radio"]:checked + span {
        font-weight: bold;
        color: #007bff;
    }

    .size-option {
        display: block;
        margin: 8px 0;
        padding: 5px;
        cursor: pointer;
        width: 100%;
        border-radius: 4px;
        transition: background-color 0.2s;
    }

    .size-option input[type="radio"] {
        margin-right: 10px;
        vertical-align: middle;
    }

    .size-option:hover, .size-option input[type="radio"]:focus + span {
        background-color: #f0f0f0;
    }
    .size-option input[type="radio"]:checked + span {
        font-weight: bold;
        color: #007bff;
    }


    .modal-actions {
        display: flex;
        justify-content: flex-end; /* Align buttons to the right */
        gap: 10px; /* Space between buttons */
        margin-top: 10px;
    }

    .modal-actions button {
        padding: 6px 12px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 0.9em;
        border: 1px solid #999;
        background-color: #f5f5f5;
        color: #333;
        transition: background-color 0.2s ease;
    }

    .modal-actions button:hover {
        background-color: #e0e0e0;
    }

    .modal-actions button:active {
        background-color: #d0d0d0;
    }

    .modal-actions #confirmSizeChangeBtn {
        background-color: #2c5aa0;
        color: white;
        border-color: #2c5aa0;
    }
    .modal-actions #confirmSizeChangeBtn:hover {
        background-color: #1e3a70;
    }
    .modal-actions #confirmSizeChangeBtn:active {
        background-color: #162d5a;
    }
    /* --- Modal Styles (Additions/Modifications) --- */
    .modal-content {
        /* May need to adjust min-width if content gets too wide */
        min-width: 320px; /* Example adjustment */
        max-width: 500px; /* Example adjustment */
    }

    .modal-preview-area {
        display: flex;
        justify-content: space-around;
        margin-bottom: 20px;
        gap: 15px; /* Space between preview boxes */
    }

    .preview-container {
        flex: 1;
        text-align: center;
        border: 1px solid #ddd;
        padding: 8px;
        border-radius: 4px;
        background-color: #f9f9f9;
        display: flex; /* Added for alignment */
        flex-direction: column; /* Added for alignment */
        align-items: center; /* Added for alignment */
    }

    .preview-container h4 {
        margin-top: 0;
        margin-bottom: 8px;
        font-size: 0.9em;
        color: #555;
    }

    #currentCanvasPreview,
    #newCanvasPreview {
        width: 120px; /* Fixed display width */
        height: 120px; /* Fixed display height */
        border: 1px solid #ccc;
        background-color: #fff; /* Fallback background */
        /* For checkerboard on newCanvasPreview, it will be drawn via JS */
        image-rendering: pixelated;
        image-rendering: -moz-crisp-edges;
        image-rendering: crisp-edges;
    }

    /* .modal-options { */
        /* Adjust max-height if previews take up too much space */
        /* max-height: 150px; */
    /* } */

    /* Mobile Undo/Redo Buttons */
    .mobile-only {
        display: none; /* Hidden by default on desktop */
    }

    #mobileUndoRedoContainer {
        display: flex;
        align-items: center;
        margin-left: 8px;
        gap: 4px;
    }

    #mobileUndoBtn, #mobileRedoBtn {
        padding: 6px 8px;
        font-size: 1.2em;
        font-weight: bold;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        cursor: pointer;
        min-width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;
    }

    #mobileUndoBtn:hover:not(:disabled), #mobileRedoBtn:hover:not(:disabled) {
        background-color: #e0e0e0;
    }

    #mobileUndoBtn:active:not(:disabled), #mobileRedoBtn:active:not(:disabled) {
        background-color: #d0d0d0;
        transform: translateY(1px);
    }

    #mobileUndoBtn:disabled, #mobileRedoBtn:disabled {
        background-color: #f8f8f8;
        color: #ccc;
        cursor: not-allowed;
        border-color: #ddd;
    }

    /* Selection Action Buttons */
    .selection-actions {
        display: flex;
        flex-direction: row;
        gap: 4px;
        justify-content: space-between;
    }

    .selection-action-btn {
        padding: 6px 8px;
        font-size: 0.75em;
        font-weight: 500;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 28px;
        flex: 1;
        white-space: nowrap;
    }

    .selection-action-btn:hover:not(:disabled) {
        background-color: #e0e0e0;
    }

    .selection-action-btn:active:not(:disabled) {
        background-color: #d0d0d0;
        transform: translateY(1px);
    }

    .selection-action-btn:disabled {
        background-color: #f8f8f8;
        color: #ccc;
        cursor: not-allowed;
        border-color: #ddd;
    }

    /* Selection Option Buttons (flip, scale, rotate) */
    .selection-option-btn {
        padding: 6px 8px;
        font-size: 0.85em;
        font-weight: 500;
        background-color: #f5f5f5;
        border: 1px solid #999;
        border-radius: 3px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        width: 100%;
    }

    .selection-option-btn:hover {
        background-color: #e0e0e0;
    }

    .selection-option-btn:active {
        background-color: #d0d0d0;
    }

    /* Show mobile buttons only on touch devices or small screens */
    @media (max-width: 768px), (pointer: coarse) {
        .mobile-only {
            display: flex !important;
        }
    }

    /* Additional mobile-specific adjustments */
    @media (max-width: 768px) {
        #toolbar {
            flex-wrap: wrap;
            justify-content: center;
            padding: 6px;
        }

        #toolbar > label, #toolbar > button, #toolbar > input, #toolbar > select, #toolbar > div {
            margin-right: 4px;
            margin-bottom: 3px;
            font-size: 0.85em;
        }

        #mobileUndoRedoContainer {
            order: 1; /* Place undo/redo buttons early in the toolbar */
            margin-left: 4px;
        }
    }

    /* Cloud Save Modal Styling */
    .cloud-save-modal {
      max-width: 600px;
      max-height: 85vh;
      overflow-y: auto;
    }

    .save-preview-section {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ddd;
    }

    .save-preview-section h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 1.1em;
    }

    .save-preview-container {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .save-preview-canvas {
      border: 2px solid #ccc;
      border-radius: 4px;
      background-color: white;
      max-width: 150px;
      max-height: 150px;
    }

    .save-preview-info {
      flex: 1;
    }

    .save-preview-info p {
      margin: 4px 0;
      font-size: 0.9em;
      color: #666;
    }

    .save-name-section {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ddd;
    }

    .save-name-section label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
    }

    .save-name-input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 0.9em;
      box-sizing: border-box;
    }

    .save-name-hint {
      display: block;
      margin-top: 4px;
      font-size: 0.8em;
      color: #666;
      font-style: italic;
    }

    .save-options-section {
      margin-bottom: 20px;
    }

    .save-options-section h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 1.1em;
    }

    .save-option-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .save-option-btn {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      border: 2px solid #ccc;
      border-radius: 6px;
      background-color: white;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;
    }

    .save-option-btn.primary {
      border-color: #2c5aa0;
      background-color: #f8fbff;
    }

    .save-option-btn.primary:hover {
      background-color: #e8f0ff;
      border-color: #1e3f73;
    }

    .save-option-btn.secondary {
      border-color: #666;
      background-color: #f8f8f8;
    }

    .save-option-btn.secondary:hover {
      background-color: #e8e8e8;
      border-color: #444;
    }

    .save-btn-icon {
      font-size: 1.5em;
      flex-shrink: 0;
    }

    .save-btn-text {
      font-weight: 500;
      color: #333;
      flex: 1;
    }

    .save-option-btn small {
      display: block;
      font-size: 0.8em;
      color: #666;
      margin-top: 2px;
    }

    .cloud-storage-status {
      padding: 12px;
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      border-radius: 4px;
      text-align: center;
    }

    .storage-info {
      display: flex;
      justify-content: space-around;
      gap: 16px;
    }

    .storage-info span {
      font-size: 0.9em;
      color: #666;
      font-weight: 500;
    }

    /* Dark theme for cloud save modal */
    .dark-theme .cloud-save-modal {
      background-color: #2a2a2a;
      color: #e0e0e0;
    }

    .dark-theme .save-preview-section,
    .dark-theme .save-name-section {
      border-bottom-color: #444;
    }

    .dark-theme .save-preview-section h3,
    .dark-theme .save-options-section h3,
    .dark-theme .save-name-section label {
      color: #e0e0e0;
    }

    .dark-theme .save-preview-canvas {
      border-color: #555;
      background-color: #333;
    }

    .dark-theme .save-preview-info p {
      color: #bbb;
    }

    .dark-theme .save-name-input {
      background-color: #333;
      border-color: #555;
      color: #e0e0e0;
    }

    .dark-theme .save-option-btn {
      background-color: #333;
      border-color: #555;
    }

    .dark-theme .save-option-btn.primary {
      border-color: #4a7bc8;
      background-color: #1a2332;
    }

    .dark-theme .save-option-btn.primary:hover {
      background-color: #243040;
      border-color: #5a8bd8;
    }

    .dark-theme .save-option-btn.secondary {
      border-color: #666;
      background-color: #2a2a2a;
    }

    .dark-theme .save-option-btn.secondary:hover {
      background-color: #333;
      border-color: #777;
    }

    .dark-theme .save-btn-text {
      color: #e0e0e0;
    }

    .dark-theme .save-option-btn small {
      color: #bbb;
    }

    .dark-theme .cloud-storage-status {
      background-color: #333;
      border-color: #555;
    }

    .dark-theme .storage-info span {
      color: #bbb;
    }