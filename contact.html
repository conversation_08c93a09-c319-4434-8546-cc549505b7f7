<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3586346747453362"
       crossorigin="anonymous"></script>
  <style>
    .contact-info {
      background-color: #f9f9f9;
      padding: 30px;
      border-radius: 8px;
      border-left: 4px solid #2c5aa0;
      text-align: center;
      margin: 30px 0;
    }

    .email {
      font-size: 1.3em;
      font-weight: bold;
      color: #2c5aa0;
      margin: 20px 0;
      padding: 15px;
      background-color: white;
      border-radius: 5px;
      border: 2px solid #2c5aa0;
    }

    .email a {
      color: #2c5aa0;
      text-decoration: none;
    }

    .email a:hover {
      text-decoration: underline;
    }

    .disclaimer {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 5px;
      padding: 15px;
      margin: 20px 0;
    }

    .disclaimer h3 {
      margin-top: 0;
      color: #856404;
    }

    .contact-reasons {
      list-style-type: none;
      padding: 0;
    }

    .contact-reasons li {
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
    }

    .contact-reasons li:before {
      content: "📧 ";
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="title.webp" alt="PixelArtNexus" class="logo">
        <h1>Contact Us</h1>
      </div>
    
    <p>We'd love to hear from you! Whether you have questions, suggestions, bug reports, or just want to share your pixel art creations, we're here to help.</p>
    
    <div class="contact-info">
      <h2>📧 Get in Touch</h2>
      <p>For all inquiries, please reach out to us at:</p>
      <div class="email">
        <a href="mailto:<EMAIL>"><EMAIL></a>
      </div>
    </div>
    
    <h2>What can we help you with?</h2>
    <ul class="contact-reasons">
      <li><strong>Technical Support:</strong> Having trouble with the editor? Let us know!</li>
      <li><strong>Feature Requests:</strong> Have an idea for a new tool or feature?</li>
      <li><strong>Bug Reports:</strong> Found something that's not working correctly?</li>
      <li><strong>General Feedback:</strong> Share your thoughts on how we can improve</li>
      <li><strong>Collaboration:</strong> Interested in partnering or contributing?</li>
      <li><strong>Media Inquiries:</strong> Press or interview requests</li>
    </ul>
    
    <div class="disclaimer">
      <h3>⏰ Response Time Disclaimer</h3>
      <p><strong>Please note:</strong> While we strive to respond to all emails as quickly as possible, response times may vary depending on the volume of inquiries and the complexity of your request. We appreciate your patience and will get back to you as soon as we can.</p>
      <p>For urgent technical issues, please include as much detail as possible about the problem, including:</p>
      <ul>
        <li>Your browser and version</li>
        <li>Steps to reproduce the issue</li>
        <li>Any error messages you see</li>
        <li>Screenshots if applicable</li>
      </ul>
    </div>
    
    <h2>Alternative Ways to Connect</h2>
    <p>You can also provide feedback through our <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" style="color: #2c5aa0;">feedback form</a> for quick suggestions or bug reports.</p>

    <a href="/" class="back-link">← Back to Home</a>

    <!-- Footer -->
    <div class="bottom-toolbar">
      <div class="footer-section core-info">
        <a href="/about" class="footer-link">About</a>
        <a href="/contact" class="footer-link">Contact</a>
        <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
        <a href="/help" class="footer-link">Help / FAQ</a>
      </div>
      <div class="footer-section legal-policy">
        <a href="/terms" class="footer-link">Terms of Service</a>
        <a href="/privacy" class="footer-link">Privacy Policy</a>
      </div>
      <div class="footer-section community-social">
        <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
        <a href="/social" class="footer-link">Social Media</a>
        <a href="/blog" class="footer-link">Blog / Updates</a>
      </div>
      <div class="footer-section copyright">
        <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
      </div>
    </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Login button (shown when not authenticated) -->
        <button id="authButton" class="auth-button">Login</button>

        <!-- Settings button (shown when not authenticated) -->
        <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

        <!-- Profile dropdown (shown when authenticated and verified) -->
        <div id="profileDropdown" class="profile-dropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div class="setting-item">
          <div>
            <label for="themeSelect">Theme:</label>
            <select id="themeSelect">
              <option value="light">Light Mode</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>
      </div>

      <!-- User Profile Settings (shown only for authenticated users) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div class="setting-item">
          <div>
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter username" maxlength="50">
          </div>
        </div>
        <div class="setting-item">
          <div>
            <label for="profileImageInput">Profile Picture:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
            <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
          </div>
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <script src="shared-layout.js"></script>
</body>
</html>
