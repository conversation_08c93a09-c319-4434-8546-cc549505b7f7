/* Main Landing Page CSS */

/* Import shared layout styles */
@import url('shared-layout.css');

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  color: #333;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Three-column layout */
#fullWidthWrapper {
  display: flex;
  flex-direction: row;
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
}

.banner-div {
  flex: 1;
  background-color: transparent;
  border: none;
  min-width: 200px;
  box-sizing: border-box;
}

#leftBanner {
  border-left: none;
  display: flex;
  flex-direction: column;
  margin-right: 5px;
  position: relative;
}

#rightBanner {
  border-left: none;
  display: flex;
  flex-direction: column;
  margin-left: 5px;
  position: relative;
}

/* Main content area */
#mainContainer {
  flex: 0 0 auto;
  width: 900px;
  max-width: 900px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-radius: 8px;
  margin: 40px 0;
  padding: 40px;
  position: relative;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Banner ad container positioning for index page */
.banner-ad-container {
  position: absolute;
  top: 57px; /* Position below toolbar */
  left: 5px;
  right: 5px;
  bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; /* Fully transparent */
  overflow: hidden;
  border-radius: 3px;
  z-index: 10; /* Above banner image */
  pointer-events: none; /* Allow clicks to pass through to banner image */
}

.banner-ad-container .adsbygoogle {
  width: 100%;
  height: 100%;
  border-radius: 3px;
  pointer-events: auto; /* Re-enable clicks for the ad itself */
}

/* Welcome Header */
.welcome-header {
  text-align: center;
  margin-bottom: 50px;
  padding-bottom: 30px;
  border-bottom: 2px solid #e0e0e0;
}

.main-logo {
  max-width: 400px;
  height: auto;
  margin-bottom: 20px;
  transition: filter 0.3s ease;
}

.welcome-header h1 {
  color: #2c5aa0;
  margin: 0 0 30px 0;
  font-size: 2.8em;
  font-weight: bold;
}

.quote-section {
  max-width: 600px;
  margin: 0 auto;
}

.quote-section blockquote {
  font-size: 1.4em;
  font-style: italic;
  color: #555;
  margin: 0 0 20px 0;
  padding: 0;
  border: none;
  position: relative;
}

.quote-section blockquote:before {
  content: '"';
  font-size: 2em;
  color: #2c5aa0;
  position: absolute;
  left: -20px;
  top: -10px;
}

.quote-section blockquote:after {
  content: '"';
  font-size: 2em;
  color: #2c5aa0;
  position: absolute;
  right: -20px;
  bottom: -20px;
}

.mission-statement {
  font-size: 1.1em;
  color: #666;
  line-height: 1.7;
  margin: 0;
}

/* Navigation Buttons */
.main-navigation {
  margin: 50px 0;
}

.nav-button-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.nav-button {
  display: flex;
  align-items: center;
  padding: 25px 30px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  background-color: #fff;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #2c5aa0;
}

.nav-button.primary {
  border-color: #2c5aa0;
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3a70 100%);
  color: white;
}

.nav-button.primary:hover {
  background: linear-gradient(135deg, #1e3a70 0%, #2c5aa0 100%);
  border-color: #1e3a70;
}

.nav-button.secondary {
  background-color: #f8f9fa;
}

.nav-button.secondary:hover {
  background-color: #e9ecef;
}

.nav-button-icon {
  font-size: 3em;
  margin-right: 25px;
  flex-shrink: 0;
}

.nav-button-content {
  flex: 1;
  text-align: left;
}

.nav-button-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.5em;
  font-weight: bold;
}

.nav-button-content p {
  margin: 0;
  font-size: 1em;
  opacity: 0.8;
}

.coming-soon-badge {
  display: inline-block;
  background-color: #ff6b35;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: bold;
  margin-top: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Features Preview */
.features-preview {
  margin: 60px 0 40px 0;
  text-align: center;
}

.features-preview h2 {
  color: #2c5aa0;
  font-size: 2.2em;
  margin-bottom: 40px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 15px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
  max-width: 600px;
  margin: 0 auto;
}

.feature-card {
  background-color: #f9f9f9;
  padding: 25px 20px;
  border-radius: 10px;
  border-left: 4px solid #2c5aa0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.feature-icon {
  font-size: 2.5em;
  margin-bottom: 15px;
}

.feature-card h4 {
  margin: 0 0 10px 0;
  color: #2c5aa0;
  font-size: 1.2em;
  font-weight: bold;
}

.feature-card p {
  margin: 0;
  color: #666;
  font-size: 0.95em;
  line-height: 1.5;
}

/* Footer styling (reuse from index.css) */
.bottom-toolbar {
  margin: 40px auto 0 auto;
  padding: 8px 16px;
  background-color: #e8e8e8;
  border: 1px solid #ccc;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 0.8em;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.footer-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-section.core-info {
  flex: 2;
}

.footer-section.legal-policy {
  flex: 1;
}

.footer-section.community-social {
  flex: 2;
}

.footer-section.copyright {
  flex: 1;
  justify-content: flex-end;
}

.footer-link {
  color: #555;
  text-decoration: none;
  font-size: 0.75em;
  padding: 2px 6px;
  border-radius: 2px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.footer-link:hover {
  color: #2c5aa0;
  background-color: #f0f0f0;
  text-decoration: underline;
}

.copyright-text {
  font-size: 0.75em;
  color: #777;
  white-space: nowrap;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  max-width: 500px;
  width: 90%;
  position: relative;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-close-btn {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s ease;
}

.modal-close-btn:hover {
  color: #333;
}

/* Settings Modal Styles */
.settings-section {
  margin: 20px 0;
}

.settings-section h3 {
  margin: 0 0 15px 0;
  color: #2c5aa0;
  font-size: 1.1em;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

.setting-item label {
  font-weight: bold;
  margin-right: 10px;
}

.setting-item select,
.setting-item input[type="text"] {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  min-width: 150px;
}

.setting-item input[type="file"] {
  padding: 3px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  width: 100%;
}

.setting-item > div {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.modal-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.modal-button.primary {
  background-color: #2c5aa0;
  color: white;
}

.modal-button.primary:hover {
  background-color: #1e3f73;
}

.modal-button.secondary {
  background-color: #f5f5f5;
  color: #333;
}

.modal-button.secondary:hover {
  background-color: #e0e0e0;
}

.coming-soon-modal {
  text-align: center;
}

.coming-soon-icon {
  font-size: 4em;
  margin-bottom: 20px;
}

.coming-soon-content h2 {
  color: #2c5aa0;
  margin-bottom: 15px;
}

.coming-soon-content p {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.6;
}

.coming-soon-details {
  font-style: italic;
  margin-bottom: 25px !important;
}

.coming-soon-actions {
  margin-top: 25px;
}

.feedback-btn {
  display: inline-block;
  background-color: #2c5aa0;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  transition: background-color 0.3s ease;
  font-weight: bold;
}

.feedback-btn:hover {
  background-color: #1e3a70;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .banner-div {
    min-width: 150px;
  }

  #mainContainer {
    width: 800px;
    max-width: 800px;
  }
}

@media (max-width: 1200px) {
  .banner-div {
    min-width: 100px;
  }

  #mainContainer {
    width: 700px;
    max-width: 700px;
    padding: 30px;
  }

  .welcome-header h1 {
    font-size: 2.4em;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 1000px) {
  #fullWidthWrapper {
    flex-direction: column;
  }

  .banner-div {
    min-width: auto;
    height: auto;
  }

  #mainContainer {
    width: 100%;
    max-width: 100%;
    margin: 20px;
    border-radius: 0;
    padding: 25px;
  }

  .banner-image-container,
  .banner-ad-container {
    display: none;
  }

  .welcome-header h1 {
    font-size: 2em;
  }

  .nav-button {
    padding: 20px 25px;
  }

  .nav-button-icon {
    font-size: 2.5em;
    margin-right: 20px;
  }

  .nav-button-content h3 {
    font-size: 1.3em;
  }
}

@media (max-width: 600px) {
  #mainContainer {
    padding: 20px;
    margin: 10px;
  }

  .welcome-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
  }

  .welcome-header h1 {
    font-size: 1.8em;
  }

  .quote-section blockquote {
    font-size: 1.2em;
  }

  .nav-button {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }

  .nav-button-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .nav-button-content {
    text-align: center;
  }

  .features-preview h2 {
    font-size: 1.8em;
  }

  .footer-section {
    flex-direction: column;
    gap: 4px;
  }

  .bottom-toolbar {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Dark Theme Styles */
body.dark-theme {
  background-color: #1a1a1a;
  color: #e0e0e0;
}

body.dark-theme .banner-div {
  background-color: #1a1a1a;
}

body.dark-theme #mainContainer {
  background-color: #2d2d2d;
  box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  color: #e0e0e0;
}

body.dark-theme .title-image,
body.dark-theme .banner-image,
body.dark-theme .main-logo {
  filter: invert(1) hue-rotate(180deg);
}

body.dark-theme .welcome-header {
  border-bottom-color: #555;
}

body.dark-theme .welcome-header h1 {
  color: #6db3f2;
}

body.dark-theme .quote-section blockquote {
  color: #b0b0b0;
}

body.dark-theme .quote-section blockquote:before,
body.dark-theme .quote-section blockquote:after {
  color: #6db3f2;
}

body.dark-theme .mission-statement {
  color: #a0a0a0;
}

body.dark-theme .nav-button {
  background-color: #3d3d3d;
  border-color: #555;
  color: #e0e0e0;
}

body.dark-theme .nav-button:hover {
  border-color: #6db3f2;
  box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

body.dark-theme .nav-button.primary {
  background: linear-gradient(135deg, #6db3f2 0%, #5a9fd9 100%);
  border-color: #6db3f2;
  color: #1a1a1a;
}

body.dark-theme .nav-button.primary:hover {
  background: linear-gradient(135deg, #5a9fd9 0%, #6db3f2 100%);
  border-color: #5a9fd9;
}

body.dark-theme .nav-button.secondary {
  background-color: #3d3d3d;
}

body.dark-theme .nav-button.secondary:hover {
  background-color: #4d4d4d;
}

body.dark-theme .features-preview h2 {
  color: #6db3f2;
  border-bottom-color: #555;
}

body.dark-theme .feature-card {
  background-color: #3d3d3d;
  border-left-color: #6db3f2;
  color: #e0e0e0;
}

body.dark-theme .feature-card h4 {
  color: #6db3f2;
}

body.dark-theme .feature-card p {
  color: #b0b0b0;
}

body.dark-theme .bottom-toolbar {
  background-color: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
}

body.dark-theme .footer-link {
  color: #b0b0b0;
}

body.dark-theme .footer-link:hover {
  color: #6db3f2;
  background-color: #333;
}

body.dark-theme .copyright-text {
  color: #888;
}

body.dark-theme .modal-content {
  background-color: #2d2d2d;
  color: #e0e0e0;
}

body.dark-theme .modal-close-btn {
  color: #b0b0b0;
}

body.dark-theme .modal-close-btn:hover {
  color: #e0e0e0;
}

/* Dark theme settings modal styles */
body.dark-theme .settings-section h3 {
  color: #6db3f2;
}

body.dark-theme .setting-item select,
body.dark-theme .setting-item input[type="text"],
body.dark-theme .setting-item input[type="file"] {
  background-color: #3a3a3a;
  color: #e0e0e0;
  border-color: #555;
}

body.dark-theme .modal-button.secondary {
  background-color: #3a3a3a;
  color: #e0e0e0;
}

body.dark-theme .modal-button.secondary:hover {
  background-color: #4a4a4a;
}

body.dark-theme .coming-soon-content h2 {
  color: #6db3f2;
}

body.dark-theme .coming-soon-content p {
  color: #b0b0b0;
}

body.dark-theme .feedback-btn {
  background-color: #6db3f2;
  color: #1a1a1a;
}

body.dark-theme .feedback-btn:hover {
  background-color: #5a9fd9;
}
