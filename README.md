# 🎨 Pixel Art Nexus

A professional pixel art editor with layers, multiple canvases, and advanced drawing tools. Built with modern web technologies for optimal performance and security.

## ✨ Features

### 🎯 Core Features
- **Multi-layer Support**: Create complex artwork with unlimited layers
- **Multiple Canvases**: Work on multiple projects simultaneously
- **Advanced Drawing Tools**: Brushes, shapes, fill tools, and more
- **Professional Brushes**: Normal, circular, calligraphy, spray paint, glow, anti-alias, taper
- **Mirror Drawing**: Vertical, horizontal, and radial symmetry options
- **Selection Tools**: Copy, paste, transform, and manipulate selections
- **Undo/Redo System**: Comprehensive history with memory management

### 🔒 Security Features
- **Input Validation**: Comprehensive validation for all user inputs
- **DoS Protection**: Rate limiting and resource management
- **Secure Storage**: Encrypted local storage with fallback mechanisms
- **XSS Prevention**: HTML sanitization and CSP headers
- **Memory Management**: Secure undo/redo with memory limits

### 📱 Modern Web Features
- **Progressive Web App (PWA)**: Install and use offline
- **Service Worker**: Offline functionality and caching
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Error Handling**: Comprehensive error tracking and reporting
- **Performance Optimized**: Efficient rendering and memory usage

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn

### Installation
```bash
# Clone the repository
git clone https://github.com/your-username/pixelartnexus.git
cd pixelartnexus

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env
# Edit .env with your configuration
```

### Development
```bash
# Start development server
npm run dev
```

This serves the source files directly at `http://localhost:8080` without obfuscation.

### Production Build
```bash
# Complete production build with obfuscation
npm run build:prod

# Or build without obfuscation
npm run build:no-obfuscate

# Serve the built files
npm run serve
```

## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server (serves source files directly) |
| `npm run build` | Build for production (copies files to dist/) |
| `npm run build:prod` | Complete production build (build + obfuscate) |
| `npm run build:safe` | Production build with safe obfuscation |
| `npm run build:no-obfuscate` | Production build without obfuscation |
| `npm run obfuscate` | Obfuscate JavaScript in dist/ |
| `npm run obfuscate:safe` | Safe obfuscation (preserves debugging) |
| `npm run serve` | Serve the built files from dist/ folder |
| `npm run clean` | Clean the dist/ folder |
| `npm run generate-config` | Generate runtime configuration |

## 🏗️ Architecture

### File Structure
```
pixelartnexus/
├── canvas.html          # Main editor interface
├── canvas.js            # Core editor functionality
├── canvas.css           # Editor styles
├── index.html           # Landing page
├── index.js             # Landing page functionality
├── security-utils.js    # Security utilities and validation
├── error-handler.js     # Error handling and logging
├── shared-layout.js     # Shared functionality
├── manifest.json        # PWA manifest
├── sw.js               # Service worker
├── .htaccess           # Security headers
└── scripts/
    └── generate-config.js # Build configuration generator
```

### Security Architecture
- **Input Validation Layer**: All user inputs validated through SecurityUtils
- **Resource Management**: Memory and resource limits to prevent DoS
- **Secure Storage**: Encrypted localStorage with integrity checks
- **Error Handling**: Comprehensive error tracking and reporting
- **CSP Headers**: Content Security Policy for XSS prevention

## 🔧 Configuration

### Environment Variables
Create a `.env` file based on `.env.example`:

```env
# Auth0 Configuration
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_CLIENT_ID=your-auth0-client-id

# Xano Configuration  
XANO_BASE_URL=https://your-xano-instance.xano.io/api:your-api-key
```

### Security Configuration
Security settings can be adjusted in `security-utils.js`:

```javascript
const SECURITY_CONFIG = {
    MAX_CANVAS_SIZE: 1024,
    MAX_LAYERS: 20,
    MAX_CANVASES: 3,
    MAX_UNDO_STEPS: 100,
    // ... other settings
};
```

## 🔒 Security Features

### Input Validation
- Username validation (length, characters)
- Color validation (hex, RGB, RGBA)
- Numeric range validation
- File size and type validation

### DoS Protection
- Rate limiting for API requests
- Memory usage monitoring
- Resource count limits
- Request timeout handling

### Data Protection
- Secure localStorage encryption
- XSS prevention through sanitization
- CSRF protection
- Content Security Policy headers

## 📱 PWA Features

### Offline Support
- Service worker caching
- Offline functionality
- Background sync for data saving

### Installation
- Add to home screen on mobile
- Desktop app installation
- Native app-like experience

## 🐛 Error Handling

### Global Error Handling
- Uncaught JavaScript errors
- Unhandled promise rejections
- Resource loading failures
- User action logging

### Error Reporting
- Local error storage
- Performance metrics
- User action tracking
- Development debugging

## 🚀 Deployment

### Build Process
1. **Clean**: Remove old build files
2. **Generate Config**: Create runtime configuration
3. **Copy Assets**: Copy all static files
4. **Copy JavaScript**: Copy main JavaScript files
5. **Obfuscate**: Protect JavaScript code (optional)

### Production Deployment
```bash
# Build for production
npm run build:prod

# Deploy the dist/ folder contents
# The dist/ folder contains all necessary files:
# - HTML, CSS, and JavaScript files
# - Images and assets
# - PWA files (manifest.json, sw.js)
# - Security files (.htaccess)
```

### Security Headers
The `.htaccess` file includes:
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options
- Strict Transport Security
- Cache control headers

## 🔧 Development

### Code Structure
- **Modular Design**: Separate utilities for security, errors, etc.
- **Progressive Enhancement**: Works without JavaScript (basic functionality)
- **Responsive Design**: Mobile-first approach
- **Accessibility**: ARIA labels and keyboard navigation

### Performance Optimization
- **Lazy Loading**: Load resources as needed
- **Efficient Rendering**: Optimized canvas operations
- **Memory Management**: Automatic cleanup and limits
- **Caching Strategy**: Service worker caching

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

- **Documentation**: Check the help section in the app
- **Issues**: Report bugs on GitHub
- **Feedback**: Use the feedback form in the app
- **Community**: Join our Discord server (coming soon)

---

**Made with ❤️ for the pixel art community**
