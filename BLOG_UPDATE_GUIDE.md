# Blog Update Guide

## How to Add New Blog Posts

To add a new blog post to the PixelArtNexus blog, follow these simple steps:

### Step 1: Open blog.html
Open the `blog.html` file in your editor.

### Step 2: Find the Blog Posts Container
Look for this section in the file:
```html
<!-- Blog Posts Container -->
<div class="blog-posts-container">
```

### Step 3: Add Your New Post
**IMPORTANT**: Always add new blog posts at the TOP of the container (right after the opening `<div class="blog-posts-container">` tag) so they appear above older posts.

Copy this template and paste it at the top:

```html
<!-- Blog Post [NUMBER] (Most Recent) -->
<div class="blog-post">
  <h2 class="blog-title">Your Blog Post Title Here</h2>
  <p class="blog-date">Month Day, Year</p>
  <div class="blog-content">
    <p>Your first paragraph goes here...</p>
    <p>Add more paragraphs as needed...</p>
    
    <!-- For lists, use this format: -->
    <ul>
      <li>List item 1</li>
      <li>List item 2</li>
      <li>List item 3</li>
    </ul>
    
    <!-- For links, use this format: -->
    <p>Check out our <a href="/canvas">pixel art editor</a> for more features!</p>
  </div>
</div>
```

### Step 4: Fill in Your Content
- Replace "Your Blog Post Title Here" with your actual title
- Update the date in "Month Day, Year" format (e.g., "December 15, 2025")
- Add your blog content in the `<div class="blog-content">` section
- Use `<p>` tags for paragraphs
- Use `<ul>` and `<li>` for bullet lists
- Use `<a href="/page">link text</a>` for internal links

### Step 5: Update the Comment
Change the comment from:
```html
<!-- Blog Post 1 (Most Recent) -->
```
to:
```html
<!-- Blog Post 2 -->
```
for the previous post, and use "Blog Post 1 (Most Recent)" for your new post.

### Example: Adding a New Post

If you want to add a post about "New Animation Features", here's how it would look:

```html
<!-- Blog Posts Container -->
<div class="blog-posts-container">
  
  <!-- Blog Post 1 (Most Recent) -->
  <div class="blog-post">
    <h2 class="blog-title">New Animation Features Coming Soon!</h2>
    <p class="blog-date">December 20, 2025</p>
    <div class="blog-content">
      <p>I'm excited to announce that animation features are coming to PixelArtNexus!</p>
      <p>Here's what you can expect:</p>
      <ul>
        <li>Frame-by-frame animation tools</li>
        <li>Onion skinning for smooth transitions</li>
        <li>Export to GIF and MP4 formats</li>
        <li>Timeline controls with play/pause</li>
      </ul>
      <p>Stay tuned for the beta release coming next month!</p>
    </div>
  </div>

  <!-- Blog Post 2 -->
  <div class="blog-post">
    <h2 class="blog-title">How I Made the Home Page Pixel by Pixel</h2>
    <!-- ... rest of the existing post ... -->
  </div>
  
  <!-- Continue with other existing posts... -->
```

### Tips for Great Blog Posts

1. **Keep titles engaging**: Use action words and be specific
2. **Date format**: Always use "Month Day, Year" (e.g., "January 15, 2025")
3. **Paragraph length**: Keep paragraphs 2-4 sentences for easy reading
4. **Use lists**: Break up long content with bullet points
5. **Add links**: Link to relevant pages like `/canvas`, `/about`, etc.
6. **Images**: If you want to add images, save them in the root directory and use:
   ```html
   <img src="blog-image-name.png" alt="Description" style="max-width: 100%; border-radius: 8px; margin: 16px 0;">
   ```

### The End Message
The "That's all the blog posts so far..." message will automatically appear at the bottom after all your posts, so you don't need to worry about it!

### Testing Your Changes
After adding a new post:
1. Save the `blog.html` file
2. Open `http://localhost:8080/blog.html` in your browser
3. Check that your new post appears at the top
4. Verify the formatting looks good
5. Test any links you added

That's it! Your new blog post will now be live on the site.
