<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Terms of Service | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3586346747453362"
       crossorigin="anonymous"></script>
  <style>
    .terms-section {
      margin: 25px 0;
    }

    .terms-section h2 {
      color: #2c5aa0;
      margin-bottom: 15px;
      font-size: 1.4em;
      border-bottom: 2px solid #e0e0e0;
      padding-bottom: 8px;
    }

    .terms-section h3 {
      color: #2c5aa0;
      margin: 20px 0 10px 0;
      font-size: 1.1em;
    }

    .terms-section p {
      margin: 12px 0;
      line-height: 1.6;
    }

    .terms-section ul {
      margin: 12px 0;
      padding-left: 25px;
    }

    .terms-section li {
      margin: 8px 0;
      line-height: 1.5;
    }

    .effective-date {
      background-color: #f0f8ff;
      padding: 15px;
      border-left: 4px solid #2c5aa0;
      margin: 20px 0;
      border-radius: 4px;
    }

    .important-notice {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }

    .contact-info {
      background-color: #e8f5e8;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }
    
    /* Zero-tolerance notice styling */
    .zero-tolerance-notice {
      background-color: #f44336;
      border: 2px solid #d32f2f;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
      position: relative;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .zero-tolerance-notice:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(244, 67, 54, 0.5);
    }
    
    .zero-tolerance-notice h3 {
      color: white !important;
      margin-top: 0;
      font-size: 1.4em !important;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid rgba(255, 255, 255, 0.3);
      padding-bottom: 10px;
    }
    
    .zero-tolerance-notice p, 
    .zero-tolerance-notice ul, 
    .zero-tolerance-notice li {
      color: white;
      font-weight: 500;
    }
    
    .zero-tolerance-notice p strong {
      font-weight: 700;
      font-size: 1.1em;
    }
    
    .zero-tolerance-notice ul {
      background-color: rgba(0, 0, 0, 0.2);
      padding: 15px 15px 15px 35px;
      border-radius: 6px;
    }
    
    .zero-tolerance-notice li {
      margin: 10px 0;
    }

    /* Dark theme styles */
    body.dark-theme .terms-section h2 {
      color: #6db3f2;
      border-bottom-color: #555;
    }

    body.dark-theme .terms-section h3 {
      color: #6db3f2;
    }

    body.dark-theme .effective-date {
      background-color: #1a2332;
      border-left-color: #6db3f2;
    }

    body.dark-theme .important-notice {
      background-color: #332a1a;
      border-color: #b8860b;
      color: #e0e0e0;
    }

    body.dark-theme .contact-info {
      background-color: #1a2e1a;
      color: #e0e0e0;
    }
    
    body.dark-theme .zero-tolerance-notice {
      background-color: #b71c1c;
      border-color: #7f0000;
    }
    
    body.dark-theme .zero-tolerance-notice ul {
      background-color: rgba(0, 0, 0, 0.4);
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArtNexus" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- leftbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="6832096065"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>

    <div id="mainContainer">
      <div class="header">
        <img src="title.webp" alt="PixelArtNexus" class="logo">
        <h1>Terms of Service</h1>
      </div>

      <div class="effective-date">
        <strong>Effective Date:</strong> January 9, 2025<br>
        <strong>Last Updated:</strong> January 16, 2025
      </div>

      <div class="terms-section">
        <h2>1. Introduction</h2>
        <p>Welcome to PixelArtNexus, a professional pixel art creation platform that provides web-based tools for creating, editing, and sharing pixel art. These Terms of Service ("Terms") govern your use of our website and services.</p>
        
        <p><strong>By creating an account or using PixelArtNexus, you agree to be bound by these Terms of Service and our Community Guidelines.</strong></p>
        
        <p>Please also review our <a href="/privacy" class="footer-link">Privacy Policy</a> and <a href="/community-guidelines" class="footer-link">Community Guidelines</a>, which are incorporated by reference into these Terms.</p>
      </div>

      <div class="terms-section">
        <h2>2. Eligibility</h2>
        <p>To use PixelArtNexus, you must:</p>
        <ul>
          <li>Be at least 12 years of age</li>
          <li>Have the legal authority to accept these terms</li>
          <li>Not be prohibited from using our services under applicable law</li>
          <li>Provide accurate information during registration</li>
        </ul>
        <p>If you are under 18, you represent that you have your parent's or guardian's permission to use our services.</p>
      </div>

      <div class="terms-section">
        <h2>3. Account Registration</h2>
        <p>To access certain features, you must create an account through our Auth0 authentication system. Account registration requires:</p>
        <ul>
          <li>A valid email address for identification and communication</li>
          <li>Authentication through Auth0 (Google, email/password, or other supported methods)</li>
          <li>Email verification for full account access</li>
        </ul>
        <p>You are responsible for:</p>
        <ul>
          <li>Maintaining the security of your account credentials</li>
          <li>All activities that occur under your account</li>
          <li>Notifying us immediately of any unauthorized use</li>
        </ul>
      </div>

      <div class="terms-section">
        <h2>4. User Content & Artwork</h2>
        <h3>Ownership</h3>
        <p>You retain full ownership of all artwork and content you create using PixelArtNexus.</p>
        
        <h3>License to PixelArtNexus</h3>
        <p>By posting or sharing content on our platform, you grant PixelArtNexus a non-exclusive, worldwide, royalty-free license to:</p>
        <ul>
          <li>Display your artwork within our platform</li>
          <li>Create thumbnails and previews for gallery features</li>
          <li>Distribute your content through our services (feeds, galleries, etc.)</li>
          <li>Use your content for promotional purposes with your consent</li>
        </ul>
        
        <h3>Content Removal</h3>
        <p>We reserve the right to remove any content that violates these Terms, our Community Guidelines, or applicable laws.</p>
      </div>

      <div class="terms-section">
        <h2>5. Community Guidelines</h2>
        <p>By using PixelArtNexus, you automatically agree to follow our Community Guidelines. Violations may result in:</p>
        <ul>
          <li>Content removal</li>
          <li>Account warnings</li>
          <li>Temporary suspension</li>
          <li>Permanent account termination</li>
        </ul>
        <p>Our Community Guidelines cover prohibited content, expected behavior, and the moderation appeals process.</p>
      </div>

      <div class="terms-section">
        <h2>6. Prohibited Conduct</h2>
        <p>You must not:</p>
        <ul>
          <li>Upload illegal, copyrighted, or infringing material without permission</li>
          <li>Impersonate others or provide false information</li>
          <li>Engage in harassment, hate speech, or discriminatory behavior</li>
          <li>Spam, hack, or interfere with our services or other users</li>
          <li>Attempt to bypass moderation tools or security measures</li>
          <li>Create multiple accounts to evade bans or restrictions</li>
          <li>Use automated tools to scrape or download content without permission</li>
          <li>Upload explicit, violent, or inappropriate content</li>
          <li><strong>Engage in any form of grooming, predatory behavior, or content that sexualizes minors</strong></li>
        </ul>
        
        <div class="zero-tolerance-notice">
          <h3>Zero Tolerance for Grooming and Predatory Behavior</h3>
          <p>PixelArtNexus maintains a <strong>zero-tolerance policy</strong> regarding grooming, pedophilia, and any predatory behavior targeting minors. This is not just a rule—it's our unwavering commitment to safety.</p>
          <p>Any content, communication, or behavior associated with grooming or pedophilia will be:</p>
          <ul>
            <li>Immediately removed from our platform</li>
            <li>Thoroughly investigated using all available resources</li>
            <li>Reported to appropriate law enforcement agencies</li>
            <li>Subject to permanent account termination without warning</li>
          </ul>
          <p>The safety of our community, especially younger users, is our highest priority. We take this responsibility personally and will use every tool at our disposal to protect our users and bring offenders to justice.</p>
        </div>
      </div>

      <div class="terms-section">
        <h2>7. Moderation and Enforcement</h2>
        <p>PixelArtNexus reserves the right (but not the obligation) to:</p>
        <ul>
          <li>Monitor and moderate all user content</li>
          <li>Remove content that violates our policies</li>
          <li>Suspend or terminate accounts for violations</li>
          <li>Investigate reports of misconduct</li>
          <li>Cooperate with law enforcement for serious violations, especially those involving the safety of minors</li>
          <li>Preserve and provide evidence of violations to appropriate authorities</li>
        </ul>
        <p>While most policy violations follow a graduated response system, certain serious violations—particularly those related to child safety, grooming, or predatory behavior—will result in immediate, permanent account termination and may be reported to authorities without prior notice.</p>
        <p>Users may appeal moderation decisions by contacting us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
      </div>

      <div class="terms-section">
        <h2>8. Privacy and Data Collection</h2>
        <p>We collect and use information as described in our Privacy Policy, including:</p>
        <ul>
          <li>Email addresses for user identification and moderation</li>
          <li>Usage data for analytics and performance improvement</li>
          <li>Artwork and content you create or upload</li>
          <li>Account preferences and settings</li>
        </ul>
        <p>For detailed information about our data practices, please review our <a href="/privacy" class="footer-link">Privacy Policy</a>.</p>
      </div>

      <div class="terms-section">
        <h2>9. Service Availability and Changes</h2>
        <p>PixelArtNexus may:</p>
        <ul>
          <li>Modify, suspend, or discontinue services at any time without notice</li>
          <li>Experience downtime, bugs, or technical issues</li>
          <li>Update these Terms of Service as needed</li>
        </ul>
        <p>We do not guarantee 100% uptime or uninterrupted service. Continued use of our services after changes constitutes acceptance of updated terms.</p>
      </div>

      <div class="terms-section">
        <h2>10. Intellectual Property</h2>
        <p>PixelArtNexus owns all rights to:</p>
        <ul>
          <li>Our logo, branding, and design elements</li>
          <li>The platform's code and functionality</li>
          <li>Our trademarks and service marks</li>
        </ul>
        <p>You may not reproduce, distribute, or create derivative works of our intellectual property without written permission.</p>
      </div>

      <div class="important-notice">
        <h2>11. Disclaimers and Limitation of Liability</h2>
        <p><strong>IMPORTANT:</strong> PixelArtNexus is provided "as is" without warranties of any kind. You use our services at your own risk.</p>
        <p>We are not liable for:</p>
        <ul>
          <li>Damages resulting from user content or interactions</li>
          <li>Service outages or data loss</li>
          <li>Misuse of our platform by other users</li>
          <li>Any indirect, incidental, or consequential damages</li>
        </ul>
        <p>Our total liability is limited to the amount you paid for our services (if any) in the 12 months preceding the claim.</p>
      </div>

      <div class="terms-section">
        <h2>12. Governing Law</h2>
        <p>These Terms are governed by the laws of the United States. Any disputes will be resolved in the appropriate courts within the United States.</p>
      </div>

      <div class="terms-section">
        <h2>13. Account Termination</h2>
        <p>Either party may terminate your account:</p>
        <ul>
          <li><strong>You may:</strong> Delete your account at any time through your account settings</li>
          <li><strong>We may:</strong> Suspend or terminate your account for violations of these Terms</li>
        </ul>
        <p>Upon termination, your right to use our services ceases immediately. We may retain certain information as required by law or for legitimate business purposes.</p>
      </div>

      <div class="contact-info">
        <h2>14. Contact Information</h2>
        <p>For questions about these Terms of Service, please contact us:</p>
        <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <p><strong>Website:</strong> <a href="https://pixelartnexus.com">pixelartnexus.com</a></p>
      </div>

      <div class="terms-section">
        <p><em>Thank you for being part of the PixelArtNexus community. We're excited to see what amazing pixel art you'll create!</em></p>
      </div>

      <!-- Footer -->
      <div class="bottom-toolbar">
        <div class="footer-section core-info">
          <a href="/about" class="footer-link">About</a>
          <a href="/contact" class="footer-link">Contact</a>
          <a href="https://docs.google.com/forms/d/e/1FAIpQLSdzeaR8oDUKpDuSFuKWGfuN9B7LC0Ti_KU0LUw3_tfQgwW-wg/viewform?usp=dialog" target="_blank" class="footer-link">Feedback</a>
          <a href="/help" class="footer-link">Help / FAQ</a>
        </div>
        <div class="footer-section legal-policy">
          <a href="/terms" class="footer-link">Terms of Service</a>
          <a href="/privacy" class="footer-link">Privacy Policy</a>
        </div>
        <div class="footer-section community-social">
          <a href="/community-guidelines" class="footer-link">Community Guidelines</a>
          <a href="/social" class="footer-link">Social Media</a>
          <a href="/blog" class="footer-link">Blog / Updates</a>
        </div>
        <div class="footer-section copyright">
          <span class="copyright-text">© 2025 PixelArtNexus. All rights reserved.</span>
        </div>
      </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Login button (shown when not authenticated) -->
        <button id="authButton" class="auth-button">Login</button>

        <!-- Settings button (shown when not authenticated) -->
        <button id="settingsButton" class="settings-button" style="display: none;">Settings</button>

        <!-- Profile dropdown (shown when authenticated and verified) -->
        <div id="profileDropdown" class="profile-dropdown" style="display: none;">
          <div id="profileAvatar" class="profile-avatar">
            <img id="profileImage" src="" alt="Profile" class="profile-image">
          </div>
          <div id="profileMenu" class="profile-menu" style="display: none;">
            <div class="profile-menu-item" id="userSettingsMenuItem">User Settings</div>
            <div class="profile-menu-item" id="savedArtMenuItem">Saved Art</div>
            <div class="profile-menu-item" id="logoutMenuItem">Logout</div>
          </div>
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
      <div class="banner-ad-container">
        <!-- rightbanner -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-3586346747453362"
             data-ad-slot="4302049929"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
      <button type="button" class="modal-close-btn" aria-label="Close">&times;</button>
      <h2>Settings</h2>

      <!-- Theme Settings -->
      <div class="settings-section">
        <h3>Appearance</h3>
        <div class="setting-item">
          <div>
            <label for="themeSelect">Theme:</label>
            <select id="themeSelect">
              <option value="light">Light Mode</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>
      </div>

      <!-- User Profile Settings (shown only for authenticated users) -->
      <div id="userProfileSection" class="settings-section" style="display: none;">
        <h3>Profile</h3>
        <div class="setting-item">
          <div>
            <label for="usernameInput">Username:</label>
            <input type="text" id="usernameInput" placeholder="Enter username" maxlength="50">
          </div>
        </div>
        <div class="setting-item">
          <div>
            <label for="profileImageInput">Profile Picture:</label>
            <input type="file" id="profileImageInput" accept="image/png,image/jpg,image/jpeg">
            <img id="profileImagePreview" style="display: none; width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
          </div>
        </div>
      </div>

      <!-- Modal Buttons -->
      <div class="modal-buttons">
        <button type="button" id="cancelSettingsBtn" class="modal-button secondary">Cancel</button>
        <button type="button" id="saveSettingsBtn" class="modal-button primary">Save Settings</button>
      </div>
    </div>
  </div>

  <script src="shared-layout.js"></script>
</body>
</html>
