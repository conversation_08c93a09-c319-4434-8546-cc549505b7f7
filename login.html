<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login | PixelArtNexus</title>
  <link rel="icon" href="/logo.png" type="image/png">
  <link rel="stylesheet" href="shared-layout.css">
  <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
  <style>
    .main-content {
      flex: 0 0 auto;
      width: 900px;
      max-width: 900px;
      background-color: white;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      border-radius: 8px;
      margin: 40px 0;
      padding: 20px;
      position: relative;
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
    }

    .login-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 60vh;
      text-align: center;
      padding: 40px 20px;
    }

    .login-box {
      background: white;
      border: 2px solid #e0e0e0;
      border-radius: 12px;
      padding: 40px;
      max-width: 400px;
      width: 100%;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .login-title {
      color: #2c5aa0;
      font-size: 28px;
      margin-bottom: 16px;
      font-weight: bold;
    }

    .login-subtitle {
      color: #666;
      font-size: 16px;
      margin-bottom: 32px;
      line-height: 1.5;
    }

    .login-button {
      background: #2c5aa0;
      color: white;
      border: none;
      padding: 14px 32px;
      font-size: 16px;
      font-weight: bold;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      width: 100%;
      margin-bottom: 16px;
    }

    .login-button:hover {
      background: #1e3a70;
      transform: translateY(-1px);
    }

    .login-button:disabled {
      background: #999;
      cursor: not-allowed;
      transform: none;
    }

    .back-link {
      color: white;
      text-decoration: underline;
      font-size: 14px;
      margin-top: 20px;
      display: inline-block;
      transition: color 0.2s ease;
    }

    .back-link:hover {
      color: #f0f0f0;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
      margin-top: 16px;
    }

    /* Dark theme styles */
    body.dark-theme .main-content {
      background-color: #2d2d2d;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      color: #e0e0e0;
    }

    body.dark-theme .login-box {
      background-color: #3d3d3d;
      border-color: #666;
    }

    body.dark-theme .login-title {
      color: #6db3f2;
    }

    body.dark-theme .login-subtitle,
    body.dark-theme .loading-text {
      color: #ccc;
    }

    body.dark-theme .login-button {
      background: #6db3f2;
      color: #1a1a1a;
    }

    body.dark-theme .login-button:hover {
      background: #5a9fd9;
    }

    body.dark-theme .login-button:disabled {
      background: #666;
      color: #999;
    }

    body.dark-theme .back-link {
      color: #ccc;
    }

    body.dark-theme .back-link:hover {
      color: #fff;
    }
  </style>
</head>
<body>
  <div id="mobileHeader" style="display: none;">
    <!-- This will be shown on mobile only via JavaScript -->
    <div class="title-container">
      <a href="/">
        <img src="title.webp" alt="PixelArtNexus" class="title-image">
      </a>
    </div>
    <!-- The login/settings buttons will be positioned here on mobile via JavaScript -->
  </div>
  
  <div id="fullWidthWrapper">
    <div id="leftBanner" class="banner-div">
      <div class="left-toolbar">
        <div class="title-container">
          <img src="title.webp" alt="PixelArt Nexus Title" class="title-image">
        </div>
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
    </div>

    <div class="main-content">
      <div class="login-container">
        <div class="login-box">
          <h1 class="login-title">Welcome Back!</h1>
          <p class="login-subtitle">Sign in to access your pixel art creations and sync your settings across devices.</p>

          <button id="loginButton" class="login-button">
            Sign In with Auth0
          </button>

          <div id="loadingText" class="loading-text" style="display: none;">
            Redirecting to login...
          </div>

          <a href="/" class="back-link">← Back to Home</a>
        </div>
      </div>
    </div>

    <div id="rightBanner" class="banner-div">
      <div class="right-toolbar">
        <!-- Empty for now, could add additional auth options later -->
      </div>
      <div class="banner-image-container">
        <img src="banner.png" alt="Banner" class="banner-image">
      </div>
    </div>
  </div>

  <script>
    // Handle login button click - redirect to Auth0 with main page as callback
    async function handleLoginPageClick() {
      // Wait for shared-layout.js to initialize Auth0
      if (!auth0Client) {
        console.log('Waiting for Auth0 to initialize...');
        setTimeout(handleLoginPageClick, 100);
        return;
      }

      try {
        const loginButton = document.getElementById('loginButton');
        const loadingText = document.getElementById('loadingText');

        loginButton.disabled = true;
        loginButton.textContent = 'Signing In...';
        loadingText.style.display = 'block';

        console.log('Starting Auth0 login from login page, redirecting to main page...');

        // Use main page as redirect URI so it handles the callback properly
        await auth0Client.loginWithRedirect({
          authorizationParams: {
            redirect_uri: window.location.origin
          }
        });

      } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed. Please try again.', 'error');

        // Reset button state
        const loginButton = document.getElementById('loginButton');
        const loadingText = document.getElementById('loadingText');
        loginButton.disabled = false;
        loginButton.textContent = 'Sign In with Auth0';
        loadingText.style.display = 'none';
      }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
      // Check if we're already authenticated and redirect to home
      if (auth0Client) {
        auth0Client.isAuthenticated().then(isAuth => {
          if (isAuth) {
            console.log('User already authenticated, redirecting to home...');
            window.location.href = '/';
            return;
          }
        }).catch(err => {
          console.log('Auth check error:', err);
        });
      }

      // Login button click handler
      document.getElementById('loginButton').addEventListener('click', handleLoginPageClick);
    });
  </script>

  <!-- Use shared layout JS for consistent theming and auth -->
  <script src="shared-layout.js"></script>
</body>
</html>
